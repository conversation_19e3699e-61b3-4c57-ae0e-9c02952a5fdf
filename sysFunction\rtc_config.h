#ifndef RTC_CONFIG_H
#define RTC_CONFIG_H

#include "sys_common.h"

// RTC配置状态
typedef enum {
    RTC_CONFIG_OK = 0,
    RTC_CONFIG_ERROR,
    RTC_CONFIG_INVALID_FORMAT,
    RTC_CONFIG_TIMEOUT
} rtc_config_status_t;

// 函数声明
sys_func_status_t rtc_config_set_time(const char* time_string);
sys_func_status_t rtc_config_get_current_time(datetime_t* current_time);
sys_func_status_t rtc_config_get_current_time_string(char* time_buffer, size_t buffer_size);
void rtc_config_print_current_time(void);
void rtc_config_print_set_success(void);
void rtc_config_print_set_failed(void);
sys_func_status_t rtc_config_parse_time_string(const char* time_string, datetime_t* time);

// 命令处理函数
void rtc_config_handle_config_command(void);
void rtc_config_handle_now_command(void);
void rtc_config_handle_time_input(const char* input);

#endif // RTC_CONFIG_H
