#include "oled_app.h"

// PID ??????��?????????
#define PID_PARAM_P_MIN 0    // P ??????��? (0.00)
#define PID_PARAM_P_MAX 1000 // P ???????? (10.00)
#define PID_PARAM_P_STEP 10  // P ????????? (0.10)

#define PID_PARAM_I_MIN 0    // I ??????��? (0.00)
#define PID_PARAM_I_MAX 1000 // I ???????? (10.00)
#define PID_PARAM_I_STEP 10  // I ????????? (0.10)

#define PID_PARAM_D_MIN 0   // D ??????��? (0.00)
#define PID_PARAM_D_MAX 100 // D ???????? (1.00)
#define PID_PARAM_D_STEP 1  // D ????????? (0.01)

/**
 * @brief	???????printf?????????????????6x8??��??ASCII???
 * @param x  Character position on the X-axis  range??0 - 127
 * @param y  Character position on the Y-axis  range??0 - 3
 * ???��oled_printf(0, 0, "Data = %d", dat);
 **/
int oled_printf(uint8_t x, uint8_t y, const char *format, ...)
{
  char buffer[512]; // ????��?????????????
  va_list arg;      // ??????????
  int len;          // ?????????????

  va_start(arg, format);
  // ???????????????? buffer
  len = vsnprintf(buffer, sizeof(buffer), format, arg);
  va_end(arg);

  OLED_ShowStr(x, y, buffer, 8);
  return len;
}

// u8g2 ?? GPIO ????????????
uint8_t u8g2_gpio_and_delay_stm32(u8x8_t *u8x8, uint8_t msg, uint8_t arg_int, void *arg_ptr)
{
  switch (msg)
  {
  case U8X8_MSG_GPIO_AND_DELAY_INIT:
    // ????? GPIO (???????????? SPI ?? CS, DC, RST ????)
    // ??????? I2C??????????????????
    break;
  case U8X8_MSG_DELAY_MILLI:
    // ???: u8g2 ????��?????????????????????
    // ????????????????? HAL ??????
    HAL_Delay(arg_int);
    break;
  case U8X8_MSG_DELAY_10MICRO:
    // ???10?????????????��???????
    {
      // GD32??????????????120-200MHz??????????????3-4?????????
      // ??160MHz????10??s????400-500?????
      for (volatile uint32_t i = 0; i < 480; i++)
      {
        __NOP(); // ?????????????????????
      }
    }
    break;
  case U8X8_MSG_DELAY_100NANO:
    // ???100??????????????NOP???
    // ???NOP????????1?????????(?6ns@160MHz)
    __NOP();
    __NOP();
    __NOP();
    __NOP();
    __NOP();
    __NOP();
    __NOP();
    __NOP();
    __NOP();
    __NOP();
    __NOP();
    __NOP();
    __NOP();
    __NOP();
    __NOP();
    __NOP();
    break;
  case U8X8_MSG_GPIO_I2C_CLOCK: // [[fallthrough]] // Fallthrough ???????????
  case U8X8_MSG_GPIO_I2C_DATA:
    // ???? SCL/SDA ??????????��????**??????? I2C** ????????
    // ?????? I2C ?????��????????????? HAL ??????
    break;
  // --- ?????? GPIO ???????????????????????? SPI ???? ---
  // ?????? u8g2 ?????????????????? SPI ???? (CS, DC, Reset)??
  // ?????????????? msg ??????/???????? GPIO ????????
  // ??????????? I2C ??????????????????????????????????
  case U8X8_MSG_GPIO_CS:
    // SPI ??????
    break;
  case U8X8_MSG_GPIO_DC:
    // SPI ????/?????????
    break;
  case U8X8_MSG_GPIO_RESET:
    // ???????��???????
    break;
  case U8X8_MSG_GPIO_MENU_SELECT:
    u8x8_SetGPIOResult(u8x8, /* ??????? GPIO ?? */ 0);
    break;
  default:
    u8x8_SetGPIOResult(u8x8, 1); // ?????????
    break;
  }
  return 1;
}

// u8g2 ????? I2C ?????????
uint8_t u8x8_byte_hw_i2c(u8x8_t *u8x8, uint8_t msg, uint8_t arg_int, void *arg_ptr)
{
  static uint8_t buffer[32]; // u8g2 ??��?????? 32 ???
  static uint8_t buf_idx;
  uint8_t *data;

  switch (msg)
  {
  case U8X8_MSG_BYTE_SEND:
    // ???: u8g2 ????????????????????????????��???
    // ????????????��?????? (arg_int ???) ?? u8g2 ?????????????????????
    // ???????????��??????��???? buffer ?��???? START/END_TRANSFER ????
    data = (uint8_t *)arg_ptr;
    while (arg_int > 0)
    {
      buffer[buf_idx++] = *data;
      data++;
      arg_int--;
    }
    break;
  case U8X8_MSG_BYTE_INIT:
    // ???: ???????????? I2C ???????????
    // ????? I2C (????? main ???????????)
    // ?????????? main ??????????????? MX_I2C1_Init()??????????????????
    break;
  case U8X8_MSG_BYTE_SET_DC:
    // ???: ?????????? SPI ????��??? Data/Command ????????
    // ????????/?????? (I2C ?????)
    // I2C ?????????????? (0x00 ?? 0x40) ????????????????????????? I2C ?????�^
    break;
  case U8X8_MSG_BYTE_START_TRANSFER:
    // ???: ?????? I2C ???????��?????
    buf_idx = 0;
    // ????????????????????????????????????????????�
    break;
  case U8X8_MSG_BYTE_END_TRANSFER:
    // ???: ?????? I2C ???????��??????
    // ????????? buffer ??????????????????????????�
    // ?????????? I2C ?????????????????
    // ??????????��?????
    // ???: u8x8_GetI2CAddress(u8x8) ??????? 7 ��??? * 2 = 8 ��???
    if (HAL_I2C_Master_Transmit(&hi2c1, u8x8_GetI2CAddress(u8x8), buffer, buf_idx, 100) != HAL_OK)
    {
      return 0; // ???????
    }
    break;
  default:
    return 0;
  }
  return 1;
}

/* ?????????? */
void OLED_SendBuff(uint8_t buff[4][128])
{
  // ??? u8g2 ??????????
  uint8_t *u8g2_buffer = u8g2_GetBufferPtr(&u8g2);

  // ??????????? u8g2 ???????
  memcpy(u8g2_buffer, buff, 4 * 128);

  // ???????????????? OLED
  u8g2_SendBuffer(&u8g2);
}

// ??????
TitlePage main_menu;         // ???????????????????
ListPage left_wheel_menu;    // ?????PID?????????
ListPage right_wheel_menu;   // ?????PID?????????
ValWin param_adjust_val_win; // ??? ValWin ???��???????

// ??????PID????????
typedef struct
{
  float p;
  float i;
  float d;
} PIDParams;

PIDParams left_wheel_pid = {1.0, 0.1, 0.01};
PIDParams right_wheel_pid = {1.0, 0.1, 0.01};

// ????????????????????
#define MAIN_MENU_NUM 2
Option main_menu_options[MAIN_MENU_NUM] = {
    {.text = (char *)"+ Left Wheel", .content = (char *)"Left"},
    {.text = (char *)"+ Right Wheel", .content = (char *)"Right"}};

// ???????????
Icon main_menu_icons[MAIN_MENU_NUM] = {
    [0] = {0xFC, 0xFE, 0xFF, 0x3F, 0x1F, 0x0F, 0x07, 0x03, 0x03, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
           0x01, 0x01, 0x01, 0x01, 0x01, 0x03, 0x07, 0x07, 0x0F, 0x1F, 0x3F, 0xFF, 0xFE, 0xFC, 0xFF, 0x01,
           0x00, 0x00, 0x00, 0x00, 0xFC, 0xFC, 0xFC, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFC, 0xFC, 0xFC,
           0x00, 0x00, 0xFC, 0xFC, 0xFC, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x03, 0xFF, 0xFF, 0xF0, 0xC0, 0x00,
           0x00, 0x00, 0x03, 0x07, 0x0F, 0x1F, 0x3E, 0x3C, 0x3C, 0x3C, 0x1E, 0x1F, 0x0F, 0x03, 0x00, 0x00,
           0x1F, 0x3F, 0x3F, 0x1F, 0x00, 0x00, 0x00, 0xC0, 0xF0, 0xFF, 0xCF, 0xDF, 0xFF, 0xFF, 0xFE, 0xFC,
           0xF8, 0xF8, 0xF0, 0xF0, 0xE0, 0xE0, 0xE0, 0xE0, 0xE0, 0xE0, 0xE0, 0xE0, 0xE0, 0xE0, 0xF0, 0xF0,
           0xF8, 0xF8, 0xFC, 0xFE, 0xFF, 0xFF, 0xDF, 0xCF}, // logo
    [1] = {0xFC, 0xFE, 0x7F, 0x3F, 0x1F, 0x0F, 0x07, 0x03, 0x83, 0x81, 0x01, 0x01, 0x81, 0xE1, 0xE1, 0xE1,
           0xE1, 0x81, 0x01, 0x81, 0x81, 0x83, 0x03, 0x07, 0x0F, 0x1F, 0x3F, 0x7F, 0xFE, 0xFC, 0xFF, 0x01,
           0x00, 0x00, 0x00, 0xE0, 0xE0, 0xF3, 0xFF, 0xFF, 0x3F, 0x0F, 0x07, 0x07, 0x03, 0x03, 0x07, 0x07,
           0x0F, 0x3F, 0xFF, 0xFF, 0xF7, 0xE0, 0xE0, 0x00, 0x00, 0x00, 0x01, 0xFF, 0xFF, 0xE0, 0x80, 0x00,
           0x00, 0x01, 0x01, 0x3B, 0x7F, 0x7F, 0x7F, 0x3C, 0x78, 0xF8, 0xF0, 0xF0, 0xF8, 0x78, 0x3C, 0x3F,
           0x7F, 0x7F, 0x33, 0x01, 0x01, 0x00, 0x00, 0x80, 0xE0, 0xFF, 0xCF, 0xDF, 0xFF, 0xFF, 0xFE, 0xFC,
           0xF8, 0xF0, 0xF0, 0xE0, 0xE0, 0xE0, 0xE0, 0xE1, 0xE1, 0xE1, 0xE1, 0xE0, 0xE0, 0xE0, 0xE0, 0xF0,
           0xF0, 0xF8, 0xFC, 0xFC, 0xFF, 0xFF, 0xDF, 0xCF} // Setting
};

// ????PID??????
Option left_wheel_options[] = {
    {.text = (char *)"- Left Wheel PID"},
    {.text = (char *)"$ P Parameter", .val = 0, .decimalNum = DecimalNum_2},
    {.text = (char *)"$ I Parameter", .val = 0, .decimalNum = DecimalNum_2},
    {.text = (char *)"$ D Parameter", .val = 0, .decimalNum = DecimalNum_2}};

// ????PID??????
Option right_wheel_options[] = {
    {.text = (char *)"- Right Wheel PID"},
    {.text = (char *)"$ P Parameter", .val = 0, .decimalNum = DecimalNum_2},
    {.text = (char *)"$ I Parameter", .val = 0, .decimalNum = DecimalNum_2},
    {.text = (char *)"$ D Parameter", .val = 0, .decimalNum = DecimalNum_2}};

// ????????????
bool MainMenu_CallBack(const Page *cur_page_addr, InputMsg msg)
{
  if (msg_click == msg)
  {
    Option *select_item = WouoUI_ListTitlePageGetSelectOpt(cur_page_addr);
    if (!strcmp(select_item->content, "Left"))
    {
      // ????????????????
      left_wheel_options[1].val = (int32_t)(left_wheel_pid.p * 100);
      left_wheel_options[2].val = (int32_t)(left_wheel_pid.i * 100);
      left_wheel_options[3].val = (int32_t)(left_wheel_pid.d * 100);
      WouoUI_JumpToPage((PageAddr)cur_page_addr, &left_wheel_menu);
    }
    else if (!strcmp(select_item->content, "Right"))
    {
      // ????????????????
      right_wheel_options[1].val = (int32_t)(right_wheel_pid.p * 100);
      right_wheel_options[2].val = (int32_t)(right_wheel_pid.i * 100);
      right_wheel_options[3].val = (int32_t)(right_wheel_pid.d * 100);
      WouoUI_JumpToPage((PageAddr)cur_page_addr, &right_wheel_menu);
    }
  }
  return false;
}

// ?????????????
bool LeftWheelMenu_CallBack(const Page *cur_page_addr, InputMsg msg)
{
  if (msg_click == msg)
  {
    Option *select_item = WouoUI_ListTitlePageGetSelectOpt(cur_page_addr);
    if (select_item->order >= 1 && select_item->order <= 3)
    {
      // ??????????????????��?��?????
      int min_val, max_val, step;
      switch (select_item->order)
      {
      case 1: // P Parameter
        min_val = PID_PARAM_P_MIN;
        max_val = PID_PARAM_P_MAX;
        step = PID_PARAM_P_STEP;
        break;
      case 2: // I Parameter
        min_val = PID_PARAM_I_MIN;
        max_val = PID_PARAM_I_MAX;
        step = PID_PARAM_I_STEP;
        break;
      case 3: // D Parameter
        min_val = PID_PARAM_D_MIN;
        max_val = PID_PARAM_D_MAX;
        step = PID_PARAM_D_STEP;
        break;
      default: // ????��???
        return false;
      }

      // ???? ValWin ?????????
      WouoUI_ValWinPageSetMinStepMax(&param_adjust_val_win, min_val, step, max_val);
      WouoUI_JumpToPage((PageAddr)cur_page_addr, &param_adjust_val_win);
    }
  }
  return false;
}

// ?????????????
bool RightWheelMenu_CallBack(const Page *cur_page_addr, InputMsg msg)
{
  if (msg_click == msg)
  {
    Option *select_item = WouoUI_ListTitlePageGetSelectOpt(cur_page_addr);
    if (select_item->order >= 1 && select_item->order <= 3)
    {
      // ??????????????????��?��?????
      int min_val, max_val, step;
      switch (select_item->order)
      {
      case 1: // P Parameter
        min_val = PID_PARAM_P_MIN;
        max_val = PID_PARAM_P_MAX;
        step = PID_PARAM_P_STEP;
        break;
      case 2: // I Parameter
        min_val = PID_PARAM_I_MIN;
        max_val = PID_PARAM_I_MAX;
        step = PID_PARAM_I_STEP;
        break;
      case 3: // D Parameter
        min_val = PID_PARAM_D_MIN;
        max_val = PID_PARAM_D_MAX;
        step = PID_PARAM_D_STEP;
        break;
      default: // ????��???
        return false;
      }

      // ???? ValWin ?????????
      WouoUI_ValWinPageSetMinStepMax(&param_adjust_val_win, min_val, step, max_val);
      WouoUI_JumpToPage((PageAddr)cur_page_addr, &param_adjust_val_win);
    }
  }
  return false;
}

// ??????????????????? (???????? ValWin)
bool ParamAdjust_CallBack(const Page *cur_page_addr, InputMsg msg)
{
  // ??? ValWin ??????��??? (???)
  ValWin *val_win = (ValWin *)cur_page_addr;
  Page *parent = (Page *)val_win->page.last_page;
  Option *select_opt = WouoUI_ListTitlePageGetSelectOpt(parent); // ???????????��????

  // ??????????????????
  if (msg == msg_up || msg == msg_right)
  { // ??????/????????
    WouoUI_ValWinPageValIncrease(val_win);
    return true; // ???????????????????????
  }
  else if (msg == msg_down || msg == msg_left)
  { // ??????/???????
    WouoUI_ValWinPageValDecrease(val_win);
    return true; // ???????????????????????
  }
  else if (msg == msg_click)
  { // ??????
    // ?��????????????????
    if (parent == (Page *)&left_wheel_menu)
    {
      // ????????PID???? (??? val ?? int32_t?????? float)
      switch (select_opt->order)
      {
      case 1:
        left_wheel_pid.p = val_win->val / 100.0f;
        break;
      case 2:
        left_wheel_pid.i = val_win->val / 100.0f;
        break;
      case 3:
        left_wheel_pid.d = val_win->val / 100.0f;
        break;
      }
      // ?????????? (ValWin ?????? auto_set_bg_opt, ?????????????????????????)
      // left_wheel_options[select_opt->order].val = val_win->val; // ??????????
    }
    else if (parent == (Page *)&right_wheel_menu)
    {
      // ????????PID????
      switch (select_opt->order)
      {
      case 1:
        right_wheel_pid.p = val_win->val / 100.0f;
        break;
      case 2:
        right_wheel_pid.i = val_win->val / 100.0f;
        break;
      case 3:
        right_wheel_pid.d = val_win->val / 100.0f;
        break;
      }
      // ?????????? (ValWin ?????? auto_set_bg_opt, ?????????????????????????)
      // right_wheel_options[select_opt->order].val = val_win->val; // ??????????
    }
    // ???????? return true??????????????
  }
  // ???? msg_return ??????��??????????????? false?????????????????????
  return false;
}

void PIDMenu_Init(void)
{
  // ??????UI
  WouoUI_SelectDefaultUI();

  // ?????�g??????
  WouoUI_BuffClear();
  WouoUI_BuffSend();
  WouoUI_GraphSetPenColor(1);

  // ?????????????
  left_wheel_options[1].val = (int32_t)(left_wheel_pid.p * 100);
  left_wheel_options[2].val = (int32_t)(left_wheel_pid.i * 100);
  left_wheel_options[3].val = (int32_t)(left_wheel_pid.d * 100);

  right_wheel_options[1].val = (int32_t)(right_wheel_pid.p * 100);
  right_wheel_options[2].val = (int32_t)(right_wheel_pid.i * 100);
  right_wheel_options[3].val = (int32_t)(right_wheel_pid.d * 100);

  // ???????????
  WouoUI_TitlePageInit(&main_menu, MAIN_MENU_NUM, main_menu_options, main_menu_icons, MainMenu_CallBack);
  WouoUI_ListPageInit(&left_wheel_menu, sizeof(left_wheel_options) / sizeof(Option), left_wheel_options, Setting_none, LeftWheelMenu_CallBack);
  WouoUI_ListPageInit(&right_wheel_menu, sizeof(right_wheel_options) / sizeof(Option), right_wheel_options, Setting_none, RightWheelMenu_CallBack);
  // WouoUI_SpinWinPageInit(&param_adjust_win, NULL, 0, DecimalNum_2, 0, 1000, true, true, ParamAdjust_CallBack); // ??? SpinWin ?????
  // ????? ValWin ???
  // text: NULL (???? auto_get_bg_opt ???????)
  // init_val: 0 (???? auto_get_bg_opt ???????)
  // min, max, step: ?????????? WouoUI_ValWinPageSetMinStepMax ????
  // auto_get_bg_opt: true (??????????????? text ?? val)
  // auto_set_bg_opt: true (????????? val ��?????????)
  // cb: ParamAdjust_CallBack (??????????????)
  WouoUI_ValWinPageInit(&param_adjust_val_win, NULL, 0, 0, 1000, 10, true, true, ParamAdjust_CallBack);
}

/* Oled ??????? */
void oled_task(void)
{
  static uint8_t last_display_state = 0xFF; // �ϴ���ʾ״̬�����ڱ���Ƶ��ˢ��
  uint8_t current_state = 0;

  // ������״̬
  if (sampling_get_state() == SAMPLING_ACTIVE)
  {
    current_state = 1; // ����״̬

    // ��ȡ��ǰʱ��
    RTC_TimeTypeDef current_rtc_time = {0};
    RTC_DateTypeDef current_rtc_date = {0};
    HAL_RTC_GetTime(&hrtc, &current_rtc_time, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &current_rtc_date, RTC_FORMAT_BIN);

    // ��ȡ��ǰ��ѹֵ
    float voltage = sampling_get_voltage();

    // ��ʾʱ�䣨��һ�У�- hh:mm:ss��ʽ
    oled_printf(0, 0, "%02d:%02d:%02d  ",
                current_rtc_time.Hours,
                current_rtc_time.Minutes,
                current_rtc_time.Seconds);

    // ��ʾ��ѹ���ڶ��У�- XX.XX V��ʽ
    oled_printf(0, 1, "%.2f V  ", voltage);

    // ��յ����к͵�����
    if (last_display_state != current_state)
    {
      oled_printf(0, 2, "        "); // ��յ�����
      oled_printf(0, 3, "        "); // ��յ�����
    }
  }
  else
  {
    current_state = 0; // ����״̬

    // ��ʾϵͳ����״̬����һ�У�
    oled_printf(0, 0, "system idle");

    // ���������
    if (last_display_state != current_state)
    {
      oled_printf(0, 1, "        "); // ��յڶ���
      oled_printf(0, 2, "        "); // ��յ�����
      oled_printf(0, 3, "        "); // ��յ�����
    }
  }

  // ������ʾ״̬
  last_display_state = current_state;
}
