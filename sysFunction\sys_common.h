#ifndef SYS_COMMON_H
#define SYS_COMMON_H

#include <stdint.h>
#include <stdbool.h>
#include <string.h>
#include <stdio.h>

// 统一返回值类型定义
typedef enum {
    SYS_FUNC_OK = 0,           // 操作成功
    SYS_FUNC_ERROR,            // 一般错误
    SYS_FUNC_INVALID_PARAM,    // 参数无效
    SYS_FUNC_NOT_FOUND,        // 未找到
    SYS_FUNC_TIMEOUT,          // 超时
    SYS_FUNC_BUSY,             // 系统忙
    SYS_FUNC_NOT_SUPPORTED     // 不支持的操作
} sys_func_status_t;

// 通用数据结构定义
typedef struct {
    uint16_t year;
    uint8_t month;
    uint8_t day;
    uint8_t hour;
    uint8_t minute;
    uint8_t second;
} datetime_t;

typedef struct {
    float ratio;                 // 变比参数 (0-100)
    float limit;                 // 阈值参数 (0-500)
    sampling_cycle_t cycle;      // 采样周期参数(5s/10s/15s)
    uint32_t crc32;              // CRC32校验值
} config_params_t;

// 采样周期枚举 (与APP/sampling_control.h保持一致)
typedef enum {
    CYCLE_5S = 5,   // 5秒周期
    CYCLE_10S = 10, // 10秒周期
    CYCLE_15S = 15  // 15秒周期
} sampling_cycle_t;

// 采样状态枚举 (与APP/sampling_control.h保持一致)
typedef enum {
    SAMPLING_IDLE = 0,  // 空闲状态
    SAMPLING_ACTIVE = 1 // 采样状态
} sampling_state_t;

// 输出格式枚举
typedef enum {
    OUTPUT_FORMAT_NORMAL = 0,
    OUTPUT_FORMAT_HIDDEN
} output_format_t;

#endif // SYS_COMMON_H
