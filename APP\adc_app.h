// �ļ�����adc_app.h
// ���ܣ�ADC����Ӧ��ͷ�ļ����ṩ����ADC����ģʽ�����ݴ����ӿ�
// ���ߣ��״׵��ӹ�����
// ��Ȩ��Copyright (c) 2024 �״׵��ӹ�����. All rights reserved.

#ifndef __ADC_APP_H_
#define __ADC_APP_H_

#include "stdint.h"  // ��׼���Ͷ���
#include "mydefine.h" // ȫ�ֶ���ͷ�ļ�

void adc_task(void);         // ADC���������� ����:�� ����:��
void dac_sin_init(void);     // DAC���Ҳ���ʼ�� ����:�� ����:��
void adc_dma_init(void);     // ADC DMAģʽ��ʼ�� ����:�� ����:��
void adc_tim_dma_init(void); // ADC��ʱ��DMAģʽ��ʼ�� ����:�� ����:��

#endif /* __ADC_APP_H_ */
