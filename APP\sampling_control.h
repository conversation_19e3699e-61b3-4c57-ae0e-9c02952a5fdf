// 文件名：sampling_control.h
// 功能：采样控制系统头文件，提供数据采集控制和状态管理接口
// 作者：西风电子公司
// 版权：Copyright (c) 2024 西风电子公司. All rights reserved.

#ifndef __SAMPLING_CONTROL_H__
#define __SAMPLING_CONTROL_H__

#include "stdint.h" // 标准数据类型定义

// 采样状态枚举
#ifndef SAMPLING_STATE_T_DEFINED
#define SAMPLING_STATE_T_DEFINED
typedef enum
{
    SAMPLING_IDLE = 0,  // 空闲状态
    SAMPLING_ACTIVE = 1 // 采样状态
} sampling_state_t;
#endif

// 采样周期枚举
#ifndef SAMPLING_CYCLE_T_DEFINED
#define SAMPLING_CYCLE_T_DEFINED
typedef enum
{
    CYCLE_5S = 5,   // 5秒周期
    CYCLE_10S = 10, // 10秒周期
    CYCLE_15S = 15  // 15秒周期（确保精确15秒）
} sampling_cycle_t;
#endif

// 采样控制结构体
typedef struct
{
    sampling_state_t state;    // 采样状态
    sampling_cycle_t cycle;    // 采样周期
    uint32_t last_sample_time; // 上次采样时间戳
    uint32_t led_blink_time;   // LED闪烁时间戳
    uint8_t led_blink_state;   // LED闪烁状态
} sampling_control_t;

// ��������״̬ö��
typedef enum
{
    SAMPLING_OK = 0,       // �����ɹ�
    SAMPLING_ERROR = 1,    // ����ʧ��
    SAMPLING_INVALID = 2,  // ������Ч
    SAMPLING_OVERLIMIT = 3 // ����״̬
} sampling_status_t;

// �������ƺ��Ľӿں���
sampling_status_t sampling_init(void);                        // ��ʼ����������ϵͳ ����:�� ����:����״̬
sampling_status_t sampling_start(void);                       // �������� ����:�� ����:����״̬
sampling_status_t sampling_stop(void);                        // ֹͣ���� ����:�� ����:����״̬
sampling_status_t sampling_set_cycle(sampling_cycle_t cycle); // ���ò������� ����:����ֵ ����:����״̬
sampling_state_t sampling_get_state(void);                    // ��ȡ����״̬ ����:�� ����:��ǰ״̬
sampling_cycle_t sampling_get_cycle(void);                    // ��ȡ�������� ����:�� ����:��ǰ����
void sampling_task(void);                                     // ���������� ����:�� ����:��

// �������ܺ���
float sampling_get_voltage(void);           // ��ȡ��ǰ��ѹֵ ����:�� ����:��ѹֵ
uint8_t sampling_check_overlimit(void);     // ����Ƿ��� ����:�� ����:���ޱ�־
uint8_t sampling_should_sample(void);       // ����Ƿ�Ӧ�ò��� ����:�� ����:������־
void sampling_update_led_blink(void);       // ����LED��˸״̬ ����:�� ����:��
uint8_t sampling_get_led_blink_state(void); // ��ȡLED��˸״̬ ����:�� ����:��˸״̬

#endif // __SAMPLING_CONTROL_H__
