// �ļ�����sampling_control.h
// ���ܣ���������ϵͳͷ�ļ����ṩ���ݲɼ����ƺ�״̬�����ӿ�
// ���ߣ��״׵��ӹ�����
// ��Ȩ��Copyright (c) 2024 �״׵��ӹ�����. All rights reserved.

#ifndef __SAMPLING_CONTROL_H__
#define __SAMPLING_CONTROL_H__

#include "stdint.h" // ��׼���Ͷ���

// ����״̬ö��
typedef enum
{
    SAMPLING_IDLE = 0,  // ����״̬
    SAMPLING_ACTIVE = 1 // ����״̬
} sampling_state_t;

// 采样周期枚举
typedef enum
{
    CYCLE_5S = 5,   // 5秒周期
    CYCLE_10S = 10, // 10秒周期
    CYCLE_15S = 15  // 15秒周期（确保精确15秒）
} sampling_cycle_t;

// �������ƽṹ��
typedef struct
{
    sampling_state_t state;    // ����״̬
    sampling_cycle_t cycle;    // ��������
    uint32_t last_sample_time; // �ϴβ���ʱ���
    uint32_t led_blink_time;   // LED��˸ʱ���
    uint8_t led_blink_state;   // LED��˸״̬
} sampling_control_t;

// ��������״̬ö��
typedef enum
{
    SAMPLING_OK = 0,       // �����ɹ�
    SAMPLING_ERROR = 1,    // ����ʧ��
    SAMPLING_INVALID = 2,  // ������Ч
    SAMPLING_OVERLIMIT = 3 // ����״̬
} sampling_status_t;

// �������ƺ��Ľӿں���
sampling_status_t sampling_init(void);                        // ��ʼ����������ϵͳ ����:�� ����:����״̬
sampling_status_t sampling_start(void);                       // �������� ����:�� ����:����״̬
sampling_status_t sampling_stop(void);                        // ֹͣ���� ����:�� ����:����״̬
sampling_status_t sampling_set_cycle(sampling_cycle_t cycle); // ���ò������� ����:����ֵ ����:����״̬
sampling_state_t sampling_get_state(void);                    // ��ȡ����״̬ ����:�� ����:��ǰ״̬
sampling_cycle_t sampling_get_cycle(void);                    // ��ȡ�������� ����:�� ����:��ǰ����
void sampling_task(void);                                     // ���������� ����:�� ����:��

// �������ܺ���
float sampling_get_voltage(void);           // ��ȡ��ǰ��ѹֵ ����:�� ����:��ѹֵ
uint8_t sampling_check_overlimit(void);     // ����Ƿ��� ����:�� ����:���ޱ�־
uint8_t sampling_should_sample(void);       // ����Ƿ�Ӧ�ò��� ����:�� ����:������־
void sampling_update_led_blink(void);       // ����LED��˸״̬ ����:�� ����:��
uint8_t sampling_get_led_blink_state(void); // ��ȡLED��˸״̬ ����:�� ����:��˸״̬

#endif // __SAMPLING_CONTROL_H__
