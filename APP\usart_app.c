// �ļ�����usart_app.c
// ���ܣ�����ͨ��Ӧ��ģ�飬�ṩ����������û������������������
// ���ߣ��״׵��ӹ�����
// ��Ȩ��Copyright (c) 2024 �״׵��ӹ�����. All rights reserved.

#include "usart_app.h"
#include "stdlib.h" // ��׼�⺯��
#include "stdarg.h" // �ɱ����
#include "string.h" // �ַ�������
#include "stdio.h"  // ��׼�������
#include "usart.h"  // ��������
#include "mydefine.h" // ȫ�ֶ���

uint16_t uart_rx_index = 0;                // ���ڽ�������
uint32_t uart_rx_ticks = 0;                // ���ڽ���ʱ���
uint8_t uart_rx_buffer[128] = {0};         // ���ڽ��ջ�����
uint8_t uart_rx_dma_buffer[128] = {0};     // ����DMA���ջ�����
uint8_t uart_dma_buffer[128] = {0};        // ����DMA����������
uint8_t uart_flag = 0;                     // ���ڱ�־λ
struct rt_ringbuffer uart_ringbuffer;      // ���λ������ṹ
uint8_t ringbuffer_pool[128];              // ���λ������ڴ��

static cmd_state_t g_cmd_state = CMD_STATE_IDLE; // ����״̬��

uint8_t g_sampling_output_enabled = 0; // �������ʹ��
uint32_t g_last_output_time = 0;       // �ϴ����ʱ��

typedef enum // �����ʽö������
{
	OUTPUT_FORMAT_NORMAL = 0, // ������ʽ
	OUTPUT_FORMAT_HIDDEN = 1  // ���ظ�ʽ
} output_format_t;

static output_format_t g_output_format = OUTPUT_FORMAT_NORMAL; // ��ǰ�����ʽ

// ��������
static void convert_voltage_to_hex_format(float voltage, uint16_t *integer_part, uint16_t *decimal_part);
static void test_unhide_conversion(const char* hex_data);

uint32_t convert_rtc_to_unix_timestamp(RTC_TimeTypeDef *time, RTC_DateTypeDef *date) // Unixʱ���ת������ ����:ʱ��ṹ��,���ڽṹ�� ����:Unixʱ���
{
	static const uint8_t days_in_month[12] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31}; // ÿ��������(������)

	uint32_t year = date->Year + 2000; // ת��Ϊ4λ���
	uint32_t month = date->Month;       // �·�
	uint32_t day = date->Date;          // ����
	uint32_t hour = time->Hours;        // Сʱ
	uint32_t minute = time->Minutes;    // ����
	uint32_t second = time->Seconds;    // ��

	uint32_t days = 0; // �����1970�굽ָ����ݵ�����

	for (uint32_t y = 1970; y < year; y++) // ������ݹ��׵�����
	{
		if ((y % 4 == 0 && y % 100 != 0) || (y % 400 == 0)) // �����ж�
		{
			days += 366; // ����
		}
		else
		{
			days += 365; // ƽ��
		}
	}

	for (uint32_t m = 1; m < month; m++) // �����·ݹ��׵�����
	{
		days += days_in_month[m - 1];
		if (m == 2 && ((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0))) // ������������Ѿ�����2�£���Ҫ��1��
		{
			days += 1;
		}
	}

	days += (day - 1); // ���ϵ�ǰ�µ�����(��1��Ϊ�Ǵ�1�ſ�ʼ����)

	// ת��Ϊ����������*24*3600 + Сʱ*3600 + ����*60 + ��
	// �޸�ʱ�����⣺��ȥ8Сʱ(28800��)ת��ΪUTCʱ���
	uint32_t timestamp = days * 86400 + hour * 3600 + minute * 60 + second - 28800;

	return timestamp;
}

// ����Unixʱ���ת����������ȷ��
static void test_unix_timestamp_conversion(void)
{
	// ����������2000-01-02 8:00:21 ������� 946771221
	RTC_TimeTypeDef test_time = {0};
	RTC_DateTypeDef test_date = {0};

	test_time.Hours = 8;
	test_time.Minutes = 0;
	test_time.Seconds = 21;

	test_date.Year = 0;  // 2000��2λ��ʾ��
	test_date.Month = 1; // 1��
	test_date.Date = 2;	 // 2��

	uint32_t result = convert_rtc_to_unix_timestamp(&test_time, &test_date);

	// ������Խ��������֤
	my_printf(&huart1, "Test: 2000-01-02 8:00:21 -> %lu (expected: 946771221)\r\n", result);
	my_printf(&huart1, "Timestamp hex: %08X\r\n", result);

	// ����hideģʽ���
	char hex_output[32];
	format_hex_output(result, 3.2911376953125f, 0, hex_output);
	my_printf(&huart1, "Hide format test: %s (expected: 386E951500034A88)\r\n", hex_output);

	// ��֤��ѹת��
	uint16_t int_part, dec_part;
	convert_voltage_to_hex_format(3.2911376953125f, &int_part, &dec_part);
	my_printf(&huart1, "Voltage 3.291V -> %04X%04X (expected: 00034A88)\r\n", int_part, dec_part);

	// ���Խ��ܹ���
	test_unhide_conversion("386E951500034A88");
}

// ����hide���ݵĲ��Ժ���
static void test_unhide_conversion(const char* hex_data)
{
	if (strlen(hex_data) < 16) {
		my_printf(&huart1, "Invalid hex data length\r\n");
		return;
	}

	// ����ʱ�����ǰ8λ��
	uint32_t timestamp;
	sscanf(hex_data, "%8X", &timestamp);

	// ������ѹ����8λ��
	uint16_t voltage_int, voltage_dec;
	sscanf(hex_data + 8, "%4X%4X", &voltage_int, &voltage_dec);

	// ת��ʱ���Ϊ����ʱ�䣨����8Сʱʱ��ƫ�ƣ�
	uint32_t local_timestamp = timestamp + 28800; // ��8Сʱת��Ϊ����ʱ��
	uint32_t days = local_timestamp / 86400;
	uint32_t remaining = local_timestamp % 86400;
	uint32_t hours = remaining / 3600;
	uint32_t minutes = (remaining % 3600) / 60;
	uint32_t seconds = remaining % 60;

	// ���������գ��򻯰汾����1970�꿪ʼ��
	uint32_t year = 1970;
	while (days >= 365) {
		if ((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0)) {
			if (days >= 366) {
				days -= 366;
				year++;
			} else {
				break;
			}
		} else {
			days -= 365;
			year++;
		}
	}

	// �����ռ���
	uint32_t month = 1;
	uint32_t day = days + 1;

	// ת����ѹ
	float voltage = (float)voltage_int + (float)voltage_dec / 65536.0f;

	my_printf(&huart1, "Unhide test: %s\r\n", hex_data);
	my_printf(&huart1, "Timestamp: %lu\r\n", timestamp);
	my_printf(&huart1, "(%04lu-%02lu-%02lu %02lu:%02lu:%02lu)\r\n", year, month, day, hours, minutes, seconds);
	my_printf(&huart1, "Voltage: %.6fV\r\n", voltage);
}

// ��ѹתHEX��ʽ����
static void convert_voltage_to_hex_format(float voltage, uint16_t *integer_part, uint16_t *decimal_part)
{
	// ��ȡ��ѹ����������
	*integer_part = (uint16_t)voltage;

	// ����С�����֣�(voltage - ��������) * 65536
	float fractional = voltage - (float)(*integer_part);
	*decimal_part = (uint16_t)(fractional * 65536.0f);
}

// ���Ե�ѹHEX���뺯������ȷ��
static void test_voltage_hex_encoding(void)
{
	// ����������12.5V ������� 000C8000
	float test_voltage = 12.5f;
	uint16_t int_part, dec_part;

	convert_voltage_to_hex_format(test_voltage, &int_part, &dec_part);

	// ������Խ��������֤
	my_printf(&huart1, "Test: %.1fV -> %04X%04X (expected: 000C8000)\r\n",
			  test_voltage, int_part, dec_part);
}

// HEX��ʽ�������
void format_hex_output(uint32_t timestamp, float voltage, uint8_t is_overlimit, char *output)
{
	uint16_t int_part, dec_part;

	// ����ѹת��ΪHEX��ʽ
	convert_voltage_to_hex_format(voltage, &int_part, &dec_part);

	// ��ʽ�������8λHEXʱ���+8λHEX��ѹ+���ޱ�־*��
	sprintf(output, "%08X%04X%04X%s",
			timestamp,
			int_part,
			dec_part,
			is_overlimit ? "*" : "");
}

// ����HEX��ʽ�����������ȷ��
static void test_hex_format_output(void)
{
	// ����������2025-01-01 12:30:45, 12.5V, δ����
	uint32_t test_timestamp = 1735705845; // ��Ӧ2025-01-01 12:30:45
	float test_voltage = 12.5f;
	uint8_t test_overlimit = 0;
	char output_buffer[32];

	format_hex_output(test_timestamp, test_voltage, test_overlimit, output_buffer);

	// ������Խ��������֤
	my_printf(&huart1, "Test HEX output: %s (expected: 6774C4F5000C8000)\r\n", output_buffer);

	// ���Գ������
	test_overlimit = 1;
	format_hex_output(test_timestamp, test_voltage, test_overlimit, output_buffer);
	my_printf(&huart1, "Test HEX output (overlimit): %s (expected: 6774C4F5000C8000*)\r\n", output_buffer);
}

int my_printf(UART_HandleTypeDef *huart, const char *format, ...) // ��ʽ��������� ����:���ھ��,��ʽ�ַ���,�ɱ���� ����:����ַ���
{
	char buffer[512]; // ���������
	va_list arg;      // �ɱ�����б�
	int len;          // �������
	va_start(arg, format);
	len = vsnprintf(buffer, sizeof(buffer), format, arg); // ��ʽ���ַ���
	va_end(arg);
	HAL_UART_Transmit(huart, (uint8_t *)buffer, (uint16_t)len, 0xFF); // ���ڷ���
	return len;
}

void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart) // ���ڽ�����ɻص����� ����:���ھ�� ����:��
{
	if (huart->Instance == USART1) // ����Ƿ�ΪUSART1
	{
		uart_rx_ticks = uwTick;                                                 // ��¼����ʱ��
		uart_rx_index++;                                                        // ���ӽ�������
		HAL_UART_Receive_IT(&huart1, &uart_rx_buffer[uart_rx_index], 1);      // ����������һ���ֽ�
	}
}

void HAL_UARTEx_RxEventCallback(UART_HandleTypeDef *huart, uint16_t Size) // ����DMA�����¼��ص����� ����:���ھ��,���մ�С ����:��
{
	if (huart->Instance == USART1) // ����Ƿ�ΪUSART1
	{
		HAL_UART_DMAStop(huart); // ֹͣDMA����

		rt_ringbuffer_put(&uart_ringbuffer, uart_rx_dma_buffer, Size); // �����ݷ��뻷�λ�����

		memset(uart_rx_dma_buffer, 0, sizeof(uart_rx_dma_buffer)); // ���DMA������

		HAL_UARTEx_ReceiveToIdle_DMA(&huart1, uart_rx_dma_buffer, sizeof(uart_rx_dma_buffer)); // ��������DMA����

		__HAL_DMA_DISABLE_IT(&hdma_usart1_rx, DMA_IT_HT); // ���ð봫���ж�
	}
}

void parse_uart_command(uint8_t *buffer, uint16_t length)
{
	// ϵͳ�Լ�����
	if (strcmp((char *)buffer, "test") == 0)
	{
		system_self_check(); // ִ��ϵͳ�Լ�
	}
	// ʱ�����������
	else if (strcmp((char *)buffer, "testtime") == 0)
	{
		test_unix_timestamp_conversion(); // ����ʱ���ת��
	}
	// ���ܲ�������
	else if (strncmp((char *)buffer, "testhide ", 9) == 0)
	{
		if (length > 9) {
			char *hex_data = (char *)buffer + 9; // ����"testhide "
			test_unhide_conversion(hex_data); // ���Խ���
		} else {
			my_printf(&huart1, "Usage: testhide <hex_data>\r\n");
		}
	}
	// RTCʱ����������
	else if (strcmp((char *)buffer, "RTC Config") == 0)
	{
		handle_rtc_config_command(); // ����RTC Config�����߼�
	}
	// RTCʱ���ѯ����
	else if (strcmp((char *)buffer, "RTC now") == 0)
	{
		rtc_print_current_time(); // ��ӡ��ǰRTCʱ��
	}
	// �����ļ���ȡ����
	else if (strcmp((char *)buffer, "conf") == 0)
	{
		handle_conf_command(); // ����conf�����߼�
	}
	// ����������������
	else if (strcmp((char *)buffer, "ratio") == 0)
	{
		handle_ratio_command(); // ����ratio�����߼�
	}
	// ��ֵ������������
	else if (strcmp((char *)buffer, "limit") == 0)
	{
		handle_limit_command(); // ����limit�����߼�
	}
	// ���ñ������� - ֧�ִ��ո�����
	else if (strcmp((char *)buffer, "config save") == 0)
	{
		handle_configsave_command(); // ����config save�����߼�
	}
	// ���ö�ȡ���� - ֧�ִ��ո�����
	else if (strcmp((char *)buffer, "config read") == 0)
	{
		handle_configread_command(); // ����config read�����߼�
	}
	// ���ñ������� - ����ԭ������ݱ���
	else if (strcmp((char *)buffer, "configsave") == 0)
	{
		handle_configsave_command(); // ����configsave�����߼�
	}
	// ���ö�ȡ���� - ����ԭ������ݱ���
	else if (strcmp((char *)buffer, "configread") == 0)
	{
		handle_configread_command(); // ����configread�����߼�
	}
	// ������������
	else if (strcmp((char *)buffer, "start") == 0)
	{
		handle_start_command(); // ����start�����߼�
	}
	// ����ֹͣ����
	else if (strcmp((char *)buffer, "stop") == 0)
	{
		handle_stop_command(); // ����stop�����߼�
	}
	// ����ģʽ����
	else if (strcmp((char *)buffer, "hide") == 0)
	{
		handle_hide_command(); // ����hide�����߼�
	}
	// ȡ����������
	else if (strcmp((char *)buffer, "unhide") == 0)
	{
		handle_unhide_command(); // ����unhide�����߼�
	}
	// ����ʽ���봦��
	else if (g_cmd_state != CMD_STATE_IDLE)
	{
		handle_interactive_input((char *)buffer); // ��������ʽ����
	}
}

void uart_task(void) // ���������� ����:�� ����:��
{
	uint16_t length; // ���ݳ���

	length = rt_ringbuffer_data_len(&uart_ringbuffer); // ��ȡ���λ��������ݳ���

	if (length > 0) // ��������Ҫ����
	{
		rt_ringbuffer_get(&uart_ringbuffer, uart_dma_buffer, length); // �ӻ��λ�������ȡ����

		parse_uart_command(uart_dma_buffer, length); // �������յ�������

		memset(uart_dma_buffer, 0, sizeof(uart_dma_buffer)); // ��ջ�����
	}

	handle_sampling_output(); // ���������������
}

// ����conf���� - ��TF����ȡconfig.ini�ļ����������ò���
void handle_conf_command(void)
{
	ini_config_t ini_config;
	config_params_t config_params;

	// ��¼����ִ�е���־
	data_storage_write_log("conf command");

	// ����config.ini�ļ�
	ini_status_t ini_status = ini_parse_file("config.ini", &ini_config);

	if (ini_status == INI_FILE_NOT_FOUND)
	{
		my_printf(&huart1, "config.ini file not found.\r\n");
		return;
	}

	if (ini_status != INI_OK)
	{
		my_printf(&huart1, "config.ini format error.\r\n");
		return;
	}

	// ����������Ƿ����
	if (!ini_config.ratio_found || !ini_config.limit_found)
	{
		my_printf(&huart1, "config.ini missing parameters.\r\n");
		return;
	}

	// ��֤������Χ
	if (config_validate_ratio(ini_config.ratio) != CONFIG_OK)
	{
		my_printf(&huart1, "ratio parameter out of range (0-100).\r\n");
		return;
	}

	if (config_validate_limit(ini_config.limit) != CONFIG_OK)
	{
		my_printf(&huart1, "limit parameter out of range (0-500).\r\n");
		return;
	}

	// ��ȡ��ǰ����
	if (config_get_params(&config_params) != CONFIG_OK)
	{
		my_printf(&huart1, "config system error.\r\n");
		return;
	}

	// �������ò���
	config_params.ratio = ini_config.ratio;
	config_params.limit = ini_config.limit;

	// ����������
	if (config_set_params(&config_params) != CONFIG_OK)
	{
		my_printf(&huart1, "config update failed.\r\n");
		return;
	}

	// ���浽Flash
	if (config_save_to_flash() != CONFIG_OK)
	{
		my_printf(&huart1, "config save to flash failed.\r\n");
		return;
	}

	// ������ý��
	my_printf(&huart1, "Ratio = %.1f\r\n", ini_config.ratio);
	my_printf(&huart1, "Limit = %.1f\r\n", ini_config.limit);
	my_printf(&huart1, "config read success\r\n");

	// ��¼�ɹ���־
	char log_msg[128];
	sprintf(log_msg, "config read success - ratio %.1f, limit %.1f", ini_config.ratio, ini_config.limit);
	data_storage_write_log(log_msg);
}

// ����ratio���� - ��ʾ��ǰ�����������ȴ��û�����
void handle_ratio_command(void)
{
	config_params_t config_params;

	// ��¼ratio�����־
	data_storage_write_log("ratio command");

	// ��ȡ��ǰ����
	if (config_get_params(&config_params) != CONFIG_OK)
	{
		my_printf(&huart1, "config system error.\r\n");
		return;
	}

	// ��ʾ��ǰratioֵ
	my_printf(&huart1, "Ratio =%.1f\r\n", config_params.ratio);

	// ��ʾ�û�����
	my_printf(&huart1, "Input value(0~100):\r\n");

	// ����״̬���ȴ�����
	g_cmd_state = CMD_STATE_WAIT_RATIO;
}

// ��������ʽ����
void handle_interactive_input(char *input)
{
	float value;
	config_params_t config_params;

	// �����������ֵ
	if (sscanf(input, "%f", &value) != 1)
	{
		my_printf(&huart1, "invalid input format.\r\n");
		g_cmd_state = CMD_STATE_IDLE;
		return;
	}

	// ��ȡ��ǰ����
	if (config_get_params(&config_params) != CONFIG_OK)
	{
		my_printf(&huart1, "config system error.\r\n");
		g_cmd_state = CMD_STATE_IDLE;
		return;
	}

	if (g_cmd_state == CMD_STATE_WAIT_RATIO)
	{
		// ��֤ratio������Χ
		if (config_validate_ratio(value) != CONFIG_OK)
		{
			my_printf(&huart1, "ratio invalid\r\n");
			my_printf(&huart1, "Ratio=%.1f\r\n", config_params.ratio);
		}
		else
		{
			// ����ratio����
			config_params.ratio = value;
			if (config_set_params(&config_params) == CONFIG_OK)
			{
				my_printf(&huart1, "ratio modified success\r\n");
				my_printf(&huart1, "Ratio=%.1f\r\n", value);

				// ��¼ratio���óɹ���־
				char log_msg[64];
				sprintf(log_msg, "ratio config success to %.1f", value);
				data_storage_write_log(log_msg);
			}
			else
			{
				my_printf(&huart1, "config update failed.\r\n");
			}
		}
		g_cmd_state = CMD_STATE_IDLE;
	}
	else if (g_cmd_state == CMD_STATE_WAIT_LIMIT)
	{
		// ��֤limit������Χ
		if (config_validate_limit(value) != CONFIG_OK)
		{
			my_printf(&huart1, "limit invalid\r\n");
			my_printf(&huart1, "limit = %.1f\r\n", config_params.limit);
		}
		else
		{
			// ����limit����
			config_params.limit = value;
			if (config_set_params(&config_params) == CONFIG_OK)
			{
				my_printf(&huart1, "limit modified success\r\n");
				my_printf(&huart1, "limit=%.1f\r\n", value);

				// ��¼limit���óɹ���־
				char log_msg[64];
				sprintf(log_msg, "limit config success to %.1f", value);
				data_storage_write_log(log_msg);
			}
			else
			{
				my_printf(&huart1, "config update failed.\r\n");
			}
		}
		g_cmd_state = CMD_STATE_IDLE;
	}
	else if (g_cmd_state == CMD_STATE_WAIT_RTC)
	{
		// ����RTCʱ������
		HAL_StatusTypeDef status = rtc_set_time_from_string(input);
		if (status == HAL_OK)
		{
			my_printf(&huart1, "RTC Config success\r\n");

			// ��ȡ���õ�ʱ�䲢��ʾ��׼��ʽ
			my_printf(&huart1, "Time: ");
			rtc_print_current_time();

			// ��¼RTC���óɹ���־
			char log_msg[128];
			sprintf(log_msg, "RTC config success to %s", input);
			data_storage_write_log(log_msg);
		}
		else
		{
			my_printf(&huart1, "RTC Config failed\r\n");
			my_printf(&huart1, "Invalid time format. Please use: 2025-01-01 15:00:10 or 2025��01��01��12:00:30\r\n");
		}
		g_cmd_state = CMD_STATE_IDLE;
	}
}

// ����limit���� - ��ʾ��ǰ��ֵ�������ȴ��û�����
void handle_limit_command(void)
{
	config_params_t config_params;

	// ��¼limit�����־
	data_storage_write_log("limit command");

	// ��ȡ��ǰ����
	if (config_get_params(&config_params) != CONFIG_OK)
	{
		my_printf(&huart1, "config system error.\r\n");
		return;
	}

	// ��ʾ��ǰlimitֵ
	my_printf(&huart1, "limit = %.1f\r\n", config_params.limit);

	// ��ʾ�û�����
	my_printf(&huart1, "Input value(0~500):\r\n");

	// ����״̬���ȴ�����
	g_cmd_state = CMD_STATE_WAIT_LIMIT;
}

// ����configsave���� - ���浱ǰ���õ�Flash
void handle_configsave_command(void)
{
	config_params_t config_params;

	// ��ȡ��ǰ���ò���
	if (config_get_params(&config_params) != CONFIG_OK)
	{
		my_printf(&huart1, "config system error.\r\n");
		return;
	}

	// ��ʾ��ǰ����
	my_printf(&huart1, "ratio:%.2f\r\n", config_params.ratio);
	my_printf(&huart1, "limit:%.2f\r\n", config_params.limit);

	// �������õ�Flash
	if (config_save_to_flash() != CONFIG_OK)
	{
		my_printf(&huart1, "save parameters to flash failed.\r\n");
		return;
	}

	// ����ɹ���Ϣ
	my_printf(&huart1, "save parameters to flash\r\n");
}

// ����configread���� - ��Flash��ȡ����
void handle_configread_command(void)
{
	config_params_t config_params;

	// ��Flash��ȡ����
	config_status_t status = config_load_from_flash();
	if (status != CONFIG_OK)
	{
		my_printf(&huart1, "read parameters from flash failed.\r\n");
		return;
	}

	// ����ɹ���Ϣ
	my_printf(&huart1, "read parameters from flash\r\n");

	// ��ȡ��ȡ������ò�������ʾ
	if (config_get_params(&config_params) != CONFIG_OK)
	{
		my_printf(&huart1, "config system error.\r\n");
		return;
	}

	// ��ʾ��ȡ������
	my_printf(&huart1, "ratio: %.2f\r\n", config_params.ratio);
	my_printf(&huart1, "limit: %.2f\r\n", config_params.limit);
}

// ����start���� - ��������
void handle_start_command(void)
{
	// ȷ������ϵͳ�ѳ�ʼ��
	sampling_init();

	// ��������ϵͳ
	if (sampling_start() != SAMPLING_OK)
	{
		my_printf(&huart1, "sampling start failed.\r\n");
		return;
	}

	// ���������Ϣ
	my_printf(&huart1, "Periodic Sampling\r\n");

	// ��ʾ��ǰ������������
	sampling_cycle_t cycle = sampling_get_cycle();
	my_printf(&huart1, "sample cycle: %ds\r\n", (int)cycle);

	// ���ò����������
	g_sampling_output_enabled = 1;
	g_last_output_time = HAL_GetTick();

	// ��¼������־
	char log_msg[64];
	sprintf(log_msg, "sample start - cycle %ds (command)", (int)cycle);
	data_storage_write_log(log_msg);
}

// ����stop���� - ֹͣ����
void handle_stop_command(void)
{
	// ȷ������ϵͳ�ѳ�ʼ��
	sampling_init();

	// ֹͣ����ϵͳ
	if (sampling_stop() != SAMPLING_OK)
	{
		my_printf(&huart1, "sampling stop failed.\r\n");
		return;
	}

	// ���ֹͣ��Ϣ
	my_printf(&huart1, "PeriodicSamplingSTOP\r\n");

	// ���ò����������
	g_sampling_output_enabled = 0;

	// ��¼ֹͣ��־
	data_storage_write_log("sample stop (command)");
}

// ���������������
void handle_sampling_output(void)
{
	// ����������Ƿ�ʹ��
	if (!g_sampling_output_enabled)
	{
		return;
	}

	// ������״̬
	if (sampling_get_state() != SAMPLING_ACTIVE)
	{
		return;
	}

	// ����Ƿ񵽴����ʱ��
	uint32_t current_time = HAL_GetTick();
	sampling_cycle_t cycle = sampling_get_cycle();
	uint32_t cycle_ms = cycle * 1000; // ת��Ϊ����

	if (current_time - g_last_output_time >= cycle_ms)
	{
		// �������ʱ��
		g_last_output_time = current_time;

		// ��ȡ��ǰʱ��
		RTC_TimeTypeDef current_rtc_time = {0};
		RTC_DateTypeDef current_rtc_date = {0};
		HAL_RTC_GetTime(&hrtc, &current_rtc_time, RTC_FORMAT_BIN);
		HAL_RTC_GetDate(&hrtc, &current_rtc_date, RTC_FORMAT_BIN);

		// ��ȡ��ǰ��ѹ
		float voltage = sampling_get_voltage();

		// ����Ƿ���
		uint8_t is_overlimit = sampling_check_overlimit();

		// ���������ʽ���в�ͬ�Ĵ���
		if (g_output_format == OUTPUT_FORMAT_HIDDEN)
		{
			// ����ģʽ�����HEX��ʽ
			uint32_t timestamp = convert_rtc_to_unix_timestamp(&current_rtc_time, &current_rtc_date);
			char hex_output[32];

			format_hex_output(timestamp, voltage, is_overlimit, hex_output);
			my_printf(&huart1, "%s\r\n", hex_output);
		}
		else
		{
			// ����ģʽ������ɶ���ʽ������������Ϣ
			if (is_overlimit)
			{
				// ��ȡlimit��ֵ
				config_params_t config_params;
				if (config_get_params(&config_params) == CONFIG_OK)
				{
					my_printf(&huart1, "%04d-%02d-%02d %02d:%02d:%02d ch0=%.2fV OverLimit(%.2f)!!\r\n",
							  current_rtc_date.Year + 2000,
							  current_rtc_date.Month,
							  current_rtc_date.Date,
							  current_rtc_time.Hours,
							  current_rtc_time.Minutes,
							  current_rtc_time.Seconds,
							  voltage,
							  config_params.limit);
				}
				else
				{
					my_printf(&huart1, "%04d-%02d-%02d %02d:%02d:%02d ch0=%.2fV OverLimit!!\r\n",
							  current_rtc_date.Year + 2000,
							  current_rtc_date.Month,
							  current_rtc_date.Date,
							  current_rtc_time.Hours,
							  current_rtc_time.Minutes,
							  current_rtc_time.Seconds,
							  voltage);
				}
			}
			else
			{
				my_printf(&huart1, "%04d-%02d-%02d %02d:%02d:%02d ch0=%.2fV\r\n",
						  current_rtc_date.Year + 2000,
						  current_rtc_date.Month,
						  current_rtc_date.Date,
						  current_rtc_time.Hours,
						  current_rtc_time.Minutes,
						  current_rtc_time.Seconds,
						  voltage);
			}
		}

		// ���ݴ洢���������������ʽѡ��洢��ʽ
		// ע�⣺���ݴ洢�������ʽ�޹أ�����Ҫ�洢
		if (g_output_format == OUTPUT_FORMAT_HIDDEN)
		{
			// ����ģʽ���洢��hideData�ļ��У����洢��sample
			data_storage_status_t result = data_storage_write_hidedata(voltage, is_overlimit);
			if (result != DATA_STORAGE_OK)
			{
				// �洢ʧ��ʱ�����������Ϣ���������������
				// my_printf(&huart1, "Warning: hideData storage failed\r\n");
			}
		}
		else
		{
			// ����ģʽ���洢��sample
			data_storage_status_t result = data_storage_write_sample(voltage);
			if (result != DATA_STORAGE_OK)
			{
				// �洢ʧ��ʱ�����������Ϣ���������������
				// my_printf(&huart1, "Warning: sample storage failed\r\n");
			}
		}

		// �������ݶ���洢��overLimit�ļ��У�����hideģʽ��
		if (is_overlimit)
		{
			config_params_t config_params;
			float limit_value = 0.0f;

			// ��ȡlimit��ֵ���ڴ洢
			if (config_get_params(&config_params) == CONFIG_OK)
			{
				limit_value = config_params.limit;
			}

			data_storage_status_t result = data_storage_write_overlimit(voltage, limit_value);
			if (result != DATA_STORAGE_OK)
			{
				// �洢ʧ��ʱ�����������Ϣ���������������
				// my_printf(&huart1, "Warning: overLimit storage failed\r\n");
			}
		}
	}
}

// ����hide���� - �л����������ģʽ
void handle_hide_command(void)
{
	// ����Ϊ���������ʽ
	g_output_format = OUTPUT_FORMAT_HIDDEN;

	// ��¼hideģʽ�л���־
	data_storage_write_log("hide data");

	// �����ȷ����Ϣ����������ģʽ�ľ�Ĭ����
	// my_printf(&huart1, "Output format switched to hidden.\r\n");
}

// ����unhide���� - �л����������ģʽ
void handle_unhide_command(void)
{
	// ����Ϊ���������ʽ
	g_output_format = OUTPUT_FORMAT_NORMAL;

	// ��¼unhideģʽ�л���־
	data_storage_write_log("unhide data");

	// �����ȷ����Ϣ�������������������
	// my_printf(&huart1, "Output format switched to normal.\r\n");
}

// ����RTC Config���� - ��ʾ�û�����ʱ�䲢�ȴ�����
void handle_rtc_config_command(void)
{
	// ��¼RTC Config�����־
	data_storage_write_log("RTC Config command");

	// ��ʾ�û�����ʱ��
	my_printf(&huart1, "Input Datetime\r\n");

	// ����״̬���ȴ�RTCʱ������
	g_cmd_state = CMD_STATE_WAIT_RTC;
}
