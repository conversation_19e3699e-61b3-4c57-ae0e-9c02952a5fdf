// 文件名：usart_app.c
// 功能：串口通信应用模块，提供命令解析、用户交互和数据输出功能
// 作者：西风电子公司
// 版权：Copyright (c) 2024 西风电子公司. All rights reserved.

#include "usart_app.h"
#include "stdlib.h" // 标准库函数
#include "stdarg.h" // 可变参数
#include "string.h" // 字符串处理
#include "stdio.h"  // 标准输入输出
#include "usart.h"  // 串口驱动
#include "mydefine.h" // 全局定义

uint16_t uart_rx_index = 0;                // ���ڽ�������
uint32_t uart_rx_ticks = 0;                // ���ڽ���ʱ���
uint8_t uart_rx_buffer[128] = {0};         // ���ڽ��ջ�����
uint8_t uart_rx_dma_buffer[128] = {0};     // ����DMA���ջ�����
uint8_t uart_dma_buffer[128] = {0};        // ����DMA����������
uint8_t uart_flag = 0;                     // ���ڱ�־λ
struct rt_ringbuffer uart_ringbuffer;      // ���λ������ṹ
uint8_t ringbuffer_pool[128];              // ���λ������ڴ��

static cmd_state_t g_cmd_state = CMD_STATE_IDLE; // ����״̬��

uint8_t g_sampling_output_enabled = 0; // �������ʹ��
uint32_t g_last_output_time = 0;       // �ϴ����ʱ��

typedef enum // �����ʽö������
{
	OUTPUT_FORMAT_NORMAL = 0, // ������ʽ
	OUTPUT_FORMAT_HIDDEN = 1  // ���ظ�ʽ
} output_format_t;

static output_format_t g_output_format = OUTPUT_FORMAT_NORMAL; // ��ǰ�����ʽ

// ��������
static void convert_voltage_to_hex_format(float voltage, uint16_t *integer_part, uint16_t *decimal_part);
static void test_unhide_conversion(const char* hex_data);
static void test_storage_status(void);
static void test_button_functions(void);

uint32_t convert_rtc_to_unix_timestamp(RTC_TimeTypeDef *time, RTC_DateTypeDef *date) // Unixʱ���ת������ ����:ʱ��ṹ��,���ڽṹ�� ����:Unixʱ���
{
	static const uint8_t days_in_month[12] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31}; // ÿ��������(������)

	uint32_t year = date->Year + 2000; // ת��Ϊ4λ���
	uint32_t month = date->Month;       // �·�
	uint32_t day = date->Date;          // ����
	uint32_t hour = time->Hours;        // Сʱ
	uint32_t minute = time->Minutes;    // ����
	uint32_t second = time->Seconds;    // ��

	uint32_t days = 0; // �����1970�굽ָ����ݵ�����

	for (uint32_t y = 1970; y < year; y++) // ������ݹ��׵�����
	{
		if ((y % 4 == 0 && y % 100 != 0) || (y % 400 == 0)) // �����ж�
		{
			days += 366; // ����
		}
		else
		{
			days += 365; // ƽ��
		}
	}

	for (uint32_t m = 1; m < month; m++) // �����·ݹ��׵�����
	{
		days += days_in_month[m - 1];
		if (m == 2 && ((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0))) // ������������Ѿ�����2�£���Ҫ��1��
		{
			days += 1;
		}
	}

	days += (day - 1); // ���ϵ�ǰ�µ�����(��1��Ϊ�Ǵ�1�ſ�ʼ����)

	// ת��Ϊ����������*24*3600 + Сʱ*3600 + ����*60 + ��
	// ����������ʱ���ת��ΪUnixʱ���
	uint32_t timestamp = days * 86400 + hour * 3600 + minute * 60 + second;

	return timestamp;
}

// ����Unixʱ���ת����������ȷ��
static void test_unix_timestamp_conversion(void)
{
	// ����������2000-01-02 8:00:21 ������� 946800021
	RTC_TimeTypeDef test_time = {0};
	RTC_DateTypeDef test_date = {0};

	test_time.Hours = 8;
	test_time.Minutes = 0;
	test_time.Seconds = 21;

	test_date.Year = 0;  // 2000��2λ��ʾ��
	test_date.Month = 1; // 1��
	test_date.Date = 2;	 // 2��

	uint32_t result = convert_rtc_to_unix_timestamp(&test_time, &test_date);

	// ������Խ��������֤
	my_printf(&huart1, "Test: 2000-01-02 8:00:21 -> %lu (expected: 946800021)\r\n", result);
	my_printf(&huart1, "Timestamp hex: %08X\r\n", result);

	// ����hideģʽ���
	char hex_output[32];
	format_hex_output(result, 3.2911376953125f, 0, hex_output);
	my_printf(&huart1, "Hide format test: %s (expected: 386E951500034A88)\r\n", hex_output);

	// ��֤��ѹת��
	uint16_t int_part, dec_part;
	convert_voltage_to_hex_format(3.2911376953125f, &int_part, &dec_part);
	my_printf(&huart1, "Voltage 3.291V -> %04X%04X (expected: 00034A88)\r\n", int_part, dec_part);

	// ���Խ��ܹ���
	test_unhide_conversion("386E951500034A88");
}

// ����hide���ݵĲ��Ժ���
static void test_unhide_conversion(const char* hex_data)
{
	if (strlen(hex_data) < 16) {
		my_printf(&huart1, "Invalid hex data length\r\n");
		return;
	}

	// ����ʱ�����ǰ8λ��
	uint32_t timestamp;
	sscanf(hex_data, "%8X", &timestamp);

	// ������ѹ����8λ��
	uint16_t voltage_int, voltage_dec;
	sscanf(hex_data + 8, "%4X%4X", &voltage_int, &voltage_dec);

	// ת��ʱ���Ϊ����ʱ�䣨�Ѿ���������ʱ���ޣ�
	uint32_t local_timestamp = timestamp; // ֱ��ʹ�ñ���ʱ���
	uint32_t days = local_timestamp / 86400;
	uint32_t remaining = local_timestamp % 86400;
	uint32_t hours = remaining / 3600;
	uint32_t minutes = (remaining % 3600) / 60;
	uint32_t seconds = remaining % 60;

	// ���������գ��򻯰汾����1970�꿪ʼ��
	uint32_t year = 1970;
	while (days >= 365) {
		if ((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0)) {
			if (days >= 366) {
				days -= 366;
				year++;
			} else {
				break;
			}
		} else {
			days -= 365;
			year++;
		}
	}

	// 计算月日信息
	uint32_t month = 1;
	uint32_t day = days + 1;

	// 转换电压
	float voltage = (float)voltage_int + (float)voltage_dec / 65536.0f;

	my_printf(&huart1, "Unhide test: %s\r\n", hex_data);
	my_printf(&huart1, "Timestamp: %lu\r\n", timestamp);
	my_printf(&huart1, "(%04lu-%02lu-%02lu %02lu:%02lu:%02lu)\r\n", year, month, day, hours, minutes, seconds);
	my_printf(&huart1, "Voltage: %.6fV\r\n", voltage);
}

// 电压转HEX格式编码
static void convert_voltage_to_hex_format(float voltage, uint16_t *integer_part, uint16_t *decimal_part)
{
	// 获取电压的整数部分
	*integer_part = (uint16_t)voltage;

	// 计算小数部分：(voltage - 整数部分) * 65536
	float fractional = voltage - (float)(*integer_part);
	*decimal_part = (uint16_t)(fractional * 65536.0f);
}

// 测试电压HEX编码函数的正确性
static void test_voltage_hex_encoding(void)
{
	// 测试用例：12.5V 期望结果 000C8000
	float test_voltage = 12.5f;
	uint16_t int_part, dec_part;

	convert_voltage_to_hex_format(test_voltage, &int_part, &dec_part);

	// 输出测试结果进行验证
	my_printf(&huart1, "Test: %.1fV -> %04X%04X (expected: 000C8000)\r\n",
			  test_voltage, int_part, dec_part);
}

// HEX格式输出生成
void format_hex_output(uint32_t timestamp, float voltage, uint8_t is_overlimit, char *output)
{
	uint16_t int_part, dec_part;

	// 将电压转换为HEX格式
	convert_voltage_to_hex_format(voltage, &int_part, &dec_part);

	// 格式化输出：8位HEX时间戳+8位HEX电压+超限标志*
	sprintf(output, "%08X%04X%04X%s",
			timestamp,
			int_part,
			dec_part,
			is_overlimit ? "*" : "");
}

// ����HEX��ʽ�����������ȷ��
static void test_hex_format_output(void)
{
	// ����������2025-01-01 12:30:45, 12.5V, δ����
	uint32_t test_timestamp = 1735705845; // ��Ӧ2025-01-01 12:30:45
	float test_voltage = 12.5f;
	uint8_t test_overlimit = 0;
	char output_buffer[32];

	format_hex_output(test_timestamp, test_voltage, test_overlimit, output_buffer);

	// ������Խ��������֤
	my_printf(&huart1, "Test HEX output: %s (expected: 6774C4F5000C8000)\r\n", output_buffer);

	// ���Գ������
	test_overlimit = 1;
	format_hex_output(test_timestamp, test_voltage, test_overlimit, output_buffer);
	my_printf(&huart1, "Test HEX output (overlimit): %s (expected: 6774C4F5000C8000*)\r\n", output_buffer);
}

// ���Բ������ڵ��־û�����
static void test_cycle_persistence(void)
{
	my_printf(&huart1, "=== Enhanced Cycle Persistence Test ===\r\n");

	// ��ȡ��ǰ����
	sampling_cycle_t current_cycle = sampling_get_cycle();
	my_printf(&huart1, "Current cycle: %ds\r\n", (int)current_cycle);

	// ��ȡ����ϵͳ�е�����
	sampling_cycle_t config_cycle = config_get_sampling_cycle();
	my_printf(&huart1, "Config cycle: %ds\r\n", (int)config_cycle);

	// ��֤һ����
	if (current_cycle == config_cycle) {
		my_printf(&huart1, "Cycle consistency: OK\r\n");
	} else {
		my_printf(&huart1, "Cycle consistency: FAILED\r\n");
	}

	// ���Բ������ڵ��־û�
	my_printf(&huart1, "\n1. Testing All Cycle Values:\r\n");
	sampling_cycle_t test_cycles[] = {CYCLE_5S, CYCLE_10S, CYCLE_15S};
	const char* cycle_names[] = {"5s", "10s", "15s"};

	for (int i = 0; i < 3; i++) {
		my_printf(&huart1, "   Testing %s cycle...\r\n", cycle_names[i]);

		if (sampling_set_cycle(test_cycles[i]) == SAMPLING_OK) {
			sampling_cycle_t new_cycle = sampling_get_cycle();
			sampling_cycle_t new_config_cycle = config_get_sampling_cycle();

			if (new_cycle == test_cycles[i] && new_config_cycle == test_cycles[i]) {
				my_printf(&huart1, "   %s cycle: OK\r\n", cycle_names[i]);
			} else {
				my_printf(&huart1, "   %s cycle: FAILED\r\n", cycle_names[i]);
			}
		} else {
			my_printf(&huart1, "   %s cycle set: FAILED\r\n", cycle_names[i]);
		}
	}

	// ���԰���ģ�⹦��
	my_printf(&huart1, "\n2. Button Function Simulation:\r\n");
	my_printf(&huart1, "   Simulating KEY2 (5s cycle)...\r\n");
	sampling_init();
	if (sampling_set_cycle(CYCLE_5S) == SAMPLING_OK) {
		my_printf(&huart1, "   KEY2 simulation: OK\r\n");
	} else {
		my_printf(&huart1, "   KEY2 simulation: FAILED\r\n");
	}

	// ��֤����ʱ����
	my_printf(&huart1, "\n3. Immediate Effect Verification:\r\n");
	sampling_cycle_t before_cycle = sampling_get_cycle();
	sampling_cycle_t target_cycle = (before_cycle == CYCLE_5S) ? CYCLE_10S : CYCLE_5S;

	if (sampling_set_cycle(target_cycle) == SAMPLING_OK) {
		sampling_cycle_t after_cycle = sampling_get_cycle();
		if (after_cycle == target_cycle) {
			my_printf(&huart1, "   Immediate effect: OK\r\n");
		} else {
			my_printf(&huart1, "   Immediate effect: FAILED\r\n");
		}
	}

	// �ָ�ԭ������
	sampling_set_cycle(current_cycle);
	my_printf(&huart1, "\nRestored original cycle: %ds\r\n", (int)current_cycle);
	my_printf(&huart1, "=== Test Complete ===\r\n");
}

// ���Բ������ڶ�ʱ������
static void test_timing_accuracy(void)
{
	my_printf(&huart1, "=== Enhanced Timing Accuracy Test ===\r\n");

	// ��ȡ��ǰ����
	sampling_cycle_t current_cycle = sampling_get_cycle();
	my_printf(&huart1, "Current cycle: %ds\r\n", (int)current_cycle);

	// ��ʾ��ǰʱ��
	RTC_TimeTypeDef current_time = {0};
	RTC_DateTypeDef current_date = {0};
	HAL_RTC_GetTime(&hrtc, &current_time, RTC_FORMAT_BIN);
	HAL_RTC_GetDate(&hrtc, &current_date, RTC_FORMAT_BIN);

	my_printf(&huart1, "Test start time: %04d-%02d-%02d %02d:%02d:%02d\r\n",
			  current_date.Year + 2000,
			  current_date.Month,
			  current_date.Date,
			  current_time.Hours,
			  current_time.Minutes,
			  current_time.Seconds);

	// ���Բ�ͬ���ڵ�׼ȷ��
	my_printf(&huart1, "\n1. Multi-Cycle Accuracy Test:\r\n");
	sampling_cycle_t test_cycles[] = {CYCLE_5S, CYCLE_10S, CYCLE_15S};
	const char* cycle_names[] = {"5s", "10s", "15s"};

	for (int i = 0; i < 3; i++) {
		my_printf(&huart1, "   %s cycle timing analysis:\r\n", cycle_names[i]);
		my_printf(&huart1, "   Expected interval: %ds\r\n", (int)test_cycles[i]);
		my_printf(&huart1, "   Theoretical accuracy: ±50ms\r\n");
		my_printf(&huart1, "   Monitor next few outputs for verification\r\n");
	}

	// LED��˸ʱ�����
	my_printf(&huart1, "\n2. LED Timing Analysis:\r\n");
	my_printf(&huart1, "   LED1 blink interval: 1000ms\r\n");
	my_printf(&huart1, "   Expected accuracy: ±50ms (after optimization)\r\n");
	my_printf(&huart1, "   Monitor LED1 for consistent 1s intervals\r\n");

	// ϵͳ��ʱ��Դ��Ϣ
	my_printf(&huart1, "\n3. System Timer Information:\r\n");
	my_printf(&huart1, "   HAL_GetTick() resolution: 1ms\r\n");
	my_printf(&huart1, "   Timing algorithm: Predictive time accumulation\r\n");
	my_printf(&huart1, "   Cumulative error: Eliminated\r\n");

	// ʵʱ���ܼ��
	my_printf(&huart1, "\n4. Real-time Monitoring:\r\n");
	my_printf(&huart1, "   Current HAL_GetTick(): %lu ms\r\n", HAL_GetTick());
	my_printf(&huart1, "   Expected interval: %ds (%lu ms)\r\n",
		(int)current_cycle, (uint32_t)current_cycle * 1000);
	my_printf(&huart1, "   Monitor output timestamps for accuracy\r\n");

	// ���Խ���
	my_printf(&huart1, "\n5. Test Instructions:\r\n");
	my_printf(&huart1, "   - Observe sampling output timestamps\r\n");
	my_printf(&huart1, "   - Check LED1 blink consistency\r\n");
	my_printf(&huart1, "   - Verify timing accuracy over multiple cycles\r\n");
	my_printf(&huart1, "   - Expected deviation: <±50ms\r\n");

	my_printf(&huart1, "=== Monitor sampling output for timing accuracy ===\r\n");
}

// ���Դ洢ϵͳ״̬
static void test_storage_status(void)
{
	my_printf(&huart1, "=== Storage System Diagnostic ===\r\n");

	// 1. ���TF��״̬
	my_printf(&huart1, "1. TF Card Status:\r\n");
	if (data_storage_init() == DATA_STORAGE_OK) {
		my_printf(&huart1, "   TF Card: OK\r\n");
	} else {
		my_printf(&huart1, "   TF Card: FAILED\r\n");
		my_printf(&huart1, "=== Test Complete ===\r\n");
		return;
	}

	// 2. ���ֵ���⹦��
	my_printf(&huart1, "2. Overlimit Detection Test:\r\n");
	float current_voltage = sampling_get_voltage();
	my_printf(&huart1, "   Current voltage: %.2fV\r\n", current_voltage);

	config_params_t config_params;
	if (config_get_params(&config_params) == CONFIG_OK) {
		my_printf(&huart1, "   Current limit: %.2fV\r\n", config_params.limit);

		// ���overlimit���
		uint8_t is_overlimit = sampling_check_overlimit();
		my_printf(&huart1, "   Overlimit status: %s\r\n", is_overlimit ? "YES" : "NO");

		// ���overlimit洢
		if (is_overlimit) {
			data_storage_status_t result = data_storage_write_overlimit(current_voltage, config_params.limit);
			my_printf(&huart1, "   Overlimit storage: %s\r\n",
				(result == DATA_STORAGE_OK) ? "OK" : "FAILED");
		} else {
			my_printf(&huart1, "   Overlimit storage: Not triggered (voltage below limit)\r\n");
		}
	} else {
		my_printf(&huart1, "   Config error: Cannot get limit value\r\n");
	}

	// 3. ���hide模式
	my_printf(&huart1, "3. Hide Mode Test:\r\n");
	my_printf(&huart1, "   Current output format: %s\r\n",
		(g_output_format == OUTPUT_FORMAT_HIDDEN) ? "HIDDEN" : "NORMAL");

	if (g_output_format == OUTPUT_FORMAT_HIDDEN) {
		// ���hide洢
		uint8_t is_overlimit = sampling_check_overlimit();
		data_storage_status_t result = data_storage_write_hidedata(current_voltage, is_overlimit);
		my_printf(&huart1, "   Hide storage: %s\r\n",
			(result == DATA_STORAGE_OK) ? "OK" : "FAILED");
	} else {
		my_printf(&huart1, "   Hide storage: Not active (normal mode)\r\n");
	}

	// 4. 文件系统状态
	my_printf(&huart1, "4. File System Status:\r\n");
	my_printf(&huart1, "   Boot count mechanism: Normal (based on boot_count.txt)\r\n");
	my_printf(&huart1, "   Log file naming: log{boot_count}.txt\r\n");
	my_printf(&huart1, "   Note: File numbering continues from last boot count\r\n");

	// 5. 数据存储目录
	my_printf(&huart1, "5. Storage Directories:\r\n");
	my_printf(&huart1, "   sample/     - Normal sampling data\r\n");
	my_printf(&huart1, "   overLimit/  - Overlimit data\r\n");
	my_printf(&huart1, "   log/        - System logs\r\n");
	my_printf(&huart1, "   hideData/   - Hidden format data\r\n");

	my_printf(&huart1, "=== Test Complete ===\r\n");
}

// ���԰���2-4����
static void test_button_functions(void)
{
	my_printf(&huart1, "=== Button Functions Test ===\r\n");

	// ��ȡ��ǰ����
	sampling_cycle_t original_cycle = sampling_get_cycle();
	my_printf(&huart1, "Original cycle: %ds\r\n", (int)original_cycle);

	// ģ��KEY2����(5s����)
	my_printf(&huart1, "\n1. Testing KEY2 Function (5s cycle):\r\n");
	my_printf(&huart1, "   Simulating: sampling_init() + sampling_set_cycle(CYCLE_5S)\r\n");

	sampling_init();
	if (sampling_set_cycle(CYCLE_5S) == SAMPLING_OK) {
		sampling_cycle_t new_cycle = sampling_get_cycle();
		sampling_cycle_t config_cycle = config_get_sampling_cycle();

		my_printf(&huart1, "   KEY2 function: OK\r\n");
		my_printf(&huart1, "   New cycle: %ds\r\n", (int)new_cycle);
		my_printf(&huart1, "   Config saved: %s\r\n",
			(config_cycle == CYCLE_5S) ? "YES" : "NO");
	} else {
		my_printf(&huart1, "   KEY2 function: FAILED\r\n");
	}

	// ģ��KEY3����(10s����)
	my_printf(&huart1, "\n2. Testing KEY3 Function (10s cycle):\r\n");
	my_printf(&huart1, "   Simulating: sampling_init() + sampling_set_cycle(CYCLE_10S)\r\n");

	sampling_init();
	if (sampling_set_cycle(CYCLE_10S) == SAMPLING_OK) {
		sampling_cycle_t new_cycle = sampling_get_cycle();
		sampling_cycle_t config_cycle = config_get_sampling_cycle();

		my_printf(&huart1, "   KEY3 function: OK\r\n");
		my_printf(&huart1, "   New cycle: %ds\r\n", (int)new_cycle);
		my_printf(&huart1, "   Config saved: %s\r\n",
			(config_cycle == CYCLE_10S) ? "YES" : "NO");
	} else {
		my_printf(&huart1, "   KEY3 function: FAILED\r\n");
	}

	// ģ��KEY4����(15s����)
	my_printf(&huart1, "\n3. Testing KEY4 Function (15s cycle):\r\n");
	my_printf(&huart1, "   Simulating: sampling_init() + sampling_set_cycle(CYCLE_15S)\r\n");

	sampling_init();
	if (sampling_set_cycle(CYCLE_15S) == SAMPLING_OK) {
		sampling_cycle_t new_cycle = sampling_get_cycle();
		sampling_cycle_t config_cycle = config_get_sampling_cycle();

		my_printf(&huart1, "   KEY4 function: OK\r\n");
		my_printf(&huart1, "   New cycle: %ds\r\n", (int)new_cycle);
		my_printf(&huart1, "   Config saved: %s\r\n",
			(config_cycle == CYCLE_15S) ? "YES" : "NO");
	} else {
		my_printf(&huart1, "   KEY4 function: FAILED\r\n");
	}

	// ���������־û�
	my_printf(&huart1, "\n4. Configuration Persistence Test:\r\n");
	sampling_cycle_t final_cycle = sampling_get_cycle();
	sampling_cycle_t final_config = config_get_sampling_cycle();

	if (final_cycle == final_config) {
		my_printf(&huart1, "   Persistence consistency: OK\r\n");
	} else {
		my_printf(&huart1, "   Persistence consistency: FAILED\r\n");
	}

	// ��ʾ��ǰ״̬
	my_printf(&huart1, "\n5. Current Status:\r\n");
	my_printf(&huart1, "   Current cycle: %ds\r\n", (int)final_cycle);
	my_printf(&huart1, "   Config cycle: %ds\r\n", (int)final_config);
	my_printf(&huart1, "   Sampling state: %s\r\n",
		(sampling_get_state() == SAMPLING_ACTIVE) ? "ACTIVE" : "STOPPED");

	// 恢复原始周期
	sampling_set_cycle(original_cycle);
	my_printf(&huart1, "\nRestored original cycle: %ds\r\n", (int)original_cycle);
	my_printf(&huart1, "=== Test Complete ===\r\n");
}

// 测试15秒周期的时间精度
static void test_15s_timing_accuracy(void)
{
	my_printf(&huart1, "=== 15s Timing Accuracy Test ===\r\n");

	// 保存当前状态
	sampling_state_t original_state = sampling_get_state();
	sampling_cycle_t original_cycle = sampling_get_cycle();

	// 设置15秒周期
	sampling_set_cycle(CYCLE_15S);
	my_printf(&huart1, "Set cycle to 15s\r\n");

	// 启动采样
	if (sampling_start() == SAMPLING_OK) {
		my_printf(&huart1, "Sampling started. Monitor timing for 3 cycles...\r\n");
		my_printf(&huart1, "Expected: exactly 15000ms intervals\r\n");
		my_printf(&huart1, "Use 'stop' command to end test\r\n");

		// 启用采样输出
		extern uint8_t g_sampling_output_enabled;
		extern uint32_t g_last_output_time;
		g_sampling_output_enabled = 1;
		g_last_output_time = HAL_GetTick();

		my_printf(&huart1, "Test start time: %lu ms\r\n", HAL_GetTick());
	} else {
		my_printf(&huart1, "Failed to start sampling\r\n");
		// 恢复原始设置
		sampling_set_cycle(original_cycle);
		if (original_state == SAMPLING_ACTIVE) {
			sampling_start();
		}
	}
}

int my_printf(UART_HandleTypeDef *huart, const char *format, ...) // 格式化串口输出 参数:串口句柄,格式字符串,可变参数 返回:输出字符数
{
	char buffer[512]; // 输出缓冲区
	va_list arg;      // 可变参数列表
	int len;          // 输出长度
	va_start(arg, format);
	len = vsnprintf(buffer, sizeof(buffer), format, arg); // 格式化字符串
	va_end(arg);
	HAL_UART_Transmit(huart, (uint8_t *)buffer, (uint16_t)len, 0xFF); // 串口发送
	return len;
}

void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart) // ���ڽ�����ɻص����� ����:���ھ�� ����:��
{
	if (huart->Instance == USART1) // ����Ƿ�ΪUSART1
	{
		uart_rx_ticks = uwTick;                                                 // ��¼����ʱ��
		uart_rx_index++;                                                        // ���ӽ�������
		HAL_UART_Receive_IT(&huart1, &uart_rx_buffer[uart_rx_index], 1);      // ����������һ���ֽ�
	}
}

void HAL_UARTEx_RxEventCallback(UART_HandleTypeDef *huart, uint16_t Size) // ����DMA�����¼��ص����� ����:���ھ��,���մ�С ����:��
{
	if (huart->Instance == USART1) // ����Ƿ�ΪUSART1
	{
		HAL_UART_DMAStop(huart); // ֹͣDMA����

		rt_ringbuffer_put(&uart_ringbuffer, uart_rx_dma_buffer, Size); // �����ݷ��뻷�λ�����

		memset(uart_rx_dma_buffer, 0, sizeof(uart_rx_dma_buffer)); // ���DMA������

		HAL_UARTEx_ReceiveToIdle_DMA(&huart1, uart_rx_dma_buffer, sizeof(uart_rx_dma_buffer)); // ��������DMA����

		__HAL_DMA_DISABLE_IT(&hdma_usart1_rx, DMA_IT_HT); // ���ð봫���ж�
	}
}

void parse_uart_command(uint8_t *buffer, uint16_t length)
{
	// 系统自检命令
	if (strcmp((char *)buffer, "test") == 0)
	{
		system_self_check(); // 执行系统自检
	}
	// 时间戳测试命令
	else if (strcmp((char *)buffer, "testtime") == 0)
	{
		test_unix_timestamp_conversion(); // 测试时间戳转换
	}
	// 隐藏测试命令
	else if (strncmp((char *)buffer, "testhide ", 9) == 0)
	{
		if (length > 9) {
			char *hex_data = (char *)buffer + 9; // 跳过"testhide "
			test_unhide_conversion(hex_data); // 测试解码
		} else {
			my_printf(&huart1, "Usage: testhide <hex_data>\r\n");
		}
	}
	// 测试采样周期测试
	else if (strcmp((char *)buffer, "testcycle") == 0)
	{
		test_cycle_persistence(); // 测试采样周期的持久化
	}
	// 测试时间精度命令
	else if (strcmp((char *)buffer, "testtime15") == 0)
	{
		test_15s_timing_accuracy(); // 测试15秒周期的时间精度
	}
	// ���Բ������ڶ�ʱ������
	else if (strcmp((char *)buffer, "testtiming") == 0)
	{
		test_timing_accuracy(); // ���Բ������ڶ�ʱ������
	}
	// ���Դ洢ϵͳ״̬
	else if (strcmp((char *)buffer, "teststorage") == 0)
	{
		test_storage_status(); // ���Դ洢ϵͳ״̬
	}
	// ���԰���2-4����
	else if (strcmp((char *)buffer, "testbuttons") == 0)
	{
		test_button_functions(); // ���԰���2-4����
	}
	// RTCʱ����������
	else if (strcmp((char *)buffer, "RTC Config") == 0)
	{
		handle_rtc_config_command(); // ����RTC Config�����߼�
	}
	// RTCʱ���ѯ����
	else if (strcmp((char *)buffer, "RTC now") == 0)
	{
		my_printf(&huart1, "Current Time:");
		rtc_print_current_time(); // ��ӡ��ǰRTCʱ��
	}
	// �����ļ���ȡ����
	else if (strcmp((char *)buffer, "conf") == 0)
	{
		handle_conf_command(); // ����conf�����߼�
	}
	// ����������������
	else if (strcmp((char *)buffer, "ratio") == 0)
	{
		handle_ratio_command(); // ����ratio�����߼�
	}
	// ��ֵ������������
	else if (strcmp((char *)buffer, "limit") == 0)
	{
		handle_limit_command(); // ����limit�����߼�
	}
	// ���ñ������� - ֧�ִ��ո�����
	else if (strcmp((char *)buffer, "config save") == 0)
	{
		handle_configsave_command(); // ����config save�����߼�
	}
	// ���ö�ȡ���� - ֧�ִ��ո�����
	else if (strcmp((char *)buffer, "config read") == 0)
	{
		handle_configread_command(); // ����config read�����߼�
	}
	// ���ñ������� - ����ԭ������ݱ���
	else if (strcmp((char *)buffer, "configsave") == 0)
	{
		handle_configsave_command(); // ����configsave�����߼�
	}
	// ���ö�ȡ���� - ����ԭ������ݱ���
	else if (strcmp((char *)buffer, "configread") == 0)
	{
		handle_configread_command(); // ����configread�����߼�
	}
	// ������������
	else if (strcmp((char *)buffer, "start") == 0)
	{
		handle_start_command(); // ����start�����߼�
	}
	// ����ֹͣ����
	else if (strcmp((char *)buffer, "stop") == 0)
	{
		handle_stop_command(); // ����stop�����߼�
	}
	// ����ģʽ����
	else if (strcmp((char *)buffer, "hide") == 0)
	{
		handle_hide_command(); // ����hide�����߼�
	}
	// ȡ����������
	else if (strcmp((char *)buffer, "unhide") == 0)
	{
		handle_unhide_command(); // ����unhide�����߼�
	}
	// ����ʽ���봦��
	else if (g_cmd_state != CMD_STATE_IDLE)
	{
		handle_interactive_input((char *)buffer); // ��������ʽ����
	}
}

void uart_task(void) // ���������� ����:�� ����:��
{
	uint16_t length; // ���ݳ���

	length = rt_ringbuffer_data_len(&uart_ringbuffer); // ��ȡ���λ��������ݳ���

	if (length > 0) // ��������Ҫ����
	{
		rt_ringbuffer_get(&uart_ringbuffer, uart_dma_buffer, length); // �ӻ��λ�������ȡ����

		parse_uart_command(uart_dma_buffer, length); // �������յ�������

		memset(uart_dma_buffer, 0, sizeof(uart_dma_buffer)); // ��ջ�����
	}

	handle_sampling_output(); // ���������������
}

// ����conf���� - ��TF����ȡconfig.ini�ļ����������ò���
void handle_conf_command(void)
{
	ini_config_t ini_config;
	config_params_t config_params;

	// ��¼����ִ�е���־
	data_storage_write_log("conf command");

	// ����config.ini�ļ�
	ini_status_t ini_status = ini_parse_file("config.ini", &ini_config);

	if (ini_status == INI_FILE_NOT_FOUND)
	{
		my_printf(&huart1, "config.ini file not found.\r\n");
		return;
	}

	if (ini_status != INI_OK)
	{
		my_printf(&huart1, "config.ini format error.\r\n");
		return;
	}

	// ����������Ƿ����
	if (!ini_config.ratio_found || !ini_config.limit_found)
	{
		my_printf(&huart1, "config.ini missing parameters.\r\n");
		return;
	}

	// ��֤������Χ
	if (config_validate_ratio(ini_config.ratio) != CONFIG_OK)
	{
		my_printf(&huart1, "ratio parameter out of range (0-100).\r\n");
		return;
	}

	if (config_validate_limit(ini_config.limit) != CONFIG_OK)
	{
		my_printf(&huart1, "limit parameter out of range (0-500).\r\n");
		return;
	}

	// ��ȡ��ǰ����
	if (config_get_params(&config_params) != CONFIG_OK)
	{
		my_printf(&huart1, "config system error.\r\n");
		return;
	}

	// �������ò���
	config_params.ratio = ini_config.ratio;
	config_params.limit = ini_config.limit;

	// ����������
	if (config_set_params(&config_params) != CONFIG_OK)
	{
		my_printf(&huart1, "config update failed.\r\n");
		return;
	}

	// ���浽Flash
	if (config_save_to_flash() != CONFIG_OK)
	{
		my_printf(&huart1, "config save to flash failed.\r\n");
		return;
	}

	// ������ý��
	my_printf(&huart1, "Ratio = %.1f\r\n", ini_config.ratio);
	my_printf(&huart1, "Limit = %.1f\r\n", ini_config.limit);
	my_printf(&huart1, "config read success\r\n");

	// ��¼�ɹ���־
	char log_msg[128];
	sprintf(log_msg, "config read success - ratio %.1f, limit %.1f", ini_config.ratio, ini_config.limit);
	data_storage_write_log(log_msg);
}

// ����ratio���� - ��ʾ��ǰ�����������ȴ��û�����
void handle_ratio_command(void)
{
	config_params_t config_params;

	// ��¼ratio�����־
	data_storage_write_log("ratio command");

	// ��ȡ��ǰ����
	if (config_get_params(&config_params) != CONFIG_OK)
	{
		my_printf(&huart1, "config system error.\r\n");
		return;
	}

	// ��ʾ��ǰratioֵ
	my_printf(&huart1, "Ratio =%.1f\r\n", config_params.ratio);

	// ��ʾ�û�����
	my_printf(&huart1, "Input value(0~100):\r\n");

	// ����״̬���ȴ�����
	g_cmd_state = CMD_STATE_WAIT_RATIO;
}

// ��������ʽ����
void handle_interactive_input(char *input)
{
	float value;
	config_params_t config_params;

	// �����������ֵ
	if (sscanf(input, "%f", &value) != 1)
	{
		my_printf(&huart1, "invalid input format.\r\n");
		g_cmd_state = CMD_STATE_IDLE;
		return;
	}

	// ��ȡ��ǰ����
	if (config_get_params(&config_params) != CONFIG_OK)
	{
		my_printf(&huart1, "config system error.\r\n");
		g_cmd_state = CMD_STATE_IDLE;
		return;
	}

	if (g_cmd_state == CMD_STATE_WAIT_RATIO)
	{
		// ��֤ratio������Χ
		if (config_validate_ratio(value) != CONFIG_OK)
		{
			my_printf(&huart1, "ratio invalid\r\n");
			my_printf(&huart1, "Ratio=%.1f\r\n", config_params.ratio);
		}
		else
		{
			// ����ratio����
			config_params.ratio = value;
			if (config_set_params(&config_params) == CONFIG_OK)
			{
				my_printf(&huart1, "ratio modified success\r\n");
				my_printf(&huart1, "Ratio=%.1f\r\n", value);

				// ��¼ratio���óɹ���־
				char log_msg[64];
				sprintf(log_msg, "ratio config success to %.1f", value);
				data_storage_write_log(log_msg);
			}
			else
			{
				my_printf(&huart1, "config update failed.\r\n");
			}
		}
		g_cmd_state = CMD_STATE_IDLE;
	}
	else if (g_cmd_state == CMD_STATE_WAIT_LIMIT)
	{
		// ��֤limit������Χ
		if (config_validate_limit(value) != CONFIG_OK)
		{
			my_printf(&huart1, "limit invalid\r\n");
			my_printf(&huart1, "limit = %.1f\r\n", config_params.limit);
		}
		else
		{
			// ����limit����
			config_params.limit = value;
			if (config_set_params(&config_params) == CONFIG_OK)
			{
				my_printf(&huart1, "limit modified success\r\n");
				my_printf(&huart1, "limit=%.1f\r\n", value);

				// ��¼limit���óɹ���־
				char log_msg[64];
				sprintf(log_msg, "limit config success to %.1f", value);
				data_storage_write_log(log_msg);
			}
			else
			{
				my_printf(&huart1, "config update failed.\r\n");
			}
		}
		g_cmd_state = CMD_STATE_IDLE;
	}
	else if (g_cmd_state == CMD_STATE_WAIT_RTC)
	{
		// ����RTCʱ������
		HAL_StatusTypeDef status = rtc_set_time_from_string(input);
		if (status == HAL_OK)
		{
			my_printf(&huart1, "RTC Config success\r\n");

			// ��ȡ���õ�ʱ�䲢��ʾ��׼��ʽ
			my_printf(&huart1, "Time: ");
			rtc_print_current_time();

			// ��¼RTC���óɹ���־
			char log_msg[128];
			sprintf(log_msg, "RTC config success to %s", input);
			data_storage_write_log(log_msg);
		}
		else
		{
			my_printf(&huart1, "RTC Config failed\r\n");
			my_printf(&huart1, "Invalid time format. Please use: 2025-01-01 15:00:10 or 2025��01��01��12:00:30\r\n");
		}
		g_cmd_state = CMD_STATE_IDLE;
	}
}

// ����limit���� - ��ʾ��ǰ��ֵ�������ȴ��û�����
void handle_limit_command(void)
{
	config_params_t config_params;

	// ��¼limit�����־
	data_storage_write_log("limit command");

	// ��ȡ��ǰ����
	if (config_get_params(&config_params) != CONFIG_OK)
	{
		my_printf(&huart1, "config system error.\r\n");
		return;
	}

	// ��ʾ��ǰlimitֵ
	my_printf(&huart1, "limit = %.1f\r\n", config_params.limit);

	// ��ʾ�û�����
	my_printf(&huart1, "Input value(0~500):\r\n");

	// ����״̬���ȴ�����
	g_cmd_state = CMD_STATE_WAIT_LIMIT;
}

// ����configsave���� - ���浱ǰ���õ�Flash
void handle_configsave_command(void)
{
	config_params_t config_params;

	// ��ȡ��ǰ���ò���
	if (config_get_params(&config_params) != CONFIG_OK)
	{
		my_printf(&huart1, "config system error.\r\n");
		return;
	}

	// ��ʾ��ǰ����
	my_printf(&huart1, "ratio:%.2f\r\n", config_params.ratio);
	my_printf(&huart1, "limit:%.2f\r\n", config_params.limit);

	// �������õ�Flash
	if (config_save_to_flash() != CONFIG_OK)
	{
		my_printf(&huart1, "save parameters to flash failed.\r\n");
		return;
	}

	// ����ɹ���Ϣ
	my_printf(&huart1, "save parameters to flash\r\n");
}

// ����configread���� - ��Flash��ȡ����
void handle_configread_command(void)
{
	config_params_t config_params;

	// ��Flash��ȡ����
	config_status_t status = config_load_from_flash();
	if (status != CONFIG_OK)
	{
		my_printf(&huart1, "read parameters from flash failed.\r\n");
		return;
	}

	// ����ɹ���Ϣ
	my_printf(&huart1, "read parameters from flash\r\n");

	// ��ȡ��ȡ������ò�������ʾ
	if (config_get_params(&config_params) != CONFIG_OK)
	{
		my_printf(&huart1, "config system error.\r\n");
		return;
	}

	// ��ʾ��ȡ������
	my_printf(&huart1, "ratio: %.2f\r\n", config_params.ratio);
	my_printf(&huart1, "limit: %.2f\r\n", config_params.limit);
}

// 处理start命令 - 启动采样
void handle_start_command(void)
{
	// 确保采样系统已初始化
	sampling_init();

	// 启动采样系统
	if (sampling_start() != SAMPLING_OK)
	{
		my_printf(&huart1, "sampling start failed.\r\n");
		return;
	}

	// 输出启动信息
	my_printf(&huart1, "Periodic Sampling\r\n");

	// 显示当前采样周期设置
	sampling_cycle_t cycle = sampling_get_cycle();
	my_printf(&huart1, "sample cycle: %ds\r\n", (int)cycle);

	// 启用采样输出功能
	g_sampling_output_enabled = 1;
	g_last_output_time = HAL_GetTick();

	// 记录启动日志
	char log_msg[64];
	sprintf(log_msg, "sample start - cycle %ds (command)", (int)cycle);
	data_storage_write_log(log_msg);
}

// ����stop���� - ֹͣ����
void handle_stop_command(void)
{
	// ȷ������ϵͳ�ѳ�ʼ��
	sampling_init();

	// ֹͣ����ϵͳ
	if (sampling_stop() != SAMPLING_OK)
	{
		my_printf(&huart1, "sampling stop failed.\r\n");
		return;
	}

	// ���ֹͣ��Ϣ
	my_printf(&huart1, "Periodic Sampling STOP\r\n");

	// ���ò����������
	g_sampling_output_enabled = 0;

	// ��¼ֹͣ��־
	data_storage_write_log("sample stop (command)");
}

// ���������������
void handle_sampling_output(void)
{
	// ����������Ƿ�ʹ��
	if (!g_sampling_output_enabled)
	{
		return;
	}

	// ������״̬
	if (sampling_get_state() != SAMPLING_ACTIVE)
	{
		return;
	}

	// ����Ƿ񵽴����ʱ��
	uint32_t current_time = HAL_GetTick();
	sampling_cycle_t cycle = sampling_get_cycle();
	uint32_t cycle_ms = cycle * 1000; // ת��Ϊ����

	// ʹ�û���Ԥ��ʱ��ļ��㷽ʽ������ۻ�����
	static uint32_t next_sample_time = 0;
	static sampling_cycle_t last_cycle = CYCLE_5S; // ��¼�ϴ�����

	// ��ʼ��Ԥ��ʱ�������������ڷ����仯ʱ����
	if (next_sample_time == 0 || cycle != last_cycle) {
		next_sample_time = current_time + cycle_ms;
		last_cycle = cycle;
	}

	if (current_time >= next_sample_time)
	{
		// ����Ԥ��ʱ��ۼ�������ʵ��ʱ��
		next_sample_time += cycle_ms;

		// ���¼�¼ʱ�䣨����ʾ��
		g_last_output_time = current_time;

		// ��ȡ��ǰʱ��
		RTC_TimeTypeDef current_rtc_time = {0};
		RTC_DateTypeDef current_rtc_date = {0};
		HAL_RTC_GetTime(&hrtc, &current_rtc_time, RTC_FORMAT_BIN);
		HAL_RTC_GetDate(&hrtc, &current_rtc_date, RTC_FORMAT_BIN);

		// ��ȡ��ǰ��ѹ
		float voltage = sampling_get_voltage();

		// ����Ƿ���
		uint8_t is_overlimit = sampling_check_overlimit();

		// ���������ʽ���в�ͬ�Ĵ���
		if (g_output_format == OUTPUT_FORMAT_HIDDEN)
		{
			// ����ģʽ�����HEX��ʽ
			uint32_t timestamp = convert_rtc_to_unix_timestamp(&current_rtc_time, &current_rtc_date);
			char hex_output[32];

			format_hex_output(timestamp, voltage, is_overlimit, hex_output);
			my_printf(&huart1, "%s\r\n", hex_output);
		}
		else
		{
			// ����ģʽ������ɶ���ʽ������������Ϣ
			if (is_overlimit)
			{
				// ��ȡlimit��ֵ
				config_params_t config_params;
				if (config_get_params(&config_params) == CONFIG_OK)
				{
					my_printf(&huart1, "%04d-%02d-%02d %02d:%02d:%02d ch0=%.2fV OverLimit(%.2f)!\r\n",
							  current_rtc_date.Year + 2000,
							  current_rtc_date.Month,
							  current_rtc_date.Date,
							  current_rtc_time.Hours,
							  current_rtc_time.Minutes,
							  current_rtc_time.Seconds,
							  voltage,
							  config_params.limit);
				}
				else
				{
					my_printf(&huart1, "%04d-%02d-%02d %02d:%02d:%02d ch0=%.2fV OverLimit!\r\n",
							  current_rtc_date.Year + 2000,
							  current_rtc_date.Month,
							  current_rtc_date.Date,
							  current_rtc_time.Hours,
							  current_rtc_time.Minutes,
							  current_rtc_time.Seconds,
							  voltage);
				}
			}
			else
			{
				my_printf(&huart1, "%04d-%02d-%02d %02d:%02d:%02d ch0=%.2fV\r\n",
						  current_rtc_date.Year + 2000,
						  current_rtc_date.Month,
						  current_rtc_date.Date,
						  current_rtc_time.Hours,
						  current_rtc_time.Minutes,
						  current_rtc_time.Seconds,
						  voltage);
			}
		}

		// ���ݴ洢���������������ʽѡ��洢��ʽ
		// ע�⣺���ݴ洢�������ʽ�޹أ�����Ҫ�洢
		if (g_output_format == OUTPUT_FORMAT_HIDDEN)
		{
			// ����ģʽ���洢��hideData�ļ��У����洢��sample
			data_storage_status_t result = data_storage_write_hidedata(voltage, is_overlimit);
			if (result != DATA_STORAGE_OK)
			{
				// �洢ʧ��ʱ�����������Ϣ���������������
				// my_printf(&huart1, "Warning: hideData storage failed\r\n");
			}
		}
		else
		{
			// ����ģʽ���洢��sample
			data_storage_status_t result = data_storage_write_sample(voltage);
			if (result != DATA_STORAGE_OK)
			{
				// �洢ʧ��ʱ�����������Ϣ���������������
				// my_printf(&huart1, "Warning: sample storage failed\r\n");
			}
		}

		// �������ݶ���洢��overLimit�ļ��У�����hideģʽ��
		if (is_overlimit)
		{
			config_params_t config_params;
			float limit_value = 0.0f;

			// ��ȡlimit��ֵ���ڴ洢
			if (config_get_params(&config_params) == CONFIG_OK)
			{
				limit_value = config_params.limit;
			}

			data_storage_status_t result = data_storage_write_overlimit(voltage, limit_value);
			if (result != DATA_STORAGE_OK)
			{
				// �洢ʧ��ʱ�����������Ϣ���������������
				// my_printf(&huart1, "Warning: overLimit storage failed\r\n");
			}
		}
	}
}

// ����hide���� - �л����������ģʽ
void handle_hide_command(void)
{
	// ����Ϊ���������ʽ
	g_output_format = OUTPUT_FORMAT_HIDDEN;

	// ��¼hideģʽ�л���־
	data_storage_write_log("hide data");

	// �����ȷ����Ϣ����������ģʽ�ľ�Ĭ����
	// my_printf(&huart1, "Output format switched to hidden.\r\n");
}

// ����unhide���� - �л����������ģʽ
void handle_unhide_command(void)
{
	// ����Ϊ���������ʽ
	g_output_format = OUTPUT_FORMAT_NORMAL;

	// ��¼unhideģʽ�л���־
	data_storage_write_log("unhide data");

	// �����ȷ����Ϣ�������������������
	// my_printf(&huart1, "Output format switched to normal.\r\n");
}

// ����RTC Config���� - ��ʾ�û�����ʱ�䲢�ȴ�����
void handle_rtc_config_command(void)
{
	// ��¼RTC Config�����־
	data_storage_write_log("RTC Config command");

	// ��ʾ�û�����ʱ��
	my_printf(&huart1, "Input Datetime\r\n");

	// ����״̬���ȴ�RTCʱ������
	g_cmd_state = CMD_STATE_WAIT_RTC;
}
