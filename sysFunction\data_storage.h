#ifndef DATA_STORAGE_H
#define DATA_STORAGE_H

#include "sys_common.h"

// 数据存储状态
typedef enum {
    DATA_STORAGE_OK = 0,
    DATA_STORAGE_ERROR,
    DATA_STORAGE_FILE_ERROR,
    DATA_STORAGE_DISK_FULL,
    DATA_STORAGE_INIT_FAILED
} data_storage_status_t;

// 存储类型
typedef enum {
    STORAGE_TYPE_SAMPLE = 0,
    STORAGE_TYPE_OVERLIMIT,
    STORAGE_TYPE_LOG,
    STORAGE_TYPE_HIDEDATA
} storage_type_t;

// 函数声明
sys_func_status_t data_storage_init(void);
sys_func_status_t data_storage_write_sample(const datetime_t* time, float voltage);
sys_func_status_t data_storage_write_overlimit(const datetime_t* time, float voltage, float limit);
sys_func_status_t data_storage_write_log(const char* log_message);
sys_func_status_t data_storage_write_hidedata(const datetime_t* time, float voltage, bool is_overlimit);

// 文件管理函数
sys_func_status_t data_storage_create_directories(void);
sys_func_status_t data_storage_get_boot_count(uint32_t* boot_count);
sys_func_status_t data_storage_increment_boot_count(void);
sys_func_status_t data_storage_generate_filename(storage_type_t type, char* filename, size_t buffer_size);

// 内部辅助函数
sys_func_status_t data_storage_write_to_file(const char* directory, const char* filename, const char* data);
sys_func_status_t data_storage_check_file_limit(const char* filepath, uint32_t max_records);

#endif // DATA_STORAGE_H
