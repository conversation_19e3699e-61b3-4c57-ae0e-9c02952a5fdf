// �ļ�����ini_parser.h
// ���ܣ�INI�ļ�������ͷ�ļ����ṩ�����ļ������Ͳ�����ȡ�ӿ�
// ���ߣ��״׵��ӹ�����
// ��Ȩ��Copyright (c) 2024 �״׵��ӹ�����. All rights reserved.

#ifndef __INI_PARSER_H__
#define __INI_PARSER_H__

#include "stdint.h" // ��׼���Ͷ���
#include "ff.h"     // FATFS�ļ�ϵͳ

typedef enum // INI������״̬ö��
{
    INI_OK = 0,             // �����ɹ�
    INI_ERROR = 1,          // һ�����
    INI_FILE_NOT_FOUND = 2, // �ļ�������
    INI_FORMAT_ERROR = 3,   // ��ʽ����
    INI_VALUE_ERROR = 4     // ��ֵת������
} ini_status_t;

typedef struct // INI配置数据结构体
{
    float ratio;         // 变比参数
    float limit;         // 阈值参数
    uint8_t ratio_found; // 是否找到ratio参数
    uint8_t limit_found; // 是否找到limit参数
} ini_config_t;

// INI解析器核心接口
ini_status_t ini_parse_file(const char *filename, ini_config_t *config); // 解析INI文件 参数:文件名,配置结构体指针 返回:解析状态
ini_status_t ini_parse_line(const char *line, ini_config_t *config);     // 解析单行数据 参数:行字符串,配置结构体指针 返回:解析状态

// 工具函数
ini_status_t ini_trim_string(char *str);                     // 去除字符串首尾空格 参数:字符串指针 返回:处理状态
ini_status_t ini_parse_float(const char *str, float *value); // 解析浮点数 参数:字符串,浮点数指针 返回:解析状态

#endif // __INI_PARSER_H__
