#ifndef CONFIG_MANAGEMENT_H
#define CONFIG_MANAGEMENT_H

#include "sys_common.h"

// 配置管理状态
typedef enum {
    CONFIG_MGMT_OK = 0,
    CONFIG_MGMT_ERROR,
    CONFIG_MGMT_FILE_NOT_FOUND,
    CONFIG_MGMT_INVALID_PARAM,
    CONFIG_MGMT_FLASH_ERROR
} config_mgmt_status_t;

// INI配置结构
typedef struct {
    float ratio;
    float limit;
    bool ratio_found;
    bool limit_found;
} ini_config_t;

// 函数声明
sys_func_status_t config_mgmt_read_from_tfcard(config_params_t* params);
sys_func_status_t config_mgmt_set_ratio(float ratio);
sys_func_status_t config_mgmt_set_limit(float limit);
sys_func_status_t config_mgmt_get_params(config_params_t* params);
sys_func_status_t config_mgmt_save_to_flash(void);
sys_func_status_t config_mgmt_load_from_flash(void);
sys_func_status_t config_mgmt_validate_ratio(float ratio);
sys_func_status_t config_mgmt_validate_limit(float limit);

// 命令处理函数
void config_mgmt_handle_conf_command(void);
void config_mgmt_handle_ratio_command(void);
void config_mgmt_handle_limit_command(void);
void config_mgmt_handle_config_save_command(void);
void config_mgmt_handle_config_read_command(void);

#endif // CONFIG_MANAGEMENT_H
