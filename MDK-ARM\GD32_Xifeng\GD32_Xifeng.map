Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32f429xx.o(RESET) refers to startup_stm32f429xx.o(STACK) for __initial_sp
    startup_stm32f429xx.o(RESET) refers to startup_stm32f429xx.o(.text) for Reset_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler) for DMA1_Stream5_IRQHandler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.ADC_IRQHandler) for ADC_IRQHandler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.TIM8_TRG_COM_TIM14_IRQHandler) for TIM8_TRG_COM_TIM14_IRQHandler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler) for DMA2_Stream0_IRQHandler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) for DMA2_Stream2_IRQHandler
    startup_stm32f429xx.o(.text) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    startup_stm32f429xx.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    main.o(i.SystemClock_Config) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.main) refers to stm32f4xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to dma.o(i.MX_DMA_Init) for MX_DMA_Init
    main.o(i.main) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(i.main) refers to adc.o(i.MX_ADC1_Init) for MX_ADC1_Init
    main.o(i.main) refers to dac.o(i.MX_DAC_Init) for MX_DAC_Init
    main.o(i.main) refers to tim.o(i.MX_TIM3_Init) for MX_TIM3_Init
    main.o(i.main) refers to tim.o(i.MX_TIM6_Init) for MX_TIM6_Init
    main.o(i.main) refers to tim.o(i.MX_TIM14_Init) for MX_TIM14_Init
    main.o(i.main) refers to i2c.o(i.MX_I2C1_Init) for MX_I2C1_Init
    main.o(i.main) refers to spi.o(i.MX_SPI2_Init) for MX_SPI2_Init
    main.o(i.main) refers to sdio.o(i.MX_SDIO_SD_Init) for MX_SDIO_SD_Init
    main.o(i.main) refers to fatfs.o(i.MX_FATFS_Init) for MX_FATFS_Init
    main.o(i.main) refers to rtc.o(i.MX_RTC_Init) for MX_RTC_Init
    main.o(i.main) refers to ringbuffer.o(i.rt_ringbuffer_init) for rt_ringbuffer_init
    main.o(i.main) refers to btn_app.o(i.app_btn_init) for app_btn_init
    main.o(i.main) refers to oled.o(i.OLED_Init) for OLED_Init
    main.o(i.main) refers to adc_app.o(i.adc_tim_dma_init) for adc_tim_dma_init
    main.o(i.main) refers to u8g2_d_setup.o(i.u8g2_Setup_ssd1306_i2c_128x32_univision_f) for u8g2_Setup_ssd1306_i2c_128x32_univision_f
    main.o(i.main) refers to u8x8_display.o(i.u8x8_InitDisplay) for u8x8_InitDisplay
    main.o(i.main) refers to u8x8_display.o(i.u8x8_SetPowerSave) for u8x8_SetPowerSave
    main.o(i.main) refers to gd25qxx.o(i.spi_flash_init) for spi_flash_init
    main.o(i.main) refers to device_id.o(i.device_id_init) for device_id_init
    main.o(i.main) refers to sampling_control.o(i.sampling_init) for sampling_init
    main.o(i.main) refers to data_storage.o(i.data_storage_init) for data_storage_init
    main.o(i.main) refers to data_storage_1.o(i.data_storage_init) for data_storage_init
    main.o(i.main) refers to scheduler.o(i.scheduler_init) for scheduler_init
    main.o(i.main) refers to device_id.o(i.device_id_print_startup_info) for device_id_print_startup_info
    main.o(i.main) refers to data_storage.o(i.data_storage_write_log) for data_storage_write_log
    main.o(i.main) refers to data_storage_1.o(i.data_storage_write_log) for data_storage_write_log
    main.o(i.main) refers to scheduler.o(i.scheduler_run) for scheduler_run
    main.o(i.main) refers to usart_app.o(.bss) for ringbuffer_pool
    main.o(i.main) refers to usart_app.o(.bss) for uart_ringbuffer
    main.o(i.main) refers to oled_app.o(i.u8g2_gpio_and_delay_stm32) for u8g2_gpio_and_delay_stm32
    main.o(i.main) refers to oled_app.o(i.u8x8_byte_hw_i2c) for u8x8_byte_hw_i2c
    main.o(i.main) refers to u8g2_setup.o(.constdata) for u8g2_cb_r0
    main.o(i.main) refers to main.o(.bss) for .bss
    gpio.o(i.MX_GPIO_Init) refers to memseta.o(.text) for __aeabi_memclr4
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    adc.o(i.HAL_ADC_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    adc.o(i.HAL_ADC_MspDeInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    adc.o(i.HAL_ADC_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    adc.o(i.HAL_ADC_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    adc.o(i.HAL_ADC_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    adc.o(i.HAL_ADC_MspInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    adc.o(i.HAL_ADC_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    adc.o(i.HAL_ADC_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    adc.o(i.HAL_ADC_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    adc.o(i.HAL_ADC_MspInit) refers to adc.o(.bss) for .bss
    adc.o(i.MX_ADC1_Init) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_Init) for HAL_ADC_Init
    adc.o(i.MX_ADC1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    adc.o(i.MX_ADC1_Init) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) for HAL_ADC_ConfigChannel
    adc.o(i.MX_ADC1_Init) refers to adc.o(.bss) for .bss
    dac.o(i.HAL_DAC_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    dac.o(i.HAL_DAC_MspDeInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    dac.o(i.HAL_DAC_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    dac.o(i.HAL_DAC_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    dac.o(i.HAL_DAC_MspInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    dac.o(i.HAL_DAC_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    dac.o(i.HAL_DAC_MspInit) refers to dac.o(.bss) for .bss
    dac.o(i.MX_DAC_Init) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_Init) for HAL_DAC_Init
    dac.o(i.MX_DAC_Init) refers to main.o(i.Error_Handler) for Error_Handler
    dac.o(i.MX_DAC_Init) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_ConfigChannel) for HAL_DAC_ConfigChannel
    dac.o(i.MX_DAC_Init) refers to dac.o(.bss) for .bss
    dma.o(i.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    dma.o(i.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    i2c.o(i.HAL_I2C_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    i2c.o(i.HAL_I2C_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    i2c.o(i.HAL_I2C_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    i2c.o(i.MX_I2C1_Init) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(i.MX_I2C1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    i2c.o(i.MX_I2C1_Init) refers to stm32f4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigAnalogFilter) for HAL_I2CEx_ConfigAnalogFilter
    i2c.o(i.MX_I2C1_Init) refers to stm32f4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigDigitalFilter) for HAL_I2CEx_ConfigDigitalFilter
    i2c.o(i.MX_I2C1_Init) refers to i2c.o(.bss) for .bss
    rtc.o(i.HAL_RTC_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    rtc.o(i.HAL_RTC_MspInit) refers to stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) for HAL_RCCEx_PeriphCLKConfig
    rtc.o(i.HAL_RTC_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    rtc.o(i.MX_RTC_Init) refers to memseta.o(.text) for __aeabi_memclr4
    rtc.o(i.MX_RTC_Init) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_Init) for HAL_RTC_Init
    rtc.o(i.MX_RTC_Init) refers to main.o(i.Error_Handler) for Error_Handler
    rtc.o(i.MX_RTC_Init) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_SetTime) for HAL_RTC_SetTime
    rtc.o(i.MX_RTC_Init) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_SetDate) for HAL_RTC_SetDate
    rtc.o(i.MX_RTC_Init) refers to rtc.o(.bss) for .bss
    sdio.o(i.HAL_SD_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    sdio.o(i.HAL_SD_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    sdio.o(i.HAL_SD_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    sdio.o(i.MX_SDIO_SD_Init) refers to sdio.o(.bss) for .bss
    spi.o(i.HAL_SPI_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    spi.o(i.HAL_SPI_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    spi.o(i.HAL_SPI_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    spi.o(i.MX_SPI2_Init) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_Init) for HAL_SPI_Init
    spi.o(i.MX_SPI2_Init) refers to main.o(i.Error_Handler) for Error_Handler
    spi.o(i.MX_SPI2_Init) refers to spi.o(.bss) for .bss
    tim.o(i.HAL_TIM_Base_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    tim.o(i.MX_TIM14_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM14_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM14_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM3_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM3_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM6_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM6_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM6_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM6_Init) refers to tim.o(.bss) for .bss
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(i.HAL_UART_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    usart.o(i.HAL_UART_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.HAL_UART_MspInit) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART1_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART1_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART1_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    usart.o(i.MX_USART1_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART1_UART_Init) refers to usart_app.o(.bss) for uart_rx_dma_buffer
    usart.o(i.fputc) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    usart.o(i.fputc) refers to usart.o(.bss) for .bss
    stm32f4xx_it.o(i.ADC_IRQHandler) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) for HAL_ADC_IRQHandler
    stm32f4xx_it.o(i.ADC_IRQHandler) refers to adc.o(.bss) for hadc1
    stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler) refers to dac.o(.bss) for hdma_dac1
    stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler) refers to adc.o(.bss) for hdma_adc1
    stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) refers to usart.o(.bss) for hdma_usart1_rx
    stm32f4xx_it.o(i.SysTick_Handler) refers to stm32f4xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32f4xx_it.o(i.TIM8_TRG_COM_TIM14_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32f4xx_it.o(i.TIM8_TRG_COM_TIM14_IRQHandler) refers to tim.o(.bss) for htim14
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to usart.o(.bss) for huart1
    bsp_driver_sd.o(i.BSP_SD_Erase) refers to stm32f4xx_hal_sd.o(i.HAL_SD_Erase) for HAL_SD_Erase
    bsp_driver_sd.o(i.BSP_SD_Erase) refers to sdio.o(.bss) for hsd
    bsp_driver_sd.o(i.BSP_SD_GetCardInfo) refers to stm32f4xx_hal_sd.o(i.HAL_SD_GetCardInfo) for HAL_SD_GetCardInfo
    bsp_driver_sd.o(i.BSP_SD_GetCardInfo) refers to sdio.o(.bss) for hsd
    bsp_driver_sd.o(i.BSP_SD_GetCardState) refers to stm32f4xx_hal_sd.o(i.HAL_SD_GetCardState) for HAL_SD_GetCardState
    bsp_driver_sd.o(i.BSP_SD_GetCardState) refers to sdio.o(.bss) for hsd
    bsp_driver_sd.o(i.BSP_SD_Init) refers to bsp_driver_sd.o(i.BSP_SD_IsDetected) for BSP_SD_IsDetected
    bsp_driver_sd.o(i.BSP_SD_Init) refers to stm32f4xx_hal_sd.o(i.HAL_SD_Init) for HAL_SD_Init
    bsp_driver_sd.o(i.BSP_SD_Init) refers to stm32f4xx_hal_sd.o(i.HAL_SD_ConfigWideBusOperation) for HAL_SD_ConfigWideBusOperation
    bsp_driver_sd.o(i.BSP_SD_Init) refers to sdio.o(.bss) for hsd
    bsp_driver_sd.o(i.BSP_SD_ReadBlocks) refers to stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks) for HAL_SD_ReadBlocks
    bsp_driver_sd.o(i.BSP_SD_ReadBlocks) refers to sdio.o(.bss) for hsd
    bsp_driver_sd.o(i.BSP_SD_ReadBlocks_DMA) refers to stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks_DMA) for HAL_SD_ReadBlocks_DMA
    bsp_driver_sd.o(i.BSP_SD_ReadBlocks_DMA) refers to sdio.o(.bss) for hsd
    bsp_driver_sd.o(i.BSP_SD_WriteBlocks) refers to stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks) for HAL_SD_WriteBlocks
    bsp_driver_sd.o(i.BSP_SD_WriteBlocks) refers to sdio.o(.bss) for hsd
    bsp_driver_sd.o(i.BSP_SD_WriteBlocks_DMA) refers to stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks_DMA) for HAL_SD_WriteBlocks_DMA
    bsp_driver_sd.o(i.BSP_SD_WriteBlocks_DMA) refers to sdio.o(.bss) for hsd
    bsp_driver_sd.o(i.HAL_SD_AbortCallback) refers to bsp_driver_sd.o(i.BSP_SD_AbortCallback) for BSP_SD_AbortCallback
    bsp_driver_sd.o(i.HAL_SD_RxCpltCallback) refers to bsp_driver_sd.o(i.BSP_SD_ReadCpltCallback) for BSP_SD_ReadCpltCallback
    bsp_driver_sd.o(i.HAL_SD_TxCpltCallback) refers to bsp_driver_sd.o(i.BSP_SD_WriteCpltCallback) for BSP_SD_WriteCpltCallback
    sd_diskio.o(i.SD_CheckStatus) refers to bsp_driver_sd.o(i.BSP_SD_GetCardState) for BSP_SD_GetCardState
    sd_diskio.o(i.SD_CheckStatus) refers to sd_diskio.o(.data) for .data
    sd_diskio.o(i.SD_initialize) refers to bsp_driver_sd.o(i.BSP_SD_Init) for BSP_SD_Init
    sd_diskio.o(i.SD_initialize) refers to sd_diskio.o(i.SD_CheckStatus) for SD_CheckStatus
    sd_diskio.o(i.SD_initialize) refers to sd_diskio.o(.data) for .data
    sd_diskio.o(i.SD_ioctl) refers to bsp_driver_sd.o(i.BSP_SD_GetCardInfo) for BSP_SD_GetCardInfo
    sd_diskio.o(i.SD_ioctl) refers to sd_diskio.o(.data) for .data
    sd_diskio.o(i.SD_read) refers to bsp_driver_sd.o(i.BSP_SD_ReadBlocks) for BSP_SD_ReadBlocks
    sd_diskio.o(i.SD_read) refers to bsp_driver_sd.o(i.BSP_SD_GetCardState) for BSP_SD_GetCardState
    sd_diskio.o(i.SD_status) refers to sd_diskio.o(i.SD_CheckStatus) for SD_CheckStatus
    sd_diskio.o(i.SD_write) refers to bsp_driver_sd.o(i.BSP_SD_WriteBlocks) for BSP_SD_WriteBlocks
    sd_diskio.o(i.SD_write) refers to bsp_driver_sd.o(i.BSP_SD_GetCardState) for BSP_SD_GetCardState
    sd_diskio.o(.constdata) refers to sd_diskio.o(i.SD_initialize) for SD_initialize
    sd_diskio.o(.constdata) refers to sd_diskio.o(i.SD_status) for SD_status
    sd_diskio.o(.constdata) refers to sd_diskio.o(i.SD_read) for SD_read
    sd_diskio.o(.constdata) refers to sd_diskio.o(i.SD_write) for SD_write
    sd_diskio.o(.constdata) refers to sd_diskio.o(i.SD_ioctl) for SD_ioctl
    fatfs.o(i.MX_FATFS_Init) refers to ff_gen_drv.o(i.FATFS_LinkDriver) for FATFS_LinkDriver
    fatfs.o(i.MX_FATFS_Init) refers to fatfs.o(.data) for .data
    fatfs.o(i.MX_FATFS_Init) refers to sd_diskio.o(.constdata) for SD_Driver
    diskio.o(i.disk_initialize) refers to ff_gen_drv.o(.bss) for disk
    diskio.o(i.disk_ioctl) refers to ff_gen_drv.o(.bss) for disk
    diskio.o(i.disk_read) refers to ff_gen_drv.o(.bss) for disk
    diskio.o(i.disk_status) refers to ff_gen_drv.o(.bss) for disk
    diskio.o(i.disk_write) refers to ff_gen_drv.o(.bss) for disk
    ff.o(i.check_fs) refers to ff.o(i.move_window) for move_window
    ff.o(i.check_fs) refers to ff.o(i.ld_word) for ld_word
    ff.o(i.check_fs) refers to ff.o(i.ld_dword) for ld_dword
    ff.o(i.chk_lock) refers to ff.o(.bss) for .bss
    ff.o(i.clear_lock) refers to ff.o(.bss) for .bss
    ff.o(i.cmp_lfn) refers to ff.o(i.ld_word) for ld_word
    ff.o(i.cmp_lfn) refers to cc936.o(i.ff_wtoupper) for ff_wtoupper
    ff.o(i.cmp_lfn) refers to ff.o(.constdata) for .constdata
    ff.o(i.create_chain) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.create_chain) refers to ff.o(i.put_fat) for put_fat
    ff.o(i.create_name) refers to cc936.o(i.ff_convert) for ff_convert
    ff.o(i.create_name) refers to ff.o(i.chk_chr) for chk_chr
    ff.o(i.create_name) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.create_name) refers to cc936.o(i.ff_wtoupper) for ff_wtoupper
    ff.o(i.dec_lock) refers to ff.o(.bss) for .bss
    ff.o(i.dir_alloc) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.dir_alloc) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_alloc) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.dir_find) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.dir_find) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_find) refers to ff.o(i.cmp_lfn) for cmp_lfn
    ff.o(i.dir_find) refers to ff.o(i.sum_sfn) for sum_sfn
    ff.o(i.dir_find) refers to ff.o(i.mem_cmp) for mem_cmp
    ff.o(i.dir_find) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.dir_next) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.dir_next) refers to ff.o(i.create_chain) for create_chain
    ff.o(i.dir_next) refers to ff.o(i.sync_window) for sync_window
    ff.o(i.dir_next) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.dir_next) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.dir_read) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_read) refers to ff.o(i.pick_lfn) for pick_lfn
    ff.o(i.dir_read) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.dir_read) refers to ff.o(i.sum_sfn) for sum_sfn
    ff.o(i.dir_register) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.dir_register) refers to ff.o(i.gen_numname) for gen_numname
    ff.o(i.dir_register) refers to ff.o(i.dir_find) for dir_find
    ff.o(i.dir_register) refers to ff.o(i.dir_alloc) for dir_alloc
    ff.o(i.dir_register) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.dir_register) refers to ff.o(i.sum_sfn) for sum_sfn
    ff.o(i.dir_register) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_register) refers to ff.o(i.put_lfn) for put_lfn
    ff.o(i.dir_register) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.dir_register) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.dir_remove) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.dir_remove) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_remove) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.dir_sdi) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.dir_sdi) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.enq_lock) refers to ff.o(.bss) for .bss
    ff.o(i.f_close) refers to ff.o(i.f_sync) for f_sync
    ff.o(i.f_close) refers to ff.o(i.validate) for validate
    ff.o(i.f_close) refers to ff.o(i.dec_lock) for dec_lock
    ff.o(i.f_closedir) refers to ff.o(i.validate) for validate
    ff.o(i.f_closedir) refers to ff.o(i.dec_lock) for dec_lock
    ff.o(i.f_getfree) refers to ff.o(i.find_volume) for find_volume
    ff.o(i.f_getfree) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.f_getfree) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_getfree) refers to ff.o(i.ld_dword) for ld_dword
    ff.o(i.f_getfree) refers to ff.o(i.ld_word) for ld_word
    ff.o(i.f_gets) refers to ff.o(i.f_read) for f_read
    ff.o(i.f_lseek) refers to ff.o(i.validate) for validate
    ff.o(i.f_lseek) refers to ff.o(i.clmt_clust) for clmt_clust
    ff.o(i.f_lseek) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_lseek) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_lseek) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.f_lseek) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.f_lseek) refers to ff.o(i.create_chain) for create_chain
    ff.o(i.f_mkdir) refers to ff.o(i.find_volume) for find_volume
    ff.o(i.f_mkdir) refers to syscall.o(i.ff_memalloc) for ff_memalloc
    ff.o(i.f_mkdir) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_mkdir) refers to ff.o(i.create_chain) for create_chain
    ff.o(i.f_mkdir) refers to ff.o(i.sync_window) for sync_window
    ff.o(i.f_mkdir) refers to fatfs.o(i.get_fattime) for get_fattime
    ff.o(i.f_mkdir) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_mkdir) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.f_mkdir) refers to ff.o(i.st_dword) for st_dword
    ff.o(i.f_mkdir) refers to ff.o(i.st_clust) for st_clust
    ff.o(i.f_mkdir) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_mkdir) refers to ff.o(i.dir_register) for dir_register
    ff.o(i.f_mkdir) refers to ff.o(i.remove_chain) for remove_chain
    ff.o(i.f_mkdir) refers to syscall.o(i.ff_memfree) for ff_memfree
    ff.o(i.f_mkdir) refers to ff.o(i.sync_fs) for sync_fs
    ff.o(i.f_mkfs) refers to ff.o(i.get_ldnumber) for get_ldnumber
    ff.o(i.f_mkfs) refers to diskio.o(i.disk_initialize) for disk_initialize
    ff.o(i.f_mkfs) refers to diskio.o(i.disk_ioctl) for disk_ioctl
    ff.o(i.f_mkfs) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.f_mkfs) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_mkfs) refers to ff.o(i.st_word) for st_word
    ff.o(i.f_mkfs) refers to ff.o(i.st_dword) for st_dword
    ff.o(i.f_mkfs) refers to fatfs.o(i.get_fattime) for get_fattime
    ff.o(i.f_mkfs) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_mkfs) refers to ff.o(.data) for .data
    ff.o(i.f_mkfs) refers to ff.o(.constdata) for .constdata
    ff.o(i.f_mount) refers to ff.o(i.get_ldnumber) for get_ldnumber
    ff.o(i.f_mount) refers to ff.o(i.clear_lock) for clear_lock
    ff.o(i.f_mount) refers to ff.o(i.find_volume) for find_volume
    ff.o(i.f_mount) refers to ff.o(.data) for .data
    ff.o(i.f_open) refers to ff.o(i.find_volume) for find_volume
    ff.o(i.f_open) refers to syscall.o(i.ff_memalloc) for ff_memalloc
    ff.o(i.f_open) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_open) refers to ff.o(i.chk_lock) for chk_lock
    ff.o(i.f_open) refers to ff.o(i.enq_lock) for enq_lock
    ff.o(i.f_open) refers to ff.o(i.dir_register) for dir_register
    ff.o(i.f_open) refers to fatfs.o(i.get_fattime) for get_fattime
    ff.o(i.f_open) refers to ff.o(i.st_dword) for st_dword
    ff.o(i.f_open) refers to ff.o(i.ld_clust) for ld_clust
    ff.o(i.f_open) refers to ff.o(i.st_clust) for st_clust
    ff.o(i.f_open) refers to ff.o(i.remove_chain) for remove_chain
    ff.o(i.f_open) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_open) refers to ff.o(i.inc_lock) for inc_lock
    ff.o(i.f_open) refers to ff.o(i.ld_dword) for ld_dword
    ff.o(i.f_open) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.f_open) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.f_open) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_open) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.f_open) refers to syscall.o(i.ff_memfree) for ff_memfree
    ff.o(i.f_opendir) refers to ff.o(i.find_volume) for find_volume
    ff.o(i.f_opendir) refers to syscall.o(i.ff_memalloc) for ff_memalloc
    ff.o(i.f_opendir) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_opendir) refers to ff.o(i.ld_clust) for ld_clust
    ff.o(i.f_opendir) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.f_opendir) refers to ff.o(i.inc_lock) for inc_lock
    ff.o(i.f_opendir) refers to syscall.o(i.ff_memfree) for ff_memfree
    ff.o(i.f_printf) refers to ff.o(i.putc_init) for putc_init
    ff.o(i.f_printf) refers to ff.o(i.putc_bfd) for putc_bfd
    ff.o(i.f_printf) refers to ff.o(i.putc_flush) for putc_flush
    ff.o(i.f_putc) refers to ff.o(i.putc_init) for putc_init
    ff.o(i.f_putc) refers to ff.o(i.putc_bfd) for putc_bfd
    ff.o(i.f_putc) refers to ff.o(i.putc_flush) for putc_flush
    ff.o(i.f_puts) refers to ff.o(i.putc_init) for putc_init
    ff.o(i.f_puts) refers to ff.o(i.putc_bfd) for putc_bfd
    ff.o(i.f_puts) refers to ff.o(i.putc_flush) for putc_flush
    ff.o(i.f_read) refers to ff.o(i.validate) for validate
    ff.o(i.f_read) refers to ff.o(i.clmt_clust) for clmt_clust
    ff.o(i.f_read) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.f_read) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_read) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.f_read) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_read) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_readdir) refers to ff.o(i.validate) for validate
    ff.o(i.f_readdir) refers to syscall.o(i.ff_memalloc) for ff_memalloc
    ff.o(i.f_readdir) refers to ff.o(i.dir_read) for dir_read
    ff.o(i.f_readdir) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.f_readdir) refers to ff.o(i.get_fileinfo) for get_fileinfo
    ff.o(i.f_readdir) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.f_readdir) refers to syscall.o(i.ff_memfree) for ff_memfree
    ff.o(i.f_rename) refers to ff.o(i.get_ldnumber) for get_ldnumber
    ff.o(i.f_rename) refers to ff.o(i.find_volume) for find_volume
    ff.o(i.f_rename) refers to syscall.o(i.ff_memalloc) for ff_memalloc
    ff.o(i.f_rename) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_rename) refers to ff.o(i.chk_lock) for chk_lock
    ff.o(i.f_rename) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_rename) refers to ff.o(i.dir_register) for dir_register
    ff.o(i.f_rename) refers to ff.o(i.ld_clust) for ld_clust
    ff.o(i.f_rename) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_rename) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_rename) refers to ff.o(i.st_clust) for st_clust
    ff.o(i.f_rename) refers to ff.o(i.dir_remove) for dir_remove
    ff.o(i.f_rename) refers to ff.o(i.sync_fs) for sync_fs
    ff.o(i.f_rename) refers to syscall.o(i.ff_memfree) for ff_memfree
    ff.o(i.f_stat) refers to ff.o(i.find_volume) for find_volume
    ff.o(i.f_stat) refers to syscall.o(i.ff_memalloc) for ff_memalloc
    ff.o(i.f_stat) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_stat) refers to ff.o(i.get_fileinfo) for get_fileinfo
    ff.o(i.f_stat) refers to syscall.o(i.ff_memfree) for ff_memfree
    ff.o(i.f_sync) refers to ff.o(i.validate) for validate
    ff.o(i.f_sync) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_sync) refers to fatfs.o(i.get_fattime) for get_fattime
    ff.o(i.f_sync) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_sync) refers to ff.o(i.st_clust) for st_clust
    ff.o(i.f_sync) refers to ff.o(i.st_dword) for st_dword
    ff.o(i.f_sync) refers to ff.o(i.st_word) for st_word
    ff.o(i.f_sync) refers to ff.o(i.sync_fs) for sync_fs
    ff.o(i.f_truncate) refers to ff.o(i.validate) for validate
    ff.o(i.f_truncate) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.f_truncate) refers to ff.o(i.remove_chain) for remove_chain
    ff.o(i.f_truncate) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_unlink) refers to ff.o(i.find_volume) for find_volume
    ff.o(i.f_unlink) refers to syscall.o(i.ff_memalloc) for ff_memalloc
    ff.o(i.f_unlink) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_unlink) refers to ff.o(i.chk_lock) for chk_lock
    ff.o(i.f_unlink) refers to ff.o(i.ld_clust) for ld_clust
    ff.o(i.f_unlink) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.f_unlink) refers to ff.o(i.dir_read) for dir_read
    ff.o(i.f_unlink) refers to ff.o(i.dir_remove) for dir_remove
    ff.o(i.f_unlink) refers to ff.o(i.remove_chain) for remove_chain
    ff.o(i.f_unlink) refers to ff.o(i.sync_fs) for sync_fs
    ff.o(i.f_unlink) refers to syscall.o(i.ff_memfree) for ff_memfree
    ff.o(i.f_write) refers to ff.o(i.validate) for validate
    ff.o(i.f_write) refers to ff.o(i.clmt_clust) for clmt_clust
    ff.o(i.f_write) refers to ff.o(i.create_chain) for create_chain
    ff.o(i.f_write) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_write) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_write) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_write) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.find_volume) refers to ff.o(i.get_ldnumber) for get_ldnumber
    ff.o(i.find_volume) refers to diskio.o(i.disk_status) for disk_status
    ff.o(i.find_volume) refers to diskio.o(i.disk_initialize) for disk_initialize
    ff.o(i.find_volume) refers to ff.o(i.check_fs) for check_fs
    ff.o(i.find_volume) refers to ff.o(i.ld_dword) for ld_dword
    ff.o(i.find_volume) refers to ff.o(i.ld_word) for ld_word
    ff.o(i.find_volume) refers to ff.o(i.move_window) for move_window
    ff.o(i.find_volume) refers to ff.o(i.clear_lock) for clear_lock
    ff.o(i.find_volume) refers to ff.o(.data) for .data
    ff.o(i.follow_path) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.follow_path) refers to ff.o(i.create_name) for create_name
    ff.o(i.follow_path) refers to ff.o(i.dir_find) for dir_find
    ff.o(i.follow_path) refers to ff.o(i.ld_clust) for ld_clust
    ff.o(i.gen_numname) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.get_fat) refers to ff.o(i.move_window) for move_window
    ff.o(i.get_fat) refers to ff.o(i.ld_word) for ld_word
    ff.o(i.get_fat) refers to ff.o(i.ld_dword) for ld_dword
    ff.o(i.get_fileinfo) refers to cc936.o(i.ff_convert) for ff_convert
    ff.o(i.get_fileinfo) refers to ff.o(i.ld_dword) for ld_dword
    ff.o(i.inc_lock) refers to ff.o(.bss) for .bss
    ff.o(i.ld_clust) refers to ff.o(i.ld_word) for ld_word
    ff.o(i.move_window) refers to ff.o(i.sync_window) for sync_window
    ff.o(i.move_window) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.pick_lfn) refers to ff.o(i.ld_word) for ld_word
    ff.o(i.pick_lfn) refers to ff.o(.constdata) for .constdata
    ff.o(i.put_fat) refers to ff.o(i.move_window) for move_window
    ff.o(i.put_fat) refers to ff.o(i.st_word) for st_word
    ff.o(i.put_fat) refers to ff.o(i.ld_dword) for ld_dword
    ff.o(i.put_fat) refers to ff.o(i.st_dword) for st_dword
    ff.o(i.put_lfn) refers to ff.o(i.st_word) for st_word
    ff.o(i.put_lfn) refers to ff.o(.constdata) for .constdata
    ff.o(i.putc_bfd) refers to ff.o(i.f_write) for f_write
    ff.o(i.putc_flush) refers to ff.o(i.f_write) for f_write
    ff.o(i.remove_chain) refers to ff.o(i.put_fat) for put_fat
    ff.o(i.remove_chain) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.st_clust) refers to ff.o(i.st_word) for st_word
    ff.o(i.sync_fs) refers to ff.o(i.sync_window) for sync_window
    ff.o(i.sync_fs) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.sync_fs) refers to ff.o(i.st_word) for st_word
    ff.o(i.sync_fs) refers to ff.o(i.st_dword) for st_dword
    ff.o(i.sync_fs) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.sync_fs) refers to diskio.o(i.disk_ioctl) for disk_ioctl
    ff.o(i.sync_window) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.validate) refers to diskio.o(i.disk_status) for disk_status
    ff_gen_drv.o(i.FATFS_GetAttachedDriversNbr) refers to ff_gen_drv.o(.bss) for .bss
    ff_gen_drv.o(i.FATFS_LinkDriver) refers to ff_gen_drv.o(i.FATFS_LinkDriverEx) for FATFS_LinkDriverEx
    ff_gen_drv.o(i.FATFS_LinkDriverEx) refers to ff_gen_drv.o(.bss) for .bss
    ff_gen_drv.o(i.FATFS_UnLinkDriver) refers to ff_gen_drv.o(i.FATFS_UnLinkDriverEx) for FATFS_UnLinkDriverEx
    ff_gen_drv.o(i.FATFS_UnLinkDriverEx) refers to ff_gen_drv.o(.bss) for .bss
    syscall.o(i.ff_memalloc) refers to malloc.o(i.malloc) for malloc
    syscall.o(i.ff_memfree) refers to malloc.o(i.free) for free
    cc936.o(i.ff_convert) refers to cc936.o(.constdata) for .constdata
    cc936.o(i.ff_wtoupper) refers to cc936.o(.constdata) for .constdata
    stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt) refers to adc_app.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f4xx_hal_adc.o(i.ADC_DMAError) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_DeInit) refers to adc.o(i.HAL_ADC_MspDeInit) for HAL_ADC_MspDeInit
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to adc_app.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback) for HAL_ADCEx_InjectedConvCpltCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback) for HAL_ADC_LevelOutOfWindowCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_Init) refers to adc.o(i.HAL_ADC_MspInit) for HAL_ADC_MspInit
    stm32f4xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32f4xx_hal_adc.o(i.ADC_Init) for ADC_Init
    stm32f4xx_hal_adc.o(i.HAL_ADC_PollForConversion) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_adc.o(i.HAL_ADC_PollForEvent) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt) for ADC_DMAConvCplt
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt) for ADC_DMAHalfConvCplt
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(i.ADC_DMAError) for ADC_DMAError
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAConvCplt) refers to adc_app.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAError) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAHalfConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAConvCplt) for ADC_MultiModeDMAConvCplt
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAHalfConvCplt) for ADC_MultiModeDMAHalfConvCplt
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAError) for ADC_MultiModeDMAError
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.constdata) for AHBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLSAI) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLSAI) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_AdvOBProgram) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) for FLASH_OB_DisableWRP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) for FLASH_OB_EnableWRP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) refers to stm32f4xx_hal_dma.o(.constdata) for .constdata
    stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam) for DMA_CheckFifoParam
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableOverDrive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableOverDrive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f4xx_hal.o(i.HAL_DeInit) refers to stm32f4xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_IncTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_InitTick) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal_dac.o(i.DAC_DMAConvCpltCh1) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_ConvCpltCallbackCh1) for HAL_DAC_ConvCpltCallbackCh1
    stm32f4xx_hal_dac.o(i.DAC_DMAErrorCh1) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_ErrorCallbackCh1) for HAL_DAC_ErrorCallbackCh1
    stm32f4xx_hal_dac.o(i.DAC_DMAHalfConvCpltCh1) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_ConvHalfCpltCallbackCh1) for HAL_DAC_ConvHalfCpltCallbackCh1
    stm32f4xx_hal_dac.o(i.HAL_DAC_DeInit) refers to dac.o(i.HAL_DAC_MspDeInit) for HAL_DAC_MspDeInit
    stm32f4xx_hal_dac.o(i.HAL_DAC_IRQHandler) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_DMAUnderrunCallbackCh1) for HAL_DAC_DMAUnderrunCallbackCh1
    stm32f4xx_hal_dac.o(i.HAL_DAC_IRQHandler) refers to stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_DMAUnderrunCallbackCh2) for HAL_DACEx_DMAUnderrunCallbackCh2
    stm32f4xx_hal_dac.o(i.HAL_DAC_Init) refers to dac.o(i.HAL_DAC_MspInit) for HAL_DAC_MspInit
    stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac_ex.o(i.DAC_DMAConvCpltCh2) for DAC_DMAConvCpltCh2
    stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac_ex.o(i.DAC_DMAHalfConvCpltCh2) for DAC_DMAHalfConvCpltCh2
    stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac_ex.o(i.DAC_DMAErrorCh2) for DAC_DMAErrorCh2
    stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac.o(i.DAC_DMAConvCpltCh1) for DAC_DMAConvCpltCh1
    stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac.o(i.DAC_DMAHalfConvCpltCh1) for DAC_DMAHalfConvCpltCh1
    stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac.o(i.DAC_DMAErrorCh1) for DAC_DMAErrorCh1
    stm32f4xx_hal_dac.o(i.HAL_DAC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_dac_ex.o(i.DAC_DMAConvCpltCh2) refers to stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ConvCpltCallbackCh2) for HAL_DACEx_ConvCpltCallbackCh2
    stm32f4xx_hal_dac_ex.o(i.DAC_DMAErrorCh2) refers to stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ErrorCallbackCh2) for HAL_DACEx_ErrorCallbackCh2
    stm32f4xx_hal_dac_ex.o(i.DAC_DMAHalfConvCpltCh2) refers to stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ConvHalfCpltCallbackCh2) for HAL_DACEx_ConvHalfCpltCallbackCh2
    stm32f4xx_hal_i2c.o(i.HAL_I2C_DeInit) refers to i2c.o(i.HAL_I2C_MspDeInit) for HAL_I2C_MspDeInit
    stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) for I2C_Slave_AF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Slave_ADDR) for I2C_Slave_ADDR
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Master_SB) for I2C_Master_SB
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Master_ADDR) for I2C_Master_ADDR
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) for I2C_MasterTransmit_TXE
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) for I2C_MasterTransmit_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) for I2C_MasterReceive_RXNE
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF) for I2C_MasterReceive_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) for I2C_Slave_STOPF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) refers to i2c.o(i.HAL_I2C_MspInit) for HAL_I2C_MspInit
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32f4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) for I2C_MasterRequestRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) for I2C_MasterRequestWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.I2C_DMAError) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_i2c.o(i.I2C_DMAError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) refers to stm32f4xx_hal_i2c.o(i.I2C_Flush_DR) for I2C_Flush_DR
    stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_Slave_ADDR) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_AddrCallback) for HAL_I2C_AddrCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f4xx_hal_i2c.o(i.I2C_Flush_DR) for I2C_Flush_DR
    stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetState) for HAL_DMA_GetState
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rtc.o(i.HAL_RTC_AlarmIRQHandler) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_AlarmAEventCallback) for HAL_RTC_AlarmAEventCallback
    stm32f4xx_hal_rtc.o(i.HAL_RTC_AlarmIRQHandler) refers to stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_AlarmBEventCallback) for HAL_RTCEx_AlarmBEventCallback
    stm32f4xx_hal_rtc.o(i.HAL_RTC_DeInit) refers to stm32f4xx_hal_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_hal_rtc.o(i.HAL_RTC_DeInit) refers to stm32f4xx_hal_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_hal_rtc.o(i.HAL_RTC_DeInit) refers to rtc.o(i.HAL_RTC_MspDeInit) for HAL_RTC_MspDeInit
    stm32f4xx_hal_rtc.o(i.HAL_RTC_DeactivateAlarm) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rtc.o(i.HAL_RTC_GetAlarm) refers to stm32f4xx_hal_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_hal_rtc.o(i.HAL_RTC_GetDate) refers to stm32f4xx_hal_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_hal_rtc.o(i.HAL_RTC_GetTime) refers to stm32f4xx_hal_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_hal_rtc.o(i.HAL_RTC_Init) refers to rtc.o(i.HAL_RTC_MspInit) for HAL_RTC_MspInit
    stm32f4xx_hal_rtc.o(i.HAL_RTC_Init) refers to stm32f4xx_hal_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_hal_rtc.o(i.HAL_RTC_Init) refers to stm32f4xx_hal_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_hal_rtc.o(i.HAL_RTC_PollForAlarmAEvent) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rtc.o(i.HAL_RTC_SetAlarm) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rtc.o(i.HAL_RTC_SetAlarm) refers to stm32f4xx_hal_rtc.o(i.RTC_ByteToBcd2) for RTC_ByteToBcd2
    stm32f4xx_hal_rtc.o(i.HAL_RTC_SetAlarm_IT) refers to stm32f4xx_hal_rtc.o(i.RTC_ByteToBcd2) for RTC_ByteToBcd2
    stm32f4xx_hal_rtc.o(i.HAL_RTC_SetAlarm_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rtc.o(i.HAL_RTC_SetDate) refers to stm32f4xx_hal_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_hal_rtc.o(i.HAL_RTC_SetDate) refers to stm32f4xx_hal_rtc.o(i.RTC_ByteToBcd2) for RTC_ByteToBcd2
    stm32f4xx_hal_rtc.o(i.HAL_RTC_SetDate) refers to stm32f4xx_hal_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_hal_rtc.o(i.HAL_RTC_SetTime) refers to stm32f4xx_hal_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_hal_rtc.o(i.HAL_RTC_SetTime) refers to stm32f4xx_hal_rtc.o(i.RTC_ByteToBcd2) for RTC_ByteToBcd2
    stm32f4xx_hal_rtc.o(i.HAL_RTC_SetTime) refers to stm32f4xx_hal_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_hal_rtc.o(i.HAL_RTC_WaitForSynchro) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rtc.o(i.RTC_EnterInitMode) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rtc.o(i.RTC_ExitInitMode) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_WaitForSynchro) for HAL_RTC_WaitForSynchro
    stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_DeactivateCoarseCalib) refers to stm32f4xx_hal_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_DeactivateCoarseCalib) refers to stm32f4xx_hal_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_DeactivateRefClock) refers to stm32f4xx_hal_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_DeactivateRefClock) refers to stm32f4xx_hal_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_DeactivateWakeUpTimer) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_GetTimeStamp) refers to stm32f4xx_hal_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_PollForAlarmBEvent) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_PollForTamper1Event) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_PollForTamper2Event) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_PollForTimeStampEvent) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_PollForWakeUpTimerEvent) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_SetCoarseCalib) refers to stm32f4xx_hal_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_SetCoarseCalib) refers to stm32f4xx_hal_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_SetRefClock) refers to stm32f4xx_hal_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_SetRefClock) refers to stm32f4xx_hal_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_SetSmoothCalib) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_SetSynchroShift) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_SetSynchroShift) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_WaitForSynchro) for HAL_RTC_WaitForSynchro
    stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_SetWakeUpTimer) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_SetWakeUpTimer_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_TamperTimeStampIRQHandler) refers to stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_TimeStampEventCallback) for HAL_RTCEx_TimeStampEventCallback
    stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_TamperTimeStampIRQHandler) refers to stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_Tamper1EventCallback) for HAL_RTCEx_Tamper1EventCallback
    stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_TamperTimeStampIRQHandler) refers to stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_Tamper2EventCallback) for HAL_RTCEx_Tamper2EventCallback
    stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_WakeUpTimerIRQHandler) refers to stm32f4xx_hal_rtc_ex.o(i.HAL_RTCEx_WakeUpTimerEventCallback) for HAL_RTCEx_WakeUpTimerEventCallback
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdAppCommand) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdAppCommand) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdAppOperCommand) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdAppOperCommand) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp3) for SDMMC_GetCmdResp3
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdBlockLength) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdBlockLength) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdBusWidth) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdBusWidth) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdErase) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdErase) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdEraseEndAdd) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdEraseEndAdd) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdEraseStartAdd) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdEraseStartAdd) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdGoIdleState) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdGoIdleState) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdOpCondition) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdOpCondition) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp3) for SDMMC_GetCmdResp3
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdOperCond) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdOperCond) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp7) for SDMMC_GetCmdResp7
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdReadMultiBlock) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdReadMultiBlock) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdReadSingleBlock) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdReadSingleBlock) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSDEraseEndAdd) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSDEraseEndAdd) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSDEraseStartAdd) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSDEraseStartAdd) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSelDesel) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSelDesel) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendCID) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendCID) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp2) for SDMMC_GetCmdResp2
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendCSD) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendCSD) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp2) for SDMMC_GetCmdResp2
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendEXTCSD) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendEXTCSD) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendSCR) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendSCR) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendStatus) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendStatus) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSetRelAdd) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSetRelAdd) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp6) for SDMMC_GetCmdResp6
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSetRelAddMmc) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSetRelAddMmc) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdStatusRegister) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdStatusRegister) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSwitch) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSwitch) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdWriteMultiBlock) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdWriteMultiBlock) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdWriteSingleBlock) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_SendCommand) for SDIO_SendCommand
    stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdWriteSingleBlock) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) for SDMMC_GetCmdResp1
    stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_GetResponse) for SDIO_GetResponse
    stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp1) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp2) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp3) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp6) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_GetResponse) for SDIO_GetResponse
    stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp6) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_ll_sdmmc.o(i.SDMMC_GetCmdResp7) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_sd.o(i.HAL_SD_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_sd.o(i.HAL_SD_Abort) refers to stm32f4xx_hal_sd.o(i.HAL_SD_GetCardState) for HAL_SD_GetCardState
    stm32f4xx_hal_sd.o(i.HAL_SD_Abort) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer) for SDMMC_CmdStopTransfer
    stm32f4xx_hal_sd.o(i.HAL_SD_Abort_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_sd.o(i.HAL_SD_Abort_IT) refers to stm32f4xx_hal_sd.o(i.HAL_SD_GetCardState) for HAL_SD_GetCardState
    stm32f4xx_hal_sd.o(i.HAL_SD_Abort_IT) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer) for SDMMC_CmdStopTransfer
    stm32f4xx_hal_sd.o(i.HAL_SD_Abort_IT) refers to bsp_driver_sd.o(i.HAL_SD_AbortCallback) for HAL_SD_AbortCallback
    stm32f4xx_hal_sd.o(i.HAL_SD_Abort_IT) refers to stm32f4xx_hal_sd.o(i.SD_DMATxAbort) for SD_DMATxAbort
    stm32f4xx_hal_sd.o(i.HAL_SD_Abort_IT) refers to stm32f4xx_hal_sd.o(i.SD_DMARxAbort) for SD_DMARxAbort
    stm32f4xx_hal_sd.o(i.HAL_SD_ConfigWideBusOperation) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_GetResponse) for SDIO_GetResponse
    stm32f4xx_hal_sd.o(i.HAL_SD_ConfigWideBusOperation) refers to stm32f4xx_hal_sd.o(i.SD_FindSCR) for SD_FindSCR
    stm32f4xx_hal_sd.o(i.HAL_SD_ConfigWideBusOperation) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdAppCommand) for SDMMC_CmdAppCommand
    stm32f4xx_hal_sd.o(i.HAL_SD_ConfigWideBusOperation) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdBusWidth) for SDMMC_CmdBusWidth
    stm32f4xx_hal_sd.o(i.HAL_SD_ConfigWideBusOperation) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_Init) for SDIO_Init
    stm32f4xx_hal_sd.o(i.HAL_SD_ConfigWideBusOperation) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdBlockLength) for SDMMC_CmdBlockLength
    stm32f4xx_hal_sd.o(i.HAL_SD_DeInit) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_PowerState_OFF) for SDIO_PowerState_OFF
    stm32f4xx_hal_sd.o(i.HAL_SD_DeInit) refers to sdio.o(i.HAL_SD_MspDeInit) for HAL_SD_MspDeInit
    stm32f4xx_hal_sd.o(i.HAL_SD_Erase) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_GetResponse) for SDIO_GetResponse
    stm32f4xx_hal_sd.o(i.HAL_SD_Erase) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSDEraseStartAdd) for SDMMC_CmdSDEraseStartAdd
    stm32f4xx_hal_sd.o(i.HAL_SD_Erase) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSDEraseEndAdd) for SDMMC_CmdSDEraseEndAdd
    stm32f4xx_hal_sd.o(i.HAL_SD_Erase) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdErase) for SDMMC_CmdErase
    stm32f4xx_hal_sd.o(i.HAL_SD_GetCardState) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendStatus) for SDMMC_CmdSendStatus
    stm32f4xx_hal_sd.o(i.HAL_SD_GetCardState) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_GetResponse) for SDIO_GetResponse
    stm32f4xx_hal_sd.o(i.HAL_SD_GetCardStatus) refers to stm32f4xx_hal_sd.o(i.SD_SendSDStatus) for SD_SendSDStatus
    stm32f4xx_hal_sd.o(i.HAL_SD_GetCardStatus) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdBlockLength) for SDMMC_CmdBlockLength
    stm32f4xx_hal_sd.o(i.HAL_SD_IRQHandler) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_ReadFIFO) for SDIO_ReadFIFO
    stm32f4xx_hal_sd.o(i.HAL_SD_IRQHandler) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer) for SDMMC_CmdStopTransfer
    stm32f4xx_hal_sd.o(i.HAL_SD_IRQHandler) refers to stm32f4xx_hal_sd.o(i.HAL_SD_ErrorCallback) for HAL_SD_ErrorCallback
    stm32f4xx_hal_sd.o(i.HAL_SD_IRQHandler) refers to bsp_driver_sd.o(i.HAL_SD_RxCpltCallback) for HAL_SD_RxCpltCallback
    stm32f4xx_hal_sd.o(i.HAL_SD_IRQHandler) refers to bsp_driver_sd.o(i.HAL_SD_TxCpltCallback) for HAL_SD_TxCpltCallback
    stm32f4xx_hal_sd.o(i.HAL_SD_IRQHandler) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_WriteFIFO) for SDIO_WriteFIFO
    stm32f4xx_hal_sd.o(i.HAL_SD_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_sd.o(i.HAL_SD_IRQHandler) refers to stm32f4xx_hal_sd.o(i.SD_DMATxAbort) for SD_DMATxAbort
    stm32f4xx_hal_sd.o(i.HAL_SD_IRQHandler) refers to stm32f4xx_hal_sd.o(i.SD_DMARxAbort) for SD_DMARxAbort
    stm32f4xx_hal_sd.o(i.HAL_SD_IRQHandler) refers to bsp_driver_sd.o(i.HAL_SD_AbortCallback) for HAL_SD_AbortCallback
    stm32f4xx_hal_sd.o(i.HAL_SD_Init) refers to sdio.o(i.HAL_SD_MspInit) for HAL_SD_MspInit
    stm32f4xx_hal_sd.o(i.HAL_SD_Init) refers to stm32f4xx_hal_sd.o(i.HAL_SD_InitCard) for HAL_SD_InitCard
    stm32f4xx_hal_sd.o(i.HAL_SD_InitCard) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_Init) for SDIO_Init
    stm32f4xx_hal_sd.o(i.HAL_SD_InitCard) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_PowerState_ON) for SDIO_PowerState_ON
    stm32f4xx_hal_sd.o(i.HAL_SD_InitCard) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    stm32f4xx_hal_sd.o(i.HAL_SD_InitCard) refers to stm32f4xx_hal_sd.o(i.SD_PowerON) for SD_PowerON
    stm32f4xx_hal_sd.o(i.HAL_SD_InitCard) refers to stm32f4xx_hal_sd.o(i.SD_InitCard) for SD_InitCard
    stm32f4xx_hal_sd.o(i.HAL_SD_InitCard) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdBlockLength) for SDMMC_CmdBlockLength
    stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_ConfigData) for SDIO_ConfigData
    stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdReadMultiBlock) for SDMMC_CmdReadMultiBlock
    stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdReadSingleBlock) for SDMMC_CmdReadSingleBlock
    stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_ReadFIFO) for SDIO_ReadFIFO
    stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer) for SDMMC_CmdStopTransfer
    stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks_DMA) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_ConfigData) for SDIO_ConfigData
    stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks_DMA) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdReadMultiBlock) for SDMMC_CmdReadMultiBlock
    stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks_DMA) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdReadSingleBlock) for SDMMC_CmdReadSingleBlock
    stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks_DMA) refers to stm32f4xx_hal_sd.o(i.SD_DMAReceiveCplt) for SD_DMAReceiveCplt
    stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks_DMA) refers to stm32f4xx_hal_sd.o(i.SD_DMAError) for SD_DMAError
    stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks_IT) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_ConfigData) for SDIO_ConfigData
    stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks_IT) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdReadMultiBlock) for SDMMC_CmdReadMultiBlock
    stm32f4xx_hal_sd.o(i.HAL_SD_ReadBlocks_IT) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdReadSingleBlock) for SDMMC_CmdReadSingleBlock
    stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_ConfigData) for SDIO_ConfigData
    stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdWriteMultiBlock) for SDMMC_CmdWriteMultiBlock
    stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdWriteSingleBlock) for SDMMC_CmdWriteSingleBlock
    stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_WriteFIFO) for SDIO_WriteFIFO
    stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer) for SDMMC_CmdStopTransfer
    stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks_DMA) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdWriteMultiBlock) for SDMMC_CmdWriteMultiBlock
    stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks_DMA) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdWriteSingleBlock) for SDMMC_CmdWriteSingleBlock
    stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks_DMA) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_ConfigData) for SDIO_ConfigData
    stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks_DMA) refers to stm32f4xx_hal_sd.o(i.SD_DMATransmitCplt) for SD_DMATransmitCplt
    stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks_DMA) refers to stm32f4xx_hal_sd.o(i.SD_DMAError) for SD_DMAError
    stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks_IT) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdWriteMultiBlock) for SDMMC_CmdWriteMultiBlock
    stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks_IT) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdWriteSingleBlock) for SDMMC_CmdWriteSingleBlock
    stm32f4xx_hal_sd.o(i.HAL_SD_WriteBlocks_IT) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_ConfigData) for SDIO_ConfigData
    stm32f4xx_hal_sd.o(i.SD_DMAError) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_sd.o(i.SD_DMAError) refers to stm32f4xx_hal_sd.o(i.HAL_SD_GetCardState) for HAL_SD_GetCardState
    stm32f4xx_hal_sd.o(i.SD_DMAError) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer) for SDMMC_CmdStopTransfer
    stm32f4xx_hal_sd.o(i.SD_DMAError) refers to stm32f4xx_hal_sd.o(i.HAL_SD_ErrorCallback) for HAL_SD_ErrorCallback
    stm32f4xx_hal_sd.o(i.SD_DMAReceiveCplt) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer) for SDMMC_CmdStopTransfer
    stm32f4xx_hal_sd.o(i.SD_DMAReceiveCplt) refers to stm32f4xx_hal_sd.o(i.HAL_SD_ErrorCallback) for HAL_SD_ErrorCallback
    stm32f4xx_hal_sd.o(i.SD_DMAReceiveCplt) refers to bsp_driver_sd.o(i.HAL_SD_RxCpltCallback) for HAL_SD_RxCpltCallback
    stm32f4xx_hal_sd.o(i.SD_DMARxAbort) refers to stm32f4xx_hal_sd.o(i.HAL_SD_GetCardState) for HAL_SD_GetCardState
    stm32f4xx_hal_sd.o(i.SD_DMARxAbort) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer) for SDMMC_CmdStopTransfer
    stm32f4xx_hal_sd.o(i.SD_DMARxAbort) refers to stm32f4xx_hal_sd.o(i.HAL_SD_ErrorCallback) for HAL_SD_ErrorCallback
    stm32f4xx_hal_sd.o(i.SD_DMARxAbort) refers to bsp_driver_sd.o(i.HAL_SD_AbortCallback) for HAL_SD_AbortCallback
    stm32f4xx_hal_sd.o(i.SD_DMATxAbort) refers to stm32f4xx_hal_sd.o(i.HAL_SD_GetCardState) for HAL_SD_GetCardState
    stm32f4xx_hal_sd.o(i.SD_DMATxAbort) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdStopTransfer) for SDMMC_CmdStopTransfer
    stm32f4xx_hal_sd.o(i.SD_DMATxAbort) refers to stm32f4xx_hal_sd.o(i.HAL_SD_ErrorCallback) for HAL_SD_ErrorCallback
    stm32f4xx_hal_sd.o(i.SD_DMATxAbort) refers to bsp_driver_sd.o(i.HAL_SD_AbortCallback) for HAL_SD_AbortCallback
    stm32f4xx_hal_sd.o(i.SD_FindSCR) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_sd.o(i.SD_FindSCR) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdBlockLength) for SDMMC_CmdBlockLength
    stm32f4xx_hal_sd.o(i.SD_FindSCR) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdAppCommand) for SDMMC_CmdAppCommand
    stm32f4xx_hal_sd.o(i.SD_FindSCR) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_ConfigData) for SDIO_ConfigData
    stm32f4xx_hal_sd.o(i.SD_FindSCR) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendSCR) for SDMMC_CmdSendSCR
    stm32f4xx_hal_sd.o(i.SD_FindSCR) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_ReadFIFO) for SDIO_ReadFIFO
    stm32f4xx_hal_sd.o(i.SD_InitCard) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_GetPowerState) for SDIO_GetPowerState
    stm32f4xx_hal_sd.o(i.SD_InitCard) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendCID) for SDMMC_CmdSendCID
    stm32f4xx_hal_sd.o(i.SD_InitCard) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_GetResponse) for SDIO_GetResponse
    stm32f4xx_hal_sd.o(i.SD_InitCard) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSetRelAdd) for SDMMC_CmdSetRelAdd
    stm32f4xx_hal_sd.o(i.SD_InitCard) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSendCSD) for SDMMC_CmdSendCSD
    stm32f4xx_hal_sd.o(i.SD_InitCard) refers to stm32f4xx_hal_sd.o(i.HAL_SD_GetCardCSD) for HAL_SD_GetCardCSD
    stm32f4xx_hal_sd.o(i.SD_InitCard) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdSelDesel) for SDMMC_CmdSelDesel
    stm32f4xx_hal_sd.o(i.SD_InitCard) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_Init) for SDIO_Init
    stm32f4xx_hal_sd.o(i.SD_PowerON) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdGoIdleState) for SDMMC_CmdGoIdleState
    stm32f4xx_hal_sd.o(i.SD_PowerON) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdOperCond) for SDMMC_CmdOperCond
    stm32f4xx_hal_sd.o(i.SD_PowerON) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdAppCommand) for SDMMC_CmdAppCommand
    stm32f4xx_hal_sd.o(i.SD_PowerON) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdAppOperCommand) for SDMMC_CmdAppOperCommand
    stm32f4xx_hal_sd.o(i.SD_PowerON) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_GetResponse) for SDIO_GetResponse
    stm32f4xx_hal_sd.o(i.SD_SendSDStatus) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_sd.o(i.SD_SendSDStatus) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_GetResponse) for SDIO_GetResponse
    stm32f4xx_hal_sd.o(i.SD_SendSDStatus) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdBlockLength) for SDMMC_CmdBlockLength
    stm32f4xx_hal_sd.o(i.SD_SendSDStatus) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdAppCommand) for SDMMC_CmdAppCommand
    stm32f4xx_hal_sd.o(i.SD_SendSDStatus) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_ConfigData) for SDIO_ConfigData
    stm32f4xx_hal_sd.o(i.SD_SendSDStatus) refers to stm32f4xx_ll_sdmmc.o(i.SDMMC_CmdStatusRegister) for SDMMC_CmdStatusRegister
    stm32f4xx_hal_sd.o(i.SD_SendSDStatus) refers to stm32f4xx_ll_sdmmc.o(i.SDIO_ReadFIFO) for SDIO_ReadFIFO
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32f4xx_hal_spi.o(i.SPI_AbortTx_ISR) for SPI_AbortTx_ISR
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32f4xx_hal_spi.o(i.SPI_AbortRx_ISR) for SPI_AbortRx_ISR
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(i.SPI_AbortTx_ISR) for SPI_AbortTx_ISR
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(i.SPI_AbortRx_ISR) for SPI_AbortRx_ISR
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(i.SPI_DMATxAbortCallback) for SPI_DMATxAbortCallback
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(i.SPI_DMARxAbortCallback) for SPI_DMARxAbortCallback
    stm32f4xx_hal_spi.o(i.HAL_SPI_DMAStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_spi.o(i.HAL_SPI_DeInit) refers to spi.o(i.HAL_SPI_MspDeInit) for HAL_SPI_MspDeInit
    stm32f4xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32f4xx_hal_spi.o(i.SPI_DMAAbortOnError) for SPI_DMAAbortOnError
    stm32f4xx_hal_spi.o(i.HAL_SPI_Init) refers to spi.o(i.HAL_SPI_MspInit) for HAL_SPI_MspInit
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) for HAL_SPI_TransmitReceive
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTransaction) for SPI_EndRxTransaction
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) for HAL_SPI_TransmitReceive_DMA
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) for SPI_DMAHalfReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) for SPI_DMAReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) for HAL_SPI_TransmitReceive_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_RxISR_16BIT) for SPI_RxISR_16BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_RxISR_8BIT) for SPI_RxISR_8BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt) for SPI_DMAHalfTransmitReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) for SPI_DMATransmitReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) for SPI_DMAHalfReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) for SPI_DMAReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_16BIT) for SPI_2linesRxISR_16BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_16BIT) for SPI_2linesTxISR_16BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_8BIT) for SPI_2linesRxISR_8BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_8BIT) for SPI_2linesTxISR_8BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt) for SPI_DMAHalfTransmitCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt) for SPI_DMATransmitCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_IT) refers to stm32f4xx_hal_spi.o(i.SPI_TxISR_16BIT) for SPI_TxISR_16BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_IT) refers to stm32f4xx_hal_spi.o(i.SPI_TxISR_8BIT) for SPI_TxISR_8BIT
    stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_16BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_8BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_16BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_8BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_AbortRx_ISR) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback) for HAL_SPI_TxRxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTransaction) for SPI_EndRxTransaction
    stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxCpltCallback) for HAL_SPI_TxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.SPI_DMAAbortOnError) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAError) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_RxHalfCpltCallback) for HAL_SPI_RxHalfCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxHalfCpltCallback) for HAL_SPI_TxHalfCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxRxHalfCpltCallback) for HAL_SPI_TxRxHalfCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTransaction) for SPI_EndRxTransaction
    stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f4xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxCpltCallback) for HAL_SPI_TxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback) for HAL_SPI_TxRxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATxAbortCallback) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATxAbortCallback) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.SPI_EndRxTransaction) refers to stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) refers to stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.SPI_RxISR_16BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) for SPI_CloseRx_ISR
    stm32f4xx_hal_spi.o(i.SPI_RxISR_8BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) for SPI_CloseRx_ISR
    stm32f4xx_hal_spi.o(i.SPI_TxISR_16BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) for SPI_CloseTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_TxISR_8BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) for SPI_CloseTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit) refers to tim.o(i.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to tim.o(i.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to btn_app.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAError) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) refers to btn_app.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to usart_app.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to usart_app.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to usart_app.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to usart_app.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to usart_app.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to usart_app.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data) for .data
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.constdata) for .constdata
    ebtn.o(i.bit_array_cmp) refers to memcmp.o(.text) for memcmp
    ebtn.o(i.ebtn_combo_btn_add_btn) refers to ebtn.o(i.ebtn_get_btn_index_by_key_id) for ebtn_get_btn_index_by_key_id
    ebtn.o(i.ebtn_combo_btn_add_btn) refers to ebtn.o(i.ebtn_combo_btn_add_btn_by_idx) for ebtn_combo_btn_add_btn_by_idx
    ebtn.o(i.ebtn_combo_btn_remove_btn) refers to ebtn.o(i.ebtn_get_btn_index_by_key_id) for ebtn_get_btn_index_by_key_id
    ebtn.o(i.ebtn_combo_btn_remove_btn) refers to ebtn.o(i.ebtn_combo_btn_remove_btn_by_idx) for ebtn_combo_btn_remove_btn_by_idx
    ebtn.o(i.ebtn_combo_register) refers to ebtn.o(.bss) for .bss
    ebtn.o(i.ebtn_get_btn_by_key_id) refers to ebtn.o(.bss) for .bss
    ebtn.o(i.ebtn_get_btn_index_by_btn) refers to ebtn.o(i.ebtn_get_btn_index_by_key_id) for ebtn_get_btn_index_by_key_id
    ebtn.o(i.ebtn_get_btn_index_by_btn_dyn) refers to ebtn.o(i.ebtn_get_btn_index_by_key_id) for ebtn_get_btn_index_by_key_id
    ebtn.o(i.ebtn_get_btn_index_by_key_id) refers to ebtn.o(.bss) for .bss
    ebtn.o(i.ebtn_get_config) refers to ebtn.o(.bss) for .bss
    ebtn.o(i.ebtn_get_total_btn_cnt) refers to ebtn.o(.bss) for .bss
    ebtn.o(i.ebtn_init) refers to memseta.o(.text) for __aeabi_memclr4
    ebtn.o(i.ebtn_init) refers to ebtn.o(.bss) for .bss
    ebtn.o(i.ebtn_is_in_process) refers to ebtn.o(i.ebtn_is_btn_in_process) for ebtn_is_btn_in_process
    ebtn.o(i.ebtn_is_in_process) refers to ebtn.o(.bss) for .bss
    ebtn.o(i.ebtn_process) refers to ebtn.o(i.bit_array_assign) for bit_array_assign
    ebtn.o(i.ebtn_process) refers to ebtn.o(i.ebtn_process_with_curr_state) for ebtn_process_with_curr_state
    ebtn.o(i.ebtn_process) refers to ebtn.o(.bss) for .bss
    ebtn.o(i.ebtn_process_btn) refers to ebtn.o(i.bit_array_get) for bit_array_get
    ebtn.o(i.ebtn_process_btn) refers to ebtn.o(i.prv_process_btn) for prv_process_btn
    ebtn.o(i.ebtn_process_btn_combo) refers to ebtn.o(i.bit_array_and) for bit_array_and
    ebtn.o(i.ebtn_process_btn_combo) refers to ebtn.o(i.bit_array_cmp) for bit_array_cmp
    ebtn.o(i.ebtn_process_btn_combo) refers to ebtn.o(i.prv_process_btn) for prv_process_btn
    ebtn.o(i.ebtn_process_with_curr_state) refers to memseta.o(.text) for __aeabi_memclr4
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.bit_array_and) for bit_array_and
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.bit_array_cmp) for bit_array_cmp
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.bit_array_or) for bit_array_or
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.bit_array_get) for bit_array_get
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.ebtn_process_btn) for ebtn_process_btn
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.ebtn_process_btn_combo) for ebtn_process_btn_combo
    ebtn.o(i.ebtn_process_with_curr_state) refers to memmovea.o(.text) for __aeabi_memcpy4
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(.bss) for .bss
    ebtn.o(i.ebtn_register) refers to ebtn.o(i.ebtn_get_total_btn_cnt) for ebtn_get_total_btn_cnt
    ebtn.o(i.ebtn_register) refers to ebtn.o(.bss) for .bss
    ebtn.o(i.ebtn_set_combo_suppress_threshold) refers to ebtn.o(.bss) for .bss
    ebtn.o(i.ebtn_set_config) refers to ebtn.o(.bss) for .bss
    ebtn.o(i.prv_get_combo_btn_by_key_id) refers to ebtn.o(.bss) for .bss
    ebtn.o(i.prv_process_btn) refers to ebtn.o(i.prv_get_combo_btn_by_key_id) for prv_get_combo_btn_by_key_id
    ebtn.o(i.prv_process_btn) refers to ebtn.o(i.bit_array_get) for bit_array_get
    ebtn.o(i.prv_process_btn) refers to ebtn.o(.bss) for .bss
    oled.o(i.OLED_Allfill) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Allfill) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_Display_Off) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Display_On) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Init) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_Init) refers to oled.o(.data) for .data
    oled.o(i.OLED_Set_Position) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowChar) refers to oled.o(.constdata) for .constdata
    oled.o(i.OLED_ShowFloat) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowHanzi) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowHanzi) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowHanzi) refers to oled.o(.constdata) for .constdata
    oled.o(i.OLED_ShowHzbig) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowHzbig) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowHzbig) refers to oled.o(.constdata) for .constdata
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowPic) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowPic) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowStr) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_Write_cmd) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    oled.o(i.OLED_Write_cmd) refers to i2c.o(.bss) for hi2c1
    oled.o(i.OLED_Write_data) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    oled.o(i.OLED_Write_data) refers to i2c.o(.bss) for hi2c1
    mui.o(i.mui_Draw) refers to mui.o(i.mui_loop_over_form) for mui_loop_over_form
    mui.o(i.mui_Draw) refers to mui.o(i.mui_task_draw) for mui_task_draw
    mui.o(i.mui_EnterForm) refers to mui.o(i.mui_LeaveForm) for mui_LeaveForm
    mui.o(i.mui_EnterForm) refers to mui.o(i.mui_loop_over_form) for mui_loop_over_form
    mui.o(i.mui_EnterForm) refers to mui.o(i.mui_NextField) for mui_NextField
    mui.o(i.mui_EnterForm) refers to mui.o(i.mui_send_cursor_enter_msg) for mui_send_cursor_enter_msg
    mui.o(i.mui_EnterForm) refers to mui.o(i.mui_task_form_start) for mui_task_form_start
    mui.o(i.mui_EnterForm) refers to mui.o(i.mui_task_find_first_cursor_uif) for mui_task_find_first_cursor_uif
    mui.o(i.mui_GetCurrentCursorFocusPosition) refers to mui.o(i.mui_loop_over_form) for mui_loop_over_form
    mui.o(i.mui_GetCurrentCursorFocusPosition) refers to mui.o(i.mui_task_get_current_cursor_focus_position) for mui_task_get_current_cursor_focus_position
    mui.o(i.mui_GetSelectableFieldOptionCnt) refers to mui.o(i.mui_fds_get_token_cnt) for mui_fds_get_token_cnt
    mui.o(i.mui_GetSelectableFieldTextOption) refers to mui.o(i.mui_fds_get_nth_token) for mui_fds_get_nth_token
    mui.o(i.mui_GotoForm) refers to mui.o(i.mui_EnterForm) for mui_EnterForm
    mui.o(i.mui_GotoForm) refers to mui.o(i.mui_fds_get_cmd_size) for mui_fds_get_cmd_size
    mui.o(i.mui_GotoFormAutoCursorPosition) refers to mui.o(i.mui_GotoForm) for mui_GotoForm
    mui.o(i.mui_Init) refers to memseta.o(.text) for __aeabi_memclr4
    mui.o(i.mui_LeaveForm) refers to mui.o(i.mui_send_cursor_msg) for mui_send_cursor_msg
    mui.o(i.mui_LeaveForm) refers to mui.o(i.mui_loop_over_form) for mui_loop_over_form
    mui.o(i.mui_LeaveForm) refers to mui.o(i.mui_task_form_end) for mui_task_form_end
    mui.o(i.mui_NextField) refers to mui.o(i.mui_send_cursor_msg) for mui_send_cursor_msg
    mui.o(i.mui_NextField) refers to mui.o(i.mui_loop_over_form) for mui_loop_over_form
    mui.o(i.mui_NextField) refers to mui.o(i.mui_send_cursor_enter_msg) for mui_send_cursor_enter_msg
    mui.o(i.mui_NextField) refers to mui.o(i.mui_task_find_next_cursor_uif) for mui_task_find_next_cursor_uif
    mui.o(i.mui_NextField) refers to mui.o(i.mui_task_find_first_cursor_uif) for mui_task_find_first_cursor_uif
    mui.o(i.mui_PrevField) refers to mui.o(i.mui_send_cursor_msg) for mui_send_cursor_msg
    mui.o(i.mui_PrevField) refers to mui.o(i.mui_loop_over_form) for mui_loop_over_form
    mui.o(i.mui_PrevField) refers to mui.o(i.mui_send_cursor_enter_msg) for mui_send_cursor_enter_msg
    mui.o(i.mui_PrevField) refers to mui.o(i.mui_task_find_prev_cursor_uif) for mui_task_find_prev_cursor_uif
    mui.o(i.mui_PrevField) refers to mui.o(i.mui_task_find_last_cursor_uif) for mui_task_find_last_cursor_uif
    mui.o(i.mui_RestoreForm) refers to mui.o(i.mui_GotoForm) for mui_GotoForm
    mui.o(i.mui_SaveForm) refers to mui.o(i.mui_GetCurrentCursorFocusPosition) for mui_GetCurrentCursorFocusPosition
    mui.o(i.mui_SaveForm) refers to mui.o(i.mui_SaveFormWithCursorPosition) for mui_SaveFormWithCursorPosition
    mui.o(i.mui_SendSelect) refers to mui.o(i.mui_send_cursor_msg) for mui_send_cursor_msg
    mui.o(i.mui_SendSelectWithExecuteOnSelectFieldSearch) refers to mui.o(i.mui_loop_over_form) for mui_loop_over_form
    mui.o(i.mui_SendSelectWithExecuteOnSelectFieldSearch) refers to mui.o(i.mui_send_cursor_msg) for mui_send_cursor_msg
    mui.o(i.mui_SendSelectWithExecuteOnSelectFieldSearch) refers to mui.o(i.mui_send_cursor_enter_msg) for mui_send_cursor_enter_msg
    mui.o(i.mui_SendSelectWithExecuteOnSelectFieldSearch) refers to mui.o(i.mui_task_find_execute_on_select_field) for mui_task_find_execute_on_select_field
    mui.o(i.mui_SendValueDecrement) refers to mui.o(i.mui_send_cursor_msg) for mui_send_cursor_msg
    mui.o(i.mui_SendValueIncrement) refers to mui.o(i.mui_send_cursor_msg) for mui_send_cursor_msg
    mui.o(i.mui_fds_first_token) refers to mui.o(i.mui_fds_get_cmd_size_without_text) for mui_fds_get_cmd_size_without_text
    mui.o(i.mui_fds_first_token) refers to mui.o(i.mui_fds_next_token) for mui_fds_next_token
    mui.o(i.mui_fds_get_cmd_size) refers to mui.o(i.mui_fds_get_cmd_size_without_text) for mui_fds_get_cmd_size_without_text
    mui.o(i.mui_fds_get_nth_token) refers to mui.o(i.mui_fds_first_token) for mui_fds_first_token
    mui.o(i.mui_fds_get_nth_token) refers to mui.o(i.mui_fds_next_token) for mui_fds_next_token
    mui.o(i.mui_fds_get_token_cnt) refers to mui.o(i.mui_fds_first_token) for mui_fds_first_token
    mui.o(i.mui_fds_get_token_cnt) refers to mui.o(i.mui_fds_next_token) for mui_fds_next_token
    mui.o(i.mui_loop_over_form) refers to mui.o(i.mui_fds_get_cmd_size) for mui_fds_get_cmd_size
    mui.o(i.mui_loop_over_form) refers to mui.o(i.mui_prepare_current_field) for mui_prepare_current_field
    mui.o(i.mui_prepare_current_field) refers to mui.o(i.mui_fds_get_cmd_size) for mui_fds_get_cmd_size
    mui.o(i.mui_send_cursor_enter_msg) refers to mui.o(i.mui_send_cursor_msg) for mui_send_cursor_msg
    mui.o(i.mui_send_cursor_msg) refers to mui.o(i.mui_prepare_current_field) for mui_prepare_current_field
    mui.o(i.mui_task_find_first_cursor_uif) refers to mui.o(i.mui_uif_is_cursor_selectable) for mui_uif_is_cursor_selectable
    mui.o(i.mui_task_find_last_cursor_uif) refers to mui.o(i.mui_uif_is_cursor_selectable) for mui_uif_is_cursor_selectable
    mui.o(i.mui_task_find_next_cursor_uif) refers to mui.o(i.mui_uif_is_cursor_selectable) for mui_uif_is_cursor_selectable
    mui.o(i.mui_task_find_prev_cursor_uif) refers to mui.o(i.mui_uif_is_cursor_selectable) for mui_uif_is_cursor_selectable
    mui.o(i.mui_task_get_current_cursor_focus_position) refers to mui.o(i.mui_uif_is_cursor_selectable) for mui_uif_is_cursor_selectable
    mui_u8g2.o(i.mui_hline) refers to u8g2_hvline.o(i.u8g2_DrawHLine) for u8g2_DrawHLine
    mui_u8g2.o(i.mui_u8g2_btn_back_w1_fi) refers to mui_u8g2.o(i.mui_get_x) for mui_get_x
    mui_u8g2.o(i.mui_u8g2_btn_back_w1_fi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_fi) for mui_u8g2_draw_button_fi
    mui_u8g2.o(i.mui_u8g2_btn_back_w1_fi) refers to mui.o(i.mui_RestoreForm) for mui_RestoreForm
    mui_u8g2.o(i.mui_u8g2_btn_back_w1_pi) refers to mui_u8g2.o(i.mui_get_x) for mui_get_x
    mui_u8g2.o(i.mui_u8g2_btn_back_w1_pi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pi) for mui_u8g2_draw_button_pi
    mui_u8g2.o(i.mui_u8g2_btn_back_w1_pi) refers to mui.o(i.mui_RestoreForm) for mui_RestoreForm
    mui_u8g2.o(i.mui_u8g2_btn_back_w2_fi) refers to mui_u8g2.o(i.mui_u8g2_get_fi_flags) for mui_u8g2_get_fi_flags
    mui_u8g2.o(i.mui_u8g2_btn_back_w2_fi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_utf) for mui_u8g2_draw_button_utf
    mui_u8g2.o(i.mui_u8g2_btn_back_w2_fi) refers to mui.o(i.mui_RestoreForm) for mui_RestoreForm
    mui_u8g2.o(i.mui_u8g2_btn_back_w2_if) refers to mui_u8g2.o(i.mui_u8g2_get_if_flags) for mui_u8g2_get_if_flags
    mui_u8g2.o(i.mui_u8g2_btn_back_w2_if) refers to mui_u8g2.o(i.mui_u8g2_draw_button_utf) for mui_u8g2_draw_button_utf
    mui_u8g2.o(i.mui_u8g2_btn_back_w2_if) refers to mui.o(i.mui_RestoreForm) for mui_RestoreForm
    mui_u8g2.o(i.mui_u8g2_btn_back_wm_fi) refers to mui_u8g2.o(i.mui_u8g2_get_fi_flags) for mui_u8g2_get_fi_flags
    mui_u8g2.o(i.mui_u8g2_btn_back_wm_fi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_utf) for mui_u8g2_draw_button_utf
    mui_u8g2.o(i.mui_u8g2_btn_back_wm_fi) refers to mui.o(i.mui_RestoreForm) for mui_RestoreForm
    mui_u8g2.o(i.mui_u8g2_btn_back_wm_if) refers to mui_u8g2.o(i.mui_u8g2_get_if_flags) for mui_u8g2_get_if_flags
    mui_u8g2.o(i.mui_u8g2_btn_back_wm_if) refers to mui_u8g2.o(i.mui_u8g2_draw_button_utf) for mui_u8g2_draw_button_utf
    mui_u8g2.o(i.mui_u8g2_btn_back_wm_if) refers to mui.o(i.mui_RestoreForm) for mui_RestoreForm
    mui_u8g2.o(i.mui_u8g2_btn_exit_wm_fi) refers to mui_u8g2.o(i.mui_u8g2_get_fi_flags) for mui_u8g2_get_fi_flags
    mui_u8g2.o(i.mui_u8g2_btn_exit_wm_fi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_utf) for mui_u8g2_draw_button_utf
    mui_u8g2.o(i.mui_u8g2_btn_exit_wm_fi) refers to mui.o(i.mui_SaveForm) for mui_SaveForm
    mui_u8g2.o(i.mui_u8g2_btn_exit_wm_fi) refers to mui.o(i.mui_LeaveForm) for mui_LeaveForm
    mui_u8g2.o(i.mui_u8g2_btn_goto_w1_fi) refers to mui_u8g2.o(i.mui_get_x) for mui_get_x
    mui_u8g2.o(i.mui_u8g2_btn_goto_w1_fi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_fi) for mui_u8g2_draw_button_fi
    mui_u8g2.o(i.mui_u8g2_btn_goto_w1_fi) refers to mui.o(i.mui_SaveForm) for mui_SaveForm
    mui_u8g2.o(i.mui_u8g2_btn_goto_w1_fi) refers to mui.o(i.mui_GotoFormAutoCursorPosition) for mui_GotoFormAutoCursorPosition
    mui_u8g2.o(i.mui_u8g2_btn_goto_w1_pi) refers to mui_u8g2.o(i.mui_get_x) for mui_get_x
    mui_u8g2.o(i.mui_u8g2_btn_goto_w1_pi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pi) for mui_u8g2_draw_button_pi
    mui_u8g2.o(i.mui_u8g2_btn_goto_w1_pi) refers to mui.o(i.mui_SaveForm) for mui_SaveForm
    mui_u8g2.o(i.mui_u8g2_btn_goto_w1_pi) refers to mui.o(i.mui_GotoFormAutoCursorPosition) for mui_GotoFormAutoCursorPosition
    mui_u8g2.o(i.mui_u8g2_btn_goto_w2_fi) refers to mui_u8g2.o(i.mui_u8g2_get_fi_flags) for mui_u8g2_get_fi_flags
    mui_u8g2.o(i.mui_u8g2_btn_goto_w2_fi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_utf) for mui_u8g2_draw_button_utf
    mui_u8g2.o(i.mui_u8g2_btn_goto_w2_fi) refers to mui.o(i.mui_SaveForm) for mui_SaveForm
    mui_u8g2.o(i.mui_u8g2_btn_goto_w2_fi) refers to mui.o(i.mui_GotoFormAutoCursorPosition) for mui_GotoFormAutoCursorPosition
    mui_u8g2.o(i.mui_u8g2_btn_goto_w2_if) refers to mui_u8g2.o(i.mui_u8g2_get_if_flags) for mui_u8g2_get_if_flags
    mui_u8g2.o(i.mui_u8g2_btn_goto_w2_if) refers to mui_u8g2.o(i.mui_u8g2_draw_button_utf) for mui_u8g2_draw_button_utf
    mui_u8g2.o(i.mui_u8g2_btn_goto_w2_if) refers to mui.o(i.mui_SaveForm) for mui_SaveForm
    mui_u8g2.o(i.mui_u8g2_btn_goto_w2_if) refers to mui.o(i.mui_GotoFormAutoCursorPosition) for mui_GotoFormAutoCursorPosition
    mui_u8g2.o(i.mui_u8g2_btn_goto_wm_fi) refers to mui_u8g2.o(i.mui_u8g2_get_fi_flags) for mui_u8g2_get_fi_flags
    mui_u8g2.o(i.mui_u8g2_btn_goto_wm_fi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_utf) for mui_u8g2_draw_button_utf
    mui_u8g2.o(i.mui_u8g2_btn_goto_wm_fi) refers to mui.o(i.mui_SaveForm) for mui_SaveForm
    mui_u8g2.o(i.mui_u8g2_btn_goto_wm_fi) refers to mui.o(i.mui_GotoFormAutoCursorPosition) for mui_GotoFormAutoCursorPosition
    mui_u8g2.o(i.mui_u8g2_btn_goto_wm_if) refers to mui_u8g2.o(i.mui_u8g2_get_if_flags) for mui_u8g2_get_if_flags
    mui_u8g2.o(i.mui_u8g2_btn_goto_wm_if) refers to mui_u8g2.o(i.mui_u8g2_draw_button_utf) for mui_u8g2_draw_button_utf
    mui_u8g2.o(i.mui_u8g2_btn_goto_wm_if) refers to mui.o(i.mui_SaveForm) for mui_SaveForm
    mui_u8g2.o(i.mui_u8g2_btn_goto_wm_if) refers to mui.o(i.mui_GotoFormAutoCursorPosition) for mui_GotoFormAutoCursorPosition
    mui_u8g2.o(i.mui_u8g2_draw_button_fi) refers to mui_u8g2.o(i.mui_u8g2_get_fi_flags) for mui_u8g2_get_fi_flags
    mui_u8g2.o(i.mui_u8g2_draw_button_fi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_utf) for mui_u8g2_draw_button_utf
    mui_u8g2.o(i.mui_u8g2_draw_button_if) refers to mui_u8g2.o(i.mui_u8g2_get_if_flags) for mui_u8g2_get_if_flags
    mui_u8g2.o(i.mui_u8g2_draw_button_if) refers to mui_u8g2.o(i.mui_u8g2_draw_button_utf) for mui_u8g2_draw_button_utf
    mui_u8g2.o(i.mui_u8g2_draw_button_pf) refers to mui_u8g2.o(i.mui_u8g2_get_pf_flags) for mui_u8g2_get_pf_flags
    mui_u8g2.o(i.mui_u8g2_draw_button_pf) refers to mui_u8g2.o(i.mui_u8g2_draw_button_utf) for mui_u8g2_draw_button_utf
    mui_u8g2.o(i.mui_u8g2_draw_button_pi) refers to mui_u8g2.o(i.mui_u8g2_get_pi_flags) for mui_u8g2_get_pi_flags
    mui_u8g2.o(i.mui_u8g2_draw_button_pi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_utf) for mui_u8g2_draw_button_utf
    mui_u8g2.o(i.mui_u8g2_draw_button_utf) refers to mui_u8g2.o(i.mui_get_x) for mui_get_x
    mui_u8g2.o(i.mui_u8g2_draw_button_utf) refers to u8g2_button.o(i.u8g2_DrawButtonUTF8) for u8g2_DrawButtonUTF8
    mui_u8g2.o(i.mui_u8g2_draw_text) refers to mui_u8g2.o(i.mui_get_x) for mui_get_x
    mui_u8g2.o(i.mui_u8g2_draw_text) refers to u8g2_font.o(i.u8g2_DrawUTF8) for u8g2_DrawUTF8
    mui_u8g2.o(i.mui_u8g2_goto_form_w1_pf) refers to mui_u8g2.o(i.mui_u8g2_u8_opt_child_mse_common) for mui_u8g2_u8_opt_child_mse_common
    mui_u8g2.o(i.mui_u8g2_goto_form_w1_pf) refers to mui.o(i.mui_GetSelectableFieldTextOption) for mui_GetSelectableFieldTextOption
    mui_u8g2.o(i.mui_u8g2_goto_form_w1_pf) refers to mui_u8g2.o(i.mui_get_x) for mui_get_x
    mui_u8g2.o(i.mui_u8g2_goto_form_w1_pf) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pf) for mui_u8g2_draw_button_pf
    mui_u8g2.o(i.mui_u8g2_goto_form_w1_pf) refers to mui.o(i.mui_SaveFormWithCursorPosition) for mui_SaveFormWithCursorPosition
    mui_u8g2.o(i.mui_u8g2_goto_form_w1_pf) refers to mui.o(i.mui_SaveCursorPosition) for mui_SaveCursorPosition
    mui_u8g2.o(i.mui_u8g2_goto_form_w1_pf) refers to mui.o(i.mui_GotoFormAutoCursorPosition) for mui_GotoFormAutoCursorPosition
    mui_u8g2.o(i.mui_u8g2_goto_form_w1_pi) refers to mui_u8g2.o(i.mui_u8g2_u8_opt_child_mse_common) for mui_u8g2_u8_opt_child_mse_common
    mui_u8g2.o(i.mui_u8g2_goto_form_w1_pi) refers to mui.o(i.mui_GetSelectableFieldTextOption) for mui_GetSelectableFieldTextOption
    mui_u8g2.o(i.mui_u8g2_goto_form_w1_pi) refers to mui_u8g2.o(i.mui_get_x) for mui_get_x
    mui_u8g2.o(i.mui_u8g2_goto_form_w1_pi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pi) for mui_u8g2_draw_button_pi
    mui_u8g2.o(i.mui_u8g2_goto_form_w1_pi) refers to mui.o(i.mui_SaveFormWithCursorPosition) for mui_SaveFormWithCursorPosition
    mui_u8g2.o(i.mui_u8g2_goto_form_w1_pi) refers to mui.o(i.mui_SaveCursorPosition) for mui_SaveCursorPosition
    mui_u8g2.o(i.mui_u8g2_goto_form_w1_pi) refers to mui.o(i.mui_GotoFormAutoCursorPosition) for mui_GotoFormAutoCursorPosition
    mui_u8g2.o(i.mui_u8g2_s8_min_max_wm_mse_pf) refers to mui_u8g2.o(i.mui_u8g2_s8_vmm_draw_wm_pf) for mui_u8g2_s8_vmm_draw_wm_pf
    mui_u8g2.o(i.mui_u8g2_s8_min_max_wm_mse_pi) refers to mui_u8g2.o(i.mui_u8g2_s8_vmm_draw_wm_pi) for mui_u8g2_s8_vmm_draw_wm_pi
    mui_u8g2.o(i.mui_u8g2_s8_min_max_wm_mud_pf) refers to mui_u8g2.o(i.mui_u8g2_s8_vmm_draw_wm_pf) for mui_u8g2_s8_vmm_draw_wm_pf
    mui_u8g2.o(i.mui_u8g2_s8_min_max_wm_mud_pi) refers to mui_u8g2.o(i.mui_u8g2_s8_vmm_draw_wm_pi) for mui_u8g2_s8_vmm_draw_wm_pi
    mui_u8g2.o(i.mui_u8g2_s8_vmm_draw_wm_pf) refers to u8x8_u8toa.o(i.u8x8_s8toa) for u8x8_s8toa
    mui_u8g2.o(i.mui_u8g2_s8_vmm_draw_wm_pf) refers to u8g2_font.o(i.u8g2_GetStrWidth) for u8g2_GetStrWidth
    mui_u8g2.o(i.mui_u8g2_s8_vmm_draw_wm_pf) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pf) for mui_u8g2_draw_button_pf
    mui_u8g2.o(i.mui_u8g2_s8_vmm_draw_wm_pi) refers to u8x8_u8toa.o(i.u8x8_s8toa) for u8x8_s8toa
    mui_u8g2.o(i.mui_u8g2_s8_vmm_draw_wm_pi) refers to u8g2_font.o(i.u8g2_GetStrWidth) for u8g2_GetStrWidth
    mui_u8g2.o(i.mui_u8g2_s8_vmm_draw_wm_pi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pi) for mui_u8g2_draw_button_pi
    mui_u8g2.o(i.mui_u8g2_s8_vmm_draw_wm_pi) refers to mui_u8g2.o(.constdata) for .constdata
    mui_u8g2.o(i.mui_u8g2_set_font_style_function) refers to u8g2_font.o(i.u8g2_SetFont) for u8g2_SetFont
    mui_u8g2.o(i.mui_u8g2_u16_list_child_mse_common) refers to mui.o(i.mui_RestoreForm) for mui_RestoreForm
    mui_u8g2.o(i.mui_u8g2_u16_list_child_mse_common) refers to mui_u8g2.o(i.mui_u8g2_handle_scroll_next_prev_events) for mui_u8g2_handle_scroll_next_prev_events
    mui_u8g2.o(i.mui_u8g2_u16_list_child_w1_pi) refers to mui_u8g2.o(i.mui_u8g2_u16_list_child_mse_common) for mui_u8g2_u16_list_child_mse_common
    mui_u8g2.o(i.mui_u8g2_u16_list_child_w1_pi) refers to mui_u8g2.o(i.mui_get_x) for mui_get_x
    mui_u8g2.o(i.mui_u8g2_u16_list_child_w1_pi) refers to mui_u8g2.o(i.u8g2_DrawValueMark) for u8g2_DrawValueMark
    mui_u8g2.o(i.mui_u8g2_u16_list_child_w1_pi) refers to u8g2_font.o(i.u8g2_DrawUTF8) for u8g2_DrawUTF8
    mui_u8g2.o(i.mui_u8g2_u16_list_child_w1_pi) refers to u8g2_button.o(i.u8g2_DrawButtonFrame) for u8g2_DrawButtonFrame
    mui_u8g2.o(i.mui_u8g2_u16_list_goto_w1_pi) refers to mui_u8g2.o(i.mui_u8g2_u16_list_child_mse_common) for mui_u8g2_u16_list_child_mse_common
    mui_u8g2.o(i.mui_u8g2_u16_list_goto_w1_pi) refers to mui_u8g2.o(i.mui_get_x) for mui_get_x
    mui_u8g2.o(i.mui_u8g2_u16_list_goto_w1_pi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pi) for mui_u8g2_draw_button_pi
    mui_u8g2.o(i.mui_u8g2_u16_list_goto_w1_pi) refers to mui.o(i.mui_SaveFormWithCursorPosition) for mui_SaveFormWithCursorPosition
    mui_u8g2.o(i.mui_u8g2_u16_list_goto_w1_pi) refers to mui.o(i.mui_SaveCursorPosition) for mui_SaveCursorPosition
    mui_u8g2.o(i.mui_u8g2_u16_list_goto_w1_pi) refers to mui.o(i.mui_GotoFormAutoCursorPosition) for mui_GotoFormAutoCursorPosition
    mui_u8g2.o(i.mui_u8g2_u16_list_line_wa_mse_pi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pi) for mui_u8g2_draw_button_pi
    mui_u8g2.o(i.mui_u8g2_u16_list_line_wa_mud_pi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pi) for mui_u8g2_draw_button_pi
    mui_u8g2.o(i.mui_u8g2_u16_list_parent_wm_pi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pi) for mui_u8g2_draw_button_pi
    mui_u8g2.o(i.mui_u8g2_u16_list_parent_wm_pi) refers to mui.o(i.mui_SaveForm) for mui_SaveForm
    mui_u8g2.o(i.mui_u8g2_u16_list_parent_wm_pi) refers to mui.o(i.mui_GotoForm) for mui_GotoForm
    mui_u8g2.o(i.mui_u8g2_u8_bar_draw_wm) refers to mui_u8g2.o(i.mui_get_x) for mui_get_x
    mui_u8g2.o(i.mui_u8g2_u8_bar_draw_wm) refers to u8g2_box.o(i.u8g2_DrawFrame) for u8g2_DrawFrame
    mui_u8g2.o(i.mui_u8g2_u8_bar_draw_wm) refers to u8g2_box.o(i.u8g2_DrawBox) for u8g2_DrawBox
    mui_u8g2.o(i.mui_u8g2_u8_bar_draw_wm) refers to u8x8_u8toa.o(i.u8x8_u8toa) for u8x8_u8toa
    mui_u8g2.o(i.mui_u8g2_u8_bar_draw_wm) refers to u8g2_font.o(i.u8g2_DrawStr) for u8g2_DrawStr
    mui_u8g2.o(i.mui_u8g2_u8_bar_draw_wm) refers to u8g2_font.o(i.u8g2_GetStrWidth) for u8g2_GetStrWidth
    mui_u8g2.o(i.mui_u8g2_u8_bar_draw_wm) refers to u8g2_button.o(i.u8g2_DrawButtonFrame) for u8g2_DrawButtonFrame
    mui_u8g2.o(i.mui_u8g2_u8_bar_wm_mse_pf) refers to mui_u8g2.o(i.mui_u8g2_u8_bar_mse_msg_handler) for mui_u8g2_u8_bar_mse_msg_handler
    mui_u8g2.o(i.mui_u8g2_u8_bar_wm_mse_pf) refers to mui_u8g2.o(i.mui_u8g2_get_pf_flags) for mui_u8g2_get_pf_flags
    mui_u8g2.o(i.mui_u8g2_u8_bar_wm_mse_pf) refers to mui_u8g2.o(i.mui_u8g2_u8_bar_draw_wm) for mui_u8g2_u8_bar_draw_wm
    mui_u8g2.o(i.mui_u8g2_u8_bar_wm_mse_pi) refers to mui_u8g2.o(i.mui_u8g2_u8_bar_mse_msg_handler) for mui_u8g2_u8_bar_mse_msg_handler
    mui_u8g2.o(i.mui_u8g2_u8_bar_wm_mse_pi) refers to mui_u8g2.o(i.mui_u8g2_get_pi_flags) for mui_u8g2_get_pi_flags
    mui_u8g2.o(i.mui_u8g2_u8_bar_wm_mse_pi) refers to mui_u8g2.o(i.mui_u8g2_u8_bar_draw_wm) for mui_u8g2_u8_bar_draw_wm
    mui_u8g2.o(i.mui_u8g2_u8_bar_wm_mud_pf) refers to mui_u8g2.o(i.mui_u8g2_u8_bar_mud_msg_handler) for mui_u8g2_u8_bar_mud_msg_handler
    mui_u8g2.o(i.mui_u8g2_u8_bar_wm_mud_pf) refers to mui_u8g2.o(i.mui_u8g2_get_pf_flags) for mui_u8g2_get_pf_flags
    mui_u8g2.o(i.mui_u8g2_u8_bar_wm_mud_pf) refers to mui_u8g2.o(i.mui_u8g2_u8_bar_draw_wm) for mui_u8g2_u8_bar_draw_wm
    mui_u8g2.o(i.mui_u8g2_u8_bar_wm_mud_pi) refers to mui_u8g2.o(i.mui_u8g2_u8_bar_mud_msg_handler) for mui_u8g2_u8_bar_mud_msg_handler
    mui_u8g2.o(i.mui_u8g2_u8_bar_wm_mud_pi) refers to mui_u8g2.o(i.mui_u8g2_get_pi_flags) for mui_u8g2_get_pi_flags
    mui_u8g2.o(i.mui_u8g2_u8_bar_wm_mud_pi) refers to mui_u8g2.o(i.mui_u8g2_u8_bar_draw_wm) for mui_u8g2_u8_bar_draw_wm
    mui_u8g2.o(i.mui_u8g2_u8_char_wm_mud_pi) refers to mui_u8g2.o(i.mui_is_valid_char) for mui_is_valid_char
    mui_u8g2.o(i.mui_u8g2_u8_char_wm_mud_pi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pi) for mui_u8g2_draw_button_pi
    mui_u8g2.o(i.mui_u8g2_u8_chkbox_wm_pi) refers to mui_u8g2.o(i.mui_get_x) for mui_get_x
    mui_u8g2.o(i.mui_u8g2_u8_chkbox_wm_pi) refers to mui_u8g2.o(i.u8g2_DrawCheckbox) for u8g2_DrawCheckbox
    mui_u8g2.o(i.mui_u8g2_u8_chkbox_wm_pi) refers to u8g2_font.o(i.u8g2_GetUTF8Width) for u8g2_GetUTF8Width
    mui_u8g2.o(i.mui_u8g2_u8_chkbox_wm_pi) refers to u8g2_font.o(i.u8g2_DrawUTF8) for u8g2_DrawUTF8
    mui_u8g2.o(i.mui_u8g2_u8_chkbox_wm_pi) refers to u8g2_button.o(i.u8g2_DrawButtonFrame) for u8g2_DrawButtonFrame
    mui_u8g2.o(i.mui_u8g2_u8_fixed_width_bar_wm_mse_pf) refers to mui_u8g2.o(i.mui_u8g2_u8_bar_mse_msg_handler) for mui_u8g2_u8_bar_mse_msg_handler
    mui_u8g2.o(i.mui_u8g2_u8_fixed_width_bar_wm_mse_pf) refers to mui_u8g2.o(i.mui_u8g2_get_pf_flags) for mui_u8g2_get_pf_flags
    mui_u8g2.o(i.mui_u8g2_u8_fixed_width_bar_wm_mse_pf) refers to mui_u8g2.o(i.mui_u8g2_u8_bar_draw_wm) for mui_u8g2_u8_bar_draw_wm
    mui_u8g2.o(i.mui_u8g2_u8_fixed_width_bar_wm_mse_pi) refers to mui_u8g2.o(i.mui_u8g2_u8_bar_mse_msg_handler) for mui_u8g2_u8_bar_mse_msg_handler
    mui_u8g2.o(i.mui_u8g2_u8_fixed_width_bar_wm_mse_pi) refers to mui_u8g2.o(i.mui_u8g2_get_pi_flags) for mui_u8g2_get_pi_flags
    mui_u8g2.o(i.mui_u8g2_u8_fixed_width_bar_wm_mse_pi) refers to mui_u8g2.o(i.mui_u8g2_u8_bar_draw_wm) for mui_u8g2_u8_bar_draw_wm
    mui_u8g2.o(i.mui_u8g2_u8_fixed_width_bar_wm_mud_pf) refers to mui_u8g2.o(i.mui_u8g2_u8_bar_mud_msg_handler) for mui_u8g2_u8_bar_mud_msg_handler
    mui_u8g2.o(i.mui_u8g2_u8_fixed_width_bar_wm_mud_pf) refers to mui_u8g2.o(i.mui_u8g2_get_pf_flags) for mui_u8g2_get_pf_flags
    mui_u8g2.o(i.mui_u8g2_u8_fixed_width_bar_wm_mud_pf) refers to mui_u8g2.o(i.mui_u8g2_u8_bar_draw_wm) for mui_u8g2_u8_bar_draw_wm
    mui_u8g2.o(i.mui_u8g2_u8_fixed_width_bar_wm_mud_pi) refers to mui_u8g2.o(i.mui_u8g2_u8_bar_mud_msg_handler) for mui_u8g2_u8_bar_mud_msg_handler
    mui_u8g2.o(i.mui_u8g2_u8_fixed_width_bar_wm_mud_pi) refers to mui_u8g2.o(i.mui_u8g2_get_pi_flags) for mui_u8g2_get_pi_flags
    mui_u8g2.o(i.mui_u8g2_u8_fixed_width_bar_wm_mud_pi) refers to mui_u8g2.o(i.mui_u8g2_u8_bar_draw_wm) for mui_u8g2_u8_bar_draw_wm
    mui_u8g2.o(i.mui_u8g2_u8_min_max_wm_mse_pf) refers to mui_u8g2.o(i.mui_u8g2_u8_vmm_draw_wm_pf) for mui_u8g2_u8_vmm_draw_wm_pf
    mui_u8g2.o(i.mui_u8g2_u8_min_max_wm_mse_pi) refers to mui_u8g2.o(i.mui_u8g2_u8_vmm_draw_wm_pi) for mui_u8g2_u8_vmm_draw_wm_pi
    mui_u8g2.o(i.mui_u8g2_u8_min_max_wm_mud_pf) refers to mui_u8g2.o(i.mui_u8g2_u8_vmm_draw_wm_pf) for mui_u8g2_u8_vmm_draw_wm_pf
    mui_u8g2.o(i.mui_u8g2_u8_min_max_wm_mud_pi) refers to mui_u8g2.o(i.mui_u8g2_u8_vmm_draw_wm_pi) for mui_u8g2_u8_vmm_draw_wm_pi
    mui_u8g2.o(i.mui_u8g2_u8_opt_child_mse_common) refers to mui.o(i.mui_GetSelectableFieldOptionCnt) for mui_GetSelectableFieldOptionCnt
    mui_u8g2.o(i.mui_u8g2_u8_opt_child_mse_common) refers to mui.o(i.mui_RestoreForm) for mui_RestoreForm
    mui_u8g2.o(i.mui_u8g2_u8_opt_child_mse_common) refers to mui_u8g2.o(i.mui_u8g2_handle_scroll_next_prev_events) for mui_u8g2_handle_scroll_next_prev_events
    mui_u8g2.o(i.mui_u8g2_u8_opt_child_wm_pi) refers to mui_u8g2.o(i.mui_u8g2_u8_opt_child_mse_common) for mui_u8g2_u8_opt_child_mse_common
    mui_u8g2.o(i.mui_u8g2_u8_opt_child_wm_pi) refers to mui_u8g2.o(i.mui_get_x) for mui_get_x
    mui_u8g2.o(i.mui_u8g2_u8_opt_child_wm_pi) refers to mui_u8g2.o(i.mui_u8g2_get_pi_flags) for mui_u8g2_get_pi_flags
    mui_u8g2.o(i.mui_u8g2_u8_opt_child_wm_pi) refers to mui.o(i.mui_GetSelectableFieldTextOption) for mui_GetSelectableFieldTextOption
    mui_u8g2.o(i.mui_u8g2_u8_opt_child_wm_pi) refers to u8g2_button.o(i.u8g2_DrawButtonUTF8) for u8g2_DrawButtonUTF8
    mui_u8g2.o(i.mui_u8g2_u8_opt_line_wa_mse_pf) refers to mui.o(i.mui_fds_get_nth_token) for mui_fds_get_nth_token
    mui_u8g2.o(i.mui_u8g2_u8_opt_line_wa_mse_pf) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pf) for mui_u8g2_draw_button_pf
    mui_u8g2.o(i.mui_u8g2_u8_opt_line_wa_mse_pf) refers to mui.o(i.mui_fds_get_token_cnt) for mui_fds_get_token_cnt
    mui_u8g2.o(i.mui_u8g2_u8_opt_line_wa_mse_pi) refers to mui.o(i.mui_fds_get_nth_token) for mui_fds_get_nth_token
    mui_u8g2.o(i.mui_u8g2_u8_opt_line_wa_mse_pi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pi) for mui_u8g2_draw_button_pi
    mui_u8g2.o(i.mui_u8g2_u8_opt_line_wa_mse_pi) refers to mui.o(i.mui_fds_get_token_cnt) for mui_fds_get_token_cnt
    mui_u8g2.o(i.mui_u8g2_u8_opt_line_wa_mud_pf) refers to mui.o(i.mui_fds_get_nth_token) for mui_fds_get_nth_token
    mui_u8g2.o(i.mui_u8g2_u8_opt_line_wa_mud_pf) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pf) for mui_u8g2_draw_button_pf
    mui_u8g2.o(i.mui_u8g2_u8_opt_line_wa_mud_pf) refers to mui.o(i.mui_fds_get_token_cnt) for mui_fds_get_token_cnt
    mui_u8g2.o(i.mui_u8g2_u8_opt_line_wa_mud_pi) refers to mui.o(i.mui_fds_get_nth_token) for mui_fds_get_nth_token
    mui_u8g2.o(i.mui_u8g2_u8_opt_line_wa_mud_pi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pi) for mui_u8g2_draw_button_pi
    mui_u8g2.o(i.mui_u8g2_u8_opt_line_wa_mud_pi) refers to mui.o(i.mui_fds_get_token_cnt) for mui_fds_get_token_cnt
    mui_u8g2.o(i.mui_u8g2_u8_opt_parent_wm_pi) refers to mui.o(i.mui_fds_get_nth_token) for mui_fds_get_nth_token
    mui_u8g2.o(i.mui_u8g2_u8_opt_parent_wm_pi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pi) for mui_u8g2_draw_button_pi
    mui_u8g2.o(i.mui_u8g2_u8_opt_parent_wm_pi) refers to mui.o(i.mui_SaveForm) for mui_SaveForm
    mui_u8g2.o(i.mui_u8g2_u8_opt_parent_wm_pi) refers to mui.o(i.mui_GotoForm) for mui_GotoForm
    mui_u8g2.o(i.mui_u8g2_u8_opt_radio_child_w1_pi) refers to mui_u8g2.o(i.mui_u8g2_u8_opt_child_mse_common) for mui_u8g2_u8_opt_child_mse_common
    mui_u8g2.o(i.mui_u8g2_u8_opt_radio_child_w1_pi) refers to mui_u8g2.o(i.mui_get_x) for mui_get_x
    mui_u8g2.o(i.mui_u8g2_u8_opt_radio_child_w1_pi) refers to mui_u8g2.o(i.u8g2_DrawValueMark) for u8g2_DrawValueMark
    mui_u8g2.o(i.mui_u8g2_u8_opt_radio_child_w1_pi) refers to mui.o(i.mui_GetSelectableFieldTextOption) for mui_GetSelectableFieldTextOption
    mui_u8g2.o(i.mui_u8g2_u8_opt_radio_child_w1_pi) refers to u8g2_font.o(i.u8g2_DrawUTF8) for u8g2_DrawUTF8
    mui_u8g2.o(i.mui_u8g2_u8_opt_radio_child_w1_pi) refers to u8g2_button.o(i.u8g2_DrawButtonFrame) for u8g2_DrawButtonFrame
    mui_u8g2.o(i.mui_u8g2_u8_opt_radio_child_wm_pi) refers to mui_u8g2.o(i.mui_u8g2_u8_opt_child_mse_common) for mui_u8g2_u8_opt_child_mse_common
    mui_u8g2.o(i.mui_u8g2_u8_opt_radio_child_wm_pi) refers to mui_u8g2.o(i.mui_get_x) for mui_get_x
    mui_u8g2.o(i.mui_u8g2_u8_opt_radio_child_wm_pi) refers to mui_u8g2.o(i.u8g2_DrawValueMark) for u8g2_DrawValueMark
    mui_u8g2.o(i.mui_u8g2_u8_opt_radio_child_wm_pi) refers to mui.o(i.mui_GetSelectableFieldTextOption) for mui_GetSelectableFieldTextOption
    mui_u8g2.o(i.mui_u8g2_u8_opt_radio_child_wm_pi) refers to u8g2_font.o(i.u8g2_GetUTF8Width) for u8g2_GetUTF8Width
    mui_u8g2.o(i.mui_u8g2_u8_opt_radio_child_wm_pi) refers to u8g2_font.o(i.u8g2_DrawUTF8) for u8g2_DrawUTF8
    mui_u8g2.o(i.mui_u8g2_u8_opt_radio_child_wm_pi) refers to u8g2_button.o(i.u8g2_DrawButtonFrame) for u8g2_DrawButtonFrame
    mui_u8g2.o(i.mui_u8g2_u8_radio_wm_pi) refers to mui_u8g2.o(i.mui_get_x) for mui_get_x
    mui_u8g2.o(i.mui_u8g2_u8_radio_wm_pi) refers to mui_u8g2.o(i.u8g2_DrawCheckbox) for u8g2_DrawCheckbox
    mui_u8g2.o(i.mui_u8g2_u8_radio_wm_pi) refers to u8g2_font.o(i.u8g2_GetUTF8Width) for u8g2_GetUTF8Width
    mui_u8g2.o(i.mui_u8g2_u8_radio_wm_pi) refers to u8g2_font.o(i.u8g2_DrawUTF8) for u8g2_DrawUTF8
    mui_u8g2.o(i.mui_u8g2_u8_radio_wm_pi) refers to u8g2_button.o(i.u8g2_DrawButtonFrame) for u8g2_DrawButtonFrame
    mui_u8g2.o(i.mui_u8g2_u8_vmm_draw_wm_pf) refers to u8x8_u8toa.o(i.u8x8_u8toa) for u8x8_u8toa
    mui_u8g2.o(i.mui_u8g2_u8_vmm_draw_wm_pf) refers to u8g2_font.o(i.u8g2_GetStrWidth) for u8g2_GetStrWidth
    mui_u8g2.o(i.mui_u8g2_u8_vmm_draw_wm_pf) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pf) for mui_u8g2_draw_button_pf
    mui_u8g2.o(i.mui_u8g2_u8_vmm_draw_wm_pi) refers to u8x8_u8toa.o(i.u8x8_u8toa) for u8x8_u8toa
    mui_u8g2.o(i.mui_u8g2_u8_vmm_draw_wm_pi) refers to u8g2_font.o(i.u8g2_GetStrWidth) for u8g2_GetStrWidth
    mui_u8g2.o(i.mui_u8g2_u8_vmm_draw_wm_pi) refers to mui_u8g2.o(i.mui_u8g2_draw_button_pi) for mui_u8g2_draw_button_pi
    mui_u8g2.o(i.u8g2_DrawCheckbox) refers to u8g2_box.o(i.u8g2_DrawFrame) for u8g2_DrawFrame
    mui_u8g2.o(i.u8g2_DrawCheckbox) refers to u8g2_box.o(i.u8g2_DrawBox) for u8g2_DrawBox
    mui_u8g2.o(i.u8g2_DrawValueMark) refers to u8g2_box.o(i.u8g2_DrawBox) for u8g2_DrawBox
    u8g2_arc.o(i.u8g2_DrawArc) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_arc.o(i.u8g2_DrawArc) refers to u8g2_arc.o(i.u8g2_draw_arc) for u8g2_draw_arc
    u8g2_arc.o(i.u8g2_draw_arc) refers to u8g2_hvline.o(i.u8g2_DrawPixel) for u8g2_DrawPixel
    u8g2_bitmap.o(i.u8g2_DrawBitmap) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_bitmap.o(i.u8g2_DrawBitmap) refers to u8g2_bitmap.o(i.u8g2_DrawHorizontalBitmap) for u8g2_DrawHorizontalBitmap
    u8g2_bitmap.o(i.u8g2_DrawHXBM) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_bitmap.o(i.u8g2_DrawHXBM) refers to u8g2_hvline.o(i.u8g2_DrawHVLine) for u8g2_DrawHVLine
    u8g2_bitmap.o(i.u8g2_DrawHXBMP) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_bitmap.o(i.u8g2_DrawHXBMP) refers to u8g2_hvline.o(i.u8g2_DrawHVLine) for u8g2_DrawHVLine
    u8g2_bitmap.o(i.u8g2_DrawHorizontalBitmap) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_bitmap.o(i.u8g2_DrawHorizontalBitmap) refers to u8g2_hvline.o(i.u8g2_DrawHVLine) for u8g2_DrawHVLine
    u8g2_bitmap.o(i.u8g2_DrawXBM) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_bitmap.o(i.u8g2_DrawXBM) refers to u8g2_bitmap.o(i.u8g2_DrawHXBM) for u8g2_DrawHXBM
    u8g2_bitmap.o(i.u8g2_DrawXBMP) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_bitmap.o(i.u8g2_DrawXBMP) refers to u8g2_bitmap.o(i.u8g2_DrawHXBMP) for u8g2_DrawHXBMP
    u8g2_box.o(i.u8g2_DrawBox) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_box.o(i.u8g2_DrawBox) refers to u8g2_hvline.o(i.u8g2_DrawHVLine) for u8g2_DrawHVLine
    u8g2_box.o(i.u8g2_DrawFrame) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_box.o(i.u8g2_DrawFrame) refers to u8g2_hvline.o(i.u8g2_DrawHVLine) for u8g2_DrawHVLine
    u8g2_box.o(i.u8g2_DrawRBox) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_box.o(i.u8g2_DrawRBox) refers to u8g2_circle.o(i.u8g2_DrawDisc) for u8g2_DrawDisc
    u8g2_box.o(i.u8g2_DrawRBox) refers to u8g2_box.o(i.u8g2_DrawBox) for u8g2_DrawBox
    u8g2_box.o(i.u8g2_DrawRFrame) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_box.o(i.u8g2_DrawRFrame) refers to u8g2_circle.o(i.u8g2_DrawCircle) for u8g2_DrawCircle
    u8g2_box.o(i.u8g2_DrawRFrame) refers to u8g2_hvline.o(i.u8g2_DrawHLine) for u8g2_DrawHLine
    u8g2_box.o(i.u8g2_DrawRFrame) refers to u8g2_hvline.o(i.u8g2_DrawVLine) for u8g2_DrawVLine
    u8g2_buffer.o(i.u8g2_ClearBuffer) refers to memseta.o(.text) for __aeabi_memclr
    u8g2_buffer.o(i.u8g2_FirstPage) refers to u8g2_buffer.o(i.u8g2_ClearBuffer) for u8g2_ClearBuffer
    u8g2_buffer.o(i.u8g2_FirstPage) refers to u8g2_buffer.o(i.u8g2_SetBufferCurrTileRow) for u8g2_SetBufferCurrTileRow
    u8g2_buffer.o(i.u8g2_NextPage) refers to u8g2_buffer.o(i.u8g2_send_buffer) for u8g2_send_buffer
    u8g2_buffer.o(i.u8g2_NextPage) refers to u8x8_display.o(i.u8x8_RefreshDisplay) for u8x8_RefreshDisplay
    u8g2_buffer.o(i.u8g2_NextPage) refers to u8g2_buffer.o(i.u8g2_ClearBuffer) for u8g2_ClearBuffer
    u8g2_buffer.o(i.u8g2_NextPage) refers to u8g2_buffer.o(i.u8g2_SetBufferCurrTileRow) for u8g2_SetBufferCurrTileRow
    u8g2_buffer.o(i.u8g2_SendBuffer) refers to u8g2_buffer.o(i.u8g2_send_buffer) for u8g2_send_buffer
    u8g2_buffer.o(i.u8g2_SendBuffer) refers to u8x8_display.o(i.u8x8_RefreshDisplay) for u8x8_RefreshDisplay
    u8g2_buffer.o(i.u8g2_UpdateDisplay) refers to u8g2_buffer.o(i.u8g2_send_buffer) for u8g2_send_buffer
    u8g2_buffer.o(i.u8g2_UpdateDisplayArea) refers to u8x8_display.o(i.u8x8_DrawTile) for u8x8_DrawTile
    u8g2_buffer.o(i.u8g2_WriteBufferPBM) refers to u8x8_capture.o(i.u8x8_capture_write_pbm_pre) for u8x8_capture_write_pbm_pre
    u8g2_buffer.o(i.u8g2_WriteBufferPBM) refers to u8x8_capture.o(i.u8x8_capture_write_pbm_buffer) for u8x8_capture_write_pbm_buffer
    u8g2_buffer.o(i.u8g2_WriteBufferPBM) refers to u8x8_capture.o(i.u8x8_capture_get_pixel_1) for u8x8_capture_get_pixel_1
    u8g2_buffer.o(i.u8g2_WriteBufferPBM2) refers to u8x8_capture.o(i.u8x8_capture_write_pbm_pre) for u8x8_capture_write_pbm_pre
    u8g2_buffer.o(i.u8g2_WriteBufferPBM2) refers to u8x8_capture.o(i.u8x8_capture_write_pbm_buffer) for u8x8_capture_write_pbm_buffer
    u8g2_buffer.o(i.u8g2_WriteBufferPBM2) refers to u8x8_capture.o(i.u8x8_capture_get_pixel_2) for u8x8_capture_get_pixel_2
    u8g2_buffer.o(i.u8g2_WriteBufferXBM) refers to u8x8_capture.o(i.u8x8_capture_write_xbm_pre) for u8x8_capture_write_xbm_pre
    u8g2_buffer.o(i.u8g2_WriteBufferXBM) refers to u8x8_capture.o(i.u8x8_capture_write_xbm_buffer) for u8x8_capture_write_xbm_buffer
    u8g2_buffer.o(i.u8g2_WriteBufferXBM) refers to u8x8_capture.o(i.u8x8_capture_get_pixel_1) for u8x8_capture_get_pixel_1
    u8g2_buffer.o(i.u8g2_WriteBufferXBM2) refers to u8x8_capture.o(i.u8x8_capture_write_xbm_pre) for u8x8_capture_write_xbm_pre
    u8g2_buffer.o(i.u8g2_WriteBufferXBM2) refers to u8x8_capture.o(i.u8x8_capture_write_xbm_buffer) for u8x8_capture_write_xbm_buffer
    u8g2_buffer.o(i.u8g2_WriteBufferXBM2) refers to u8x8_capture.o(i.u8x8_capture_get_pixel_2) for u8x8_capture_get_pixel_2
    u8g2_buffer.o(i.u8g2_send_buffer) refers to u8x8_display.o(i.u8x8_DrawTile) for u8x8_DrawTile
    u8g2_button.o(i.u8g2_DrawButtonFrame) refers to u8g2_hvline.o(i.u8g2_SetDrawColor) for u8g2_SetDrawColor
    u8g2_button.o(i.u8g2_DrawButtonFrame) refers to u8g2_box.o(i.u8g2_DrawFrame) for u8g2_DrawFrame
    u8g2_button.o(i.u8g2_DrawButtonFrame) refers to u8g2_hvline.o(i.u8g2_DrawHLine) for u8g2_DrawHLine
    u8g2_button.o(i.u8g2_DrawButtonFrame) refers to u8g2_hvline.o(i.u8g2_DrawVLine) for u8g2_DrawVLine
    u8g2_button.o(i.u8g2_DrawButtonFrame) refers to u8g2_box.o(i.u8g2_DrawBox) for u8g2_DrawBox
    u8g2_button.o(i.u8g2_DrawButtonUTF8) refers to u8g2_font.o(i.u8g2_GetUTF8Width) for u8g2_GetUTF8Width
    u8g2_button.o(i.u8g2_DrawButtonUTF8) refers to u8g2_font.o(i.u8g2_SetFontMode) for u8g2_SetFontMode
    u8g2_button.o(i.u8g2_DrawButtonUTF8) refers to u8g2_font.o(i.u8g2_DrawUTF8) for u8g2_DrawUTF8
    u8g2_button.o(i.u8g2_DrawButtonUTF8) refers to u8g2_button.o(i.u8g2_DrawButtonFrame) for u8g2_DrawButtonFrame
    u8g2_circle.o(i.u8g2_DrawCircle) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_circle.o(i.u8g2_DrawCircle) refers to u8g2_circle.o(i.u8g2_draw_circle) for u8g2_draw_circle
    u8g2_circle.o(i.u8g2_DrawDisc) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_circle.o(i.u8g2_DrawDisc) refers to u8g2_circle.o(i.u8g2_draw_disc) for u8g2_draw_disc
    u8g2_circle.o(i.u8g2_DrawEllipse) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_circle.o(i.u8g2_DrawEllipse) refers to u8g2_circle.o(i.u8g2_draw_ellipse) for u8g2_draw_ellipse
    u8g2_circle.o(i.u8g2_DrawFilledEllipse) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_circle.o(i.u8g2_DrawFilledEllipse) refers to u8g2_circle.o(i.u8g2_draw_filled_ellipse) for u8g2_draw_filled_ellipse
    u8g2_circle.o(i.u8g2_draw_circle) refers to u8g2_circle.o(i.u8g2_draw_circle_section) for u8g2_draw_circle_section
    u8g2_circle.o(i.u8g2_draw_circle_section) refers to u8g2_hvline.o(i.u8g2_DrawPixel) for u8g2_DrawPixel
    u8g2_circle.o(i.u8g2_draw_disc) refers to u8g2_circle.o(i.u8g2_draw_disc_section) for u8g2_draw_disc_section
    u8g2_circle.o(i.u8g2_draw_disc_section) refers to u8g2_hvline.o(i.u8g2_DrawVLine) for u8g2_DrawVLine
    u8g2_circle.o(i.u8g2_draw_ellipse) refers to u8g2_circle.o(i.u8g2_draw_ellipse_section) for u8g2_draw_ellipse_section
    u8g2_circle.o(i.u8g2_draw_ellipse_section) refers to u8g2_hvline.o(i.u8g2_DrawPixel) for u8g2_DrawPixel
    u8g2_circle.o(i.u8g2_draw_filled_ellipse) refers to u8g2_circle.o(i.u8g2_draw_filled_ellipse_section) for u8g2_draw_filled_ellipse_section
    u8g2_circle.o(i.u8g2_draw_filled_ellipse_section) refers to u8g2_hvline.o(i.u8g2_DrawVLine) for u8g2_DrawVLine
    u8g2_cleardisplay.o(i.u8g2_ClearDisplay) refers to u8g2_buffer.o(i.u8g2_FirstPage) for u8g2_FirstPage
    u8g2_cleardisplay.o(i.u8g2_ClearDisplay) refers to u8g2_buffer.o(i.u8g2_NextPage) for u8g2_NextPage
    u8g2_cleardisplay.o(i.u8g2_ClearDisplay) refers to u8g2_buffer.o(i.u8g2_SetBufferCurrTileRow) for u8g2_SetBufferCurrTileRow
    u8g2_d_memory.o(i.u8g2_m_16_4_f) refers to u8g2_d_memory.o(.bss) for .bss
    u8g2_d_setup.o(i.u8g2_Setup_ssd1306_i2c_128x32_univision_f) refers to u8x8_setup.o(i.u8x8_Setup) for u8x8_Setup
    u8g2_d_setup.o(i.u8g2_Setup_ssd1306_i2c_128x32_univision_f) refers to u8g2_d_memory.o(i.u8g2_m_16_4_f) for u8g2_m_16_4_f
    u8g2_d_setup.o(i.u8g2_Setup_ssd1306_i2c_128x32_univision_f) refers to u8g2_setup.o(i.u8g2_SetupBuffer) for u8g2_SetupBuffer
    u8g2_d_setup.o(i.u8g2_Setup_ssd1306_i2c_128x32_univision_f) refers to u8x8_cad.o(i.u8x8_cad_ssd13xx_fast_i2c) for u8x8_cad_ssd13xx_fast_i2c
    u8g2_d_setup.o(i.u8g2_Setup_ssd1306_i2c_128x32_univision_f) refers to u8x8_d_ssd1306_128x32.o(i.u8x8_d_ssd1306_128x32_univision) for u8x8_d_ssd1306_128x32_univision
    u8g2_d_setup.o(i.u8g2_Setup_ssd1306_i2c_128x32_univision_f) refers to u8g2_ll_hvline.o(i.u8g2_ll_hvline_vertical_top_lsb) for u8g2_ll_hvline_vertical_top_lsb
    u8g2_font.o(i.u8g2_DrawExtUTF8) refers to u8x8_8x8.o(i.u8x8_utf8_init) for u8x8_utf8_init
    u8g2_font.o(i.u8g2_DrawExtUTF8) refers to u8g2_font.o(i.u8g2_GetGlyphWidth) for u8g2_GetGlyphWidth
    u8g2_font.o(i.u8g2_DrawExtUTF8) refers to u8g2_kerning.o(i.u8g2_GetKerningByTable) for u8g2_GetKerningByTable
    u8g2_font.o(i.u8g2_DrawExtUTF8) refers to u8g2_font.o(i.u8g2_DrawGlyph) for u8g2_DrawGlyph
    u8g2_font.o(i.u8g2_DrawExtUTF8) refers to u8x8_8x8.o(i.u8x8_utf8_next) for u8x8_utf8_next
    u8g2_font.o(i.u8g2_DrawExtendedUTF8) refers to u8x8_8x8.o(i.u8x8_utf8_init) for u8x8_utf8_init
    u8g2_font.o(i.u8g2_DrawExtendedUTF8) refers to u8g2_font.o(i.u8g2_GetGlyphWidth) for u8g2_GetGlyphWidth
    u8g2_font.o(i.u8g2_DrawExtendedUTF8) refers to u8g2_kerning.o(i.u8g2_GetKerning) for u8g2_GetKerning
    u8g2_font.o(i.u8g2_DrawExtendedUTF8) refers to u8g2_font.o(i.u8g2_DrawGlyph) for u8g2_DrawGlyph
    u8g2_font.o(i.u8g2_DrawExtendedUTF8) refers to u8x8_8x8.o(i.u8x8_utf8_next) for u8x8_utf8_next
    u8g2_font.o(i.u8g2_DrawGlyph) refers to u8g2_font.o(i.u8g2_font_get_glyph_data) for u8g2_font_get_glyph_data
    u8g2_font.o(i.u8g2_DrawGlyph) refers to u8g2_font.o(i.u8g2_font_decode_glyph) for u8g2_font_decode_glyph
    u8g2_font.o(i.u8g2_DrawGlyphX2) refers to u8g2_font.o(i.u8g2_font_get_glyph_data) for u8g2_font_get_glyph_data
    u8g2_font.o(i.u8g2_DrawGlyphX2) refers to u8g2_font.o(i.u8g2_font_2x_decode_glyph) for u8g2_font_2x_decode_glyph
    u8g2_font.o(i.u8g2_DrawStr) refers to u8g2_font.o(i.u8g2_draw_string) for u8g2_draw_string
    u8g2_font.o(i.u8g2_DrawStr) refers to u8x8_8x8.o(i.u8x8_ascii_next) for u8x8_ascii_next
    u8g2_font.o(i.u8g2_DrawStrX2) refers to u8g2_font.o(i.u8g2_draw_string_2x) for u8g2_draw_string_2x
    u8g2_font.o(i.u8g2_DrawStrX2) refers to u8x8_8x8.o(i.u8x8_ascii_next) for u8x8_ascii_next
    u8g2_font.o(i.u8g2_DrawUTF8) refers to u8g2_font.o(i.u8g2_draw_string) for u8g2_draw_string
    u8g2_font.o(i.u8g2_DrawUTF8) refers to u8x8_8x8.o(i.u8x8_utf8_next) for u8x8_utf8_next
    u8g2_font.o(i.u8g2_DrawUTF8X2) refers to u8g2_font.o(i.u8g2_draw_string_2x) for u8g2_draw_string_2x
    u8g2_font.o(i.u8g2_DrawUTF8X2) refers to u8x8_8x8.o(i.u8x8_utf8_next) for u8x8_utf8_next
    u8g2_font.o(i.u8g2_GetFontSize) refers to u8g2_font.o(i.u8g2_font_get_word) for u8g2_font_get_word
    u8g2_font.o(i.u8g2_GetGlyphWidth) refers to u8g2_font.o(i.u8g2_font_get_glyph_data) for u8g2_font_get_glyph_data
    u8g2_font.o(i.u8g2_GetGlyphWidth) refers to u8g2_font.o(i.u8g2_font_setup_decode) for u8g2_font_setup_decode
    u8g2_font.o(i.u8g2_GetGlyphWidth) refers to u8g2_font.o(i.u8g2_font_decode_get_signed_bits) for u8g2_font_decode_get_signed_bits
    u8g2_font.o(i.u8g2_GetStrWidth) refers to u8g2_font.o(i.u8g2_string_width) for u8g2_string_width
    u8g2_font.o(i.u8g2_GetStrWidth) refers to u8x8_8x8.o(i.u8x8_ascii_next) for u8x8_ascii_next
    u8g2_font.o(i.u8g2_GetStrX) refers to u8g2_font.o(i.u8g2_font_get_glyph_data) for u8g2_font_get_glyph_data
    u8g2_font.o(i.u8g2_GetStrX) refers to u8g2_font.o(i.u8g2_font_setup_decode) for u8g2_font_setup_decode
    u8g2_font.o(i.u8g2_GetStrX) refers to u8g2_font.o(i.u8g2_font_decode_get_signed_bits) for u8g2_font_decode_get_signed_bits
    u8g2_font.o(i.u8g2_GetUTF8Width) refers to u8g2_font.o(i.u8g2_string_width) for u8g2_string_width
    u8g2_font.o(i.u8g2_GetUTF8Width) refers to u8x8_8x8.o(i.u8x8_utf8_next) for u8x8_utf8_next
    u8g2_font.o(i.u8g2_GetXOffsetGlyph) refers to u8g2_font.o(i.u8g2_GetGlyphWidth) for u8g2_GetGlyphWidth
    u8g2_font.o(i.u8g2_GetXOffsetUTF8) refers to u8x8_8x8.o(i.u8x8_utf8_init) for u8x8_utf8_init
    u8g2_font.o(i.u8g2_GetXOffsetUTF8) refers to u8x8_8x8.o(i.u8x8_utf8_next) for u8x8_utf8_next
    u8g2_font.o(i.u8g2_GetXOffsetUTF8) refers to u8g2_font.o(i.u8g2_GetXOffsetGlyph) for u8g2_GetXOffsetGlyph
    u8g2_font.o(i.u8g2_IsAllValidUTF8) refers to u8x8_8x8.o(i.u8x8_utf8_init) for u8x8_utf8_init
    u8g2_font.o(i.u8g2_IsAllValidUTF8) refers to u8g2_font.o(i.u8g2_font_get_glyph_data) for u8g2_font_get_glyph_data
    u8g2_font.o(i.u8g2_IsAllValidUTF8) refers to u8x8_8x8.o(i.u8x8_utf8_next) for u8x8_utf8_next
    u8g2_font.o(i.u8g2_IsGlyph) refers to u8g2_font.o(i.u8g2_font_get_glyph_data) for u8g2_font_get_glyph_data
    u8g2_font.o(i.u8g2_SetFont) refers to u8g2_font.o(i.u8g2_read_font_info) for u8g2_read_font_info
    u8g2_font.o(i.u8g2_SetFont) refers to u8g2_font.o(i.u8g2_UpdateRefHeight) for u8g2_UpdateRefHeight
    u8g2_font.o(i.u8g2_SetFontPosBaseline) refers to u8g2_font.o(i.u8g2_font_calc_vref_font) for u8g2_font_calc_vref_font
    u8g2_font.o(i.u8g2_SetFontPosBottom) refers to u8g2_font.o(i.u8g2_font_calc_vref_bottom) for u8g2_font_calc_vref_bottom
    u8g2_font.o(i.u8g2_SetFontPosCenter) refers to u8g2_font.o(i.u8g2_font_calc_vref_center) for u8g2_font_calc_vref_center
    u8g2_font.o(i.u8g2_SetFontPosTop) refers to u8g2_font.o(i.u8g2_font_calc_vref_top) for u8g2_font_calc_vref_top
    u8g2_font.o(i.u8g2_SetFontRefHeightAll) refers to u8g2_font.o(i.u8g2_UpdateRefHeight) for u8g2_UpdateRefHeight
    u8g2_font.o(i.u8g2_SetFontRefHeightExtendedText) refers to u8g2_font.o(i.u8g2_UpdateRefHeight) for u8g2_UpdateRefHeight
    u8g2_font.o(i.u8g2_SetFontRefHeightText) refers to u8g2_font.o(i.u8g2_UpdateRefHeight) for u8g2_UpdateRefHeight
    u8g2_font.o(i.u8g2_draw_string) refers to u8x8_8x8.o(i.u8x8_utf8_init) for u8x8_utf8_init
    u8g2_font.o(i.u8g2_draw_string) refers to u8g2_font.o(i.u8g2_DrawGlyph) for u8g2_DrawGlyph
    u8g2_font.o(i.u8g2_draw_string_2x) refers to u8x8_8x8.o(i.u8x8_utf8_init) for u8x8_utf8_init
    u8g2_font.o(i.u8g2_draw_string_2x) refers to u8g2_font.o(i.u8g2_DrawGlyphX2) for u8g2_DrawGlyphX2
    u8g2_font.o(i.u8g2_font_2x_decode_glyph) refers to u8g2_font.o(i.u8g2_font_setup_decode) for u8g2_font_setup_decode
    u8g2_font.o(i.u8g2_font_2x_decode_glyph) refers to u8g2_font.o(i.u8g2_font_decode_get_signed_bits) for u8g2_font_decode_get_signed_bits
    u8g2_font.o(i.u8g2_font_2x_decode_glyph) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_font.o(i.u8g2_font_2x_decode_glyph) refers to u8g2_font.o(i.u8g2_font_decode_get_unsigned_bits) for u8g2_font_decode_get_unsigned_bits
    u8g2_font.o(i.u8g2_font_2x_decode_glyph) refers to u8g2_font.o(i.u8g2_font_2x_decode_len) for u8g2_font_2x_decode_len
    u8g2_font.o(i.u8g2_font_2x_decode_len) refers to u8g2_hvline.o(i.u8g2_DrawHVLine) for u8g2_DrawHVLine
    u8g2_font.o(i.u8g2_font_decode_get_signed_bits) refers to u8g2_font.o(i.u8g2_font_decode_get_unsigned_bits) for u8g2_font_decode_get_unsigned_bits
    u8g2_font.o(i.u8g2_font_decode_glyph) refers to u8g2_font.o(i.u8g2_font_setup_decode) for u8g2_font_setup_decode
    u8g2_font.o(i.u8g2_font_decode_glyph) refers to u8g2_font.o(i.u8g2_font_decode_get_signed_bits) for u8g2_font_decode_get_signed_bits
    u8g2_font.o(i.u8g2_font_decode_glyph) refers to u8g2_font.o(i.u8g2_add_vector_x) for u8g2_add_vector_x
    u8g2_font.o(i.u8g2_font_decode_glyph) refers to u8g2_font.o(i.u8g2_add_vector_y) for u8g2_add_vector_y
    u8g2_font.o(i.u8g2_font_decode_glyph) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_font.o(i.u8g2_font_decode_glyph) refers to u8g2_font.o(i.u8g2_font_decode_get_unsigned_bits) for u8g2_font_decode_get_unsigned_bits
    u8g2_font.o(i.u8g2_font_decode_glyph) refers to u8g2_font.o(i.u8g2_font_decode_len) for u8g2_font_decode_len
    u8g2_font.o(i.u8g2_font_decode_len) refers to u8g2_font.o(i.u8g2_add_vector_x) for u8g2_add_vector_x
    u8g2_font.o(i.u8g2_font_decode_len) refers to u8g2_font.o(i.u8g2_add_vector_y) for u8g2_add_vector_y
    u8g2_font.o(i.u8g2_font_decode_len) refers to u8g2_hvline.o(i.u8g2_DrawHVLine) for u8g2_DrawHVLine
    u8g2_font.o(i.u8g2_font_get_glyph_data) refers to u8g2_font.o(i.u8g2_font_get_word) for u8g2_font_get_word
    u8g2_font.o(i.u8g2_font_setup_decode) refers to u8g2_font.o(i.u8g2_font_decode_get_unsigned_bits) for u8g2_font_decode_get_unsigned_bits
    u8g2_font.o(i.u8g2_read_font_info) refers to u8g2_font.o(i.u8g2_font_get_word) for u8g2_font_get_word
    u8g2_font.o(i.u8g2_string_width) refers to u8x8_8x8.o(i.u8x8_utf8_init) for u8x8_utf8_init
    u8g2_font.o(i.u8g2_string_width) refers to u8g2_font.o(i.u8g2_GetGlyphWidth) for u8g2_GetGlyphWidth
    u8g2_hvline.o(i.u8g2_DrawHLine) refers to u8g2_hvline.o(i.u8g2_DrawHVLine) for u8g2_DrawHVLine
    u8g2_hvline.o(i.u8g2_DrawHVLine) refers to u8g2_hvline.o(i.u8g2_clip_intersection2) for u8g2_clip_intersection2
    u8g2_hvline.o(i.u8g2_DrawPixel) refers to u8g2_hvline.o(i.u8g2_DrawHVLine) for u8g2_DrawHVLine
    u8g2_hvline.o(i.u8g2_DrawVLine) refers to u8g2_hvline.o(i.u8g2_DrawHVLine) for u8g2_DrawHVLine
    u8g2_input_value.o(i.u8g2_UserInterfaceInputValue) refers to u8g2_font.o(i.u8g2_SetFontDirection) for u8g2_SetFontDirection
    u8g2_input_value.o(i.u8g2_UserInterfaceInputValue) refers to u8g2_font.o(i.u8g2_SetFontPosBaseline) for u8g2_SetFontPosBaseline
    u8g2_input_value.o(i.u8g2_UserInterfaceInputValue) refers to u8x8_string.o(i.u8x8_GetStringLineCnt) for u8x8_GetStringLineCnt
    u8g2_input_value.o(i.u8g2_UserInterfaceInputValue) refers to u8g2_font.o(i.u8g2_GetUTF8Width) for u8g2_GetUTF8Width
    u8g2_input_value.o(i.u8g2_UserInterfaceInputValue) refers to u8g2_buffer.o(i.u8g2_FirstPage) for u8g2_FirstPage
    u8g2_input_value.o(i.u8g2_UserInterfaceInputValue) refers to u8g2_selection_list.o(i.u8g2_DrawUTF8Lines) for u8g2_DrawUTF8Lines
    u8g2_input_value.o(i.u8g2_UserInterfaceInputValue) refers to u8g2_font.o(i.u8g2_DrawUTF8) for u8g2_DrawUTF8
    u8g2_input_value.o(i.u8g2_UserInterfaceInputValue) refers to u8x8_u8toa.o(i.u8x8_u8toa) for u8x8_u8toa
    u8g2_input_value.o(i.u8g2_UserInterfaceInputValue) refers to u8g2_buffer.o(i.u8g2_NextPage) for u8g2_NextPage
    u8g2_input_value.o(i.u8g2_UserInterfaceInputValue) refers to u8x8_debounce.o(i.u8x8_GetMenuEvent) for u8x8_GetMenuEvent
    u8g2_intersection.o(i.u8g2_IsIntersection) refers to u8g2_intersection.o(i.u8g2_is_intersection_decision_tree) for u8g2_is_intersection_decision_tree
    u8g2_line.o(i.u8g2_DrawLine) refers to u8g2_hvline.o(i.u8g2_DrawPixel) for u8g2_DrawPixel
    u8g2_message.o(i.u8g2_UserInterfaceMessage) refers to u8g2_font.o(i.u8g2_SetFontDirection) for u8g2_SetFontDirection
    u8g2_message.o(i.u8g2_UserInterfaceMessage) refers to u8g2_font.o(i.u8g2_SetFontPosBaseline) for u8g2_SetFontPosBaseline
    u8g2_message.o(i.u8g2_UserInterfaceMessage) refers to u8x8_string.o(i.u8x8_GetStringLineCnt) for u8x8_GetStringLineCnt
    u8g2_message.o(i.u8g2_UserInterfaceMessage) refers to u8g2_buffer.o(i.u8g2_FirstPage) for u8g2_FirstPage
    u8g2_message.o(i.u8g2_UserInterfaceMessage) refers to u8g2_selection_list.o(i.u8g2_DrawUTF8Lines) for u8g2_DrawUTF8Lines
    u8g2_message.o(i.u8g2_UserInterfaceMessage) refers to u8g2_selection_list.o(i.u8g2_DrawUTF8Line) for u8g2_DrawUTF8Line
    u8g2_message.o(i.u8g2_UserInterfaceMessage) refers to u8g2_message.o(i.u8g2_draw_button_line) for u8g2_draw_button_line
    u8g2_message.o(i.u8g2_UserInterfaceMessage) refers to u8g2_buffer.o(i.u8g2_NextPage) for u8g2_NextPage
    u8g2_message.o(i.u8g2_UserInterfaceMessage) refers to u8x8_debounce.o(i.u8x8_GetMenuEvent) for u8x8_GetMenuEvent
    u8g2_message.o(i.u8g2_draw_button_line) refers to u8x8_string.o(i.u8x8_GetStringLineCnt) for u8x8_GetStringLineCnt
    u8g2_message.o(i.u8g2_draw_button_line) refers to u8x8_string.o(i.u8x8_GetStringLineStart) for u8x8_GetStringLineStart
    u8g2_message.o(i.u8g2_draw_button_line) refers to u8g2_font.o(i.u8g2_GetUTF8Width) for u8g2_GetUTF8Width
    u8g2_message.o(i.u8g2_draw_button_line) refers to u8g2_selection_list.o(i.u8g2_DrawUTF8Line) for u8g2_DrawUTF8Line
    u8g2_polygon.o(i.pg_exec) refers to u8g2_polygon.o(i.pg_line_init) for pg_line_init
    u8g2_polygon.o(i.pg_exec) refers to u8g2_polygon.o(i.pge_Next) for pge_Next
    u8g2_polygon.o(i.pg_exec) refers to u8g2_polygon.o(i.pg_hline) for pg_hline
    u8g2_polygon.o(i.pg_hline) refers to u8g2_hvline.o(i.u8g2_DrawHLine) for u8g2_DrawHLine
    u8g2_polygon.o(i.pg_prepare) refers to u8g2_polygon.o(i.pg_expand_min_y) for pg_expand_min_y
    u8g2_polygon.o(i.pg_prepare) refers to u8g2_polygon.o(i.pg_inc) for pg_inc
    u8g2_polygon.o(i.pg_prepare) refers to u8g2_polygon.o(i.pg_dec) for pg_dec
    u8g2_polygon.o(i.u8g2_AddPolygonXY) refers to u8g2_polygon.o(.bss) for .bss
    u8g2_polygon.o(i.u8g2_ClearPolygonXY) refers to u8g2_polygon.o(.bss) for .bss
    u8g2_polygon.o(i.u8g2_DrawPolygon) refers to u8g2_polygon.o(i.pg_prepare) for pg_prepare
    u8g2_polygon.o(i.u8g2_DrawPolygon) refers to u8g2_polygon.o(i.pg_exec) for pg_exec
    u8g2_polygon.o(i.u8g2_DrawPolygon) refers to u8g2_polygon.o(.bss) for .bss
    u8g2_polygon.o(i.u8g2_DrawTriangle) refers to u8g2_polygon.o(i.u8g2_AddPolygonXY) for u8g2_AddPolygonXY
    u8g2_polygon.o(i.u8g2_DrawTriangle) refers to u8g2_polygon.o(i.u8g2_DrawPolygon) for u8g2_DrawPolygon
    u8g2_polygon.o(i.u8g2_DrawTriangle) refers to u8g2_polygon.o(.bss) for .bss
    u8g2_selection_list.o(i.u8g2_DrawSelectionList) refers to u8x8_string.o(i.u8x8_GetStringLineStart) for u8x8_GetStringLineStart
    u8g2_selection_list.o(i.u8g2_DrawSelectionList) refers to u8g2_selection_list.o(i.u8g2_DrawUTF8Line) for u8g2_DrawUTF8Line
    u8g2_selection_list.o(i.u8g2_DrawUTF8Line) refers to u8g2_font.o(i.u8g2_SetFontDirection) for u8g2_SetFontDirection
    u8g2_selection_list.o(i.u8g2_DrawUTF8Line) refers to u8g2_font.o(i.u8g2_GetUTF8Width) for u8g2_GetUTF8Width
    u8g2_selection_list.o(i.u8g2_DrawUTF8Line) refers to u8g2_font.o(i.u8g2_GetXOffsetUTF8) for u8g2_GetXOffsetUTF8
    u8g2_selection_list.o(i.u8g2_DrawUTF8Line) refers to u8g2_hvline.o(i.u8g2_SetDrawColor) for u8g2_SetDrawColor
    u8g2_selection_list.o(i.u8g2_DrawUTF8Line) refers to u8g2_box.o(i.u8g2_DrawBox) for u8g2_DrawBox
    u8g2_selection_list.o(i.u8g2_DrawUTF8Line) refers to u8g2_box.o(i.u8g2_DrawFrame) for u8g2_DrawFrame
    u8g2_selection_list.o(i.u8g2_DrawUTF8Line) refers to u8g2_font.o(i.u8g2_DrawUTF8) for u8g2_DrawUTF8
    u8g2_selection_list.o(i.u8g2_DrawUTF8Lines) refers to u8x8_string.o(i.u8x8_GetStringLineCnt) for u8x8_GetStringLineCnt
    u8g2_selection_list.o(i.u8g2_DrawUTF8Lines) refers to u8x8_string.o(i.u8x8_GetStringLineStart) for u8x8_GetStringLineStart
    u8g2_selection_list.o(i.u8g2_DrawUTF8Lines) refers to u8g2_selection_list.o(i.u8g2_DrawUTF8Line) for u8g2_DrawUTF8Line
    u8g2_selection_list.o(i.u8g2_UserInterfaceSelectionList) refers to u8x8_string.o(i.u8x8_GetStringLineCnt) for u8x8_GetStringLineCnt
    u8g2_selection_list.o(i.u8g2_UserInterfaceSelectionList) refers to u8g2_font.o(i.u8g2_SetFontPosBaseline) for u8g2_SetFontPosBaseline
    u8g2_selection_list.o(i.u8g2_UserInterfaceSelectionList) refers to u8g2_buffer.o(i.u8g2_FirstPage) for u8g2_FirstPage
    u8g2_selection_list.o(i.u8g2_UserInterfaceSelectionList) refers to u8g2_selection_list.o(i.u8g2_DrawUTF8Lines) for u8g2_DrawUTF8Lines
    u8g2_selection_list.o(i.u8g2_UserInterfaceSelectionList) refers to u8g2_hvline.o(i.u8g2_DrawHLine) for u8g2_DrawHLine
    u8g2_selection_list.o(i.u8g2_UserInterfaceSelectionList) refers to u8g2_selection_list.o(i.u8g2_DrawSelectionList) for u8g2_DrawSelectionList
    u8g2_selection_list.o(i.u8g2_UserInterfaceSelectionList) refers to u8g2_buffer.o(i.u8g2_NextPage) for u8g2_NextPage
    u8g2_selection_list.o(i.u8g2_UserInterfaceSelectionList) refers to u8x8_debounce.o(i.u8x8_GetMenuEvent) for u8x8_GetMenuEvent
    u8g2_selection_list.o(i.u8g2_UserInterfaceSelectionList) refers to u8x8_selection_list.o(i.u8sl_Prev) for u8sl_Prev
    u8g2_selection_list.o(i.u8g2_UserInterfaceSelectionList) refers to u8x8_selection_list.o(i.u8sl_Next) for u8sl_Next
    u8g2_setup.o(i.u8g2_SendF) refers to u8x8_cad.o(i.u8x8_cad_vsendf) for u8x8_cad_vsendf
    u8g2_setup.o(i.u8g2_SetupBuffer) refers to u8g2_setup.o(i.u8g2_SetMaxClipWindow) for u8g2_SetMaxClipWindow
    u8g2_setup.o(i.u8g2_SetupBuffer) refers to u8g2_font.o(i.u8g2_SetFontPosBaseline) for u8g2_SetFontPosBaseline
    u8g2_setup.o(i.u8g2_Setup_null) refers to u8x8_setup.o(i.u8x8_Setup) for u8x8_Setup
    u8g2_setup.o(i.u8g2_Setup_null) refers to u8g2_setup.o(i.u8g2_SetupBuffer) for u8g2_SetupBuffer
    u8g2_setup.o(i.u8g2_Setup_null) refers to u8x8_cad.o(i.u8x8_cad_empty) for u8x8_cad_empty
    u8g2_setup.o(i.u8g2_Setup_null) refers to u8x8_setup.o(i.u8x8_d_null_cb) for u8x8_d_null_cb
    u8g2_setup.o(i.u8g2_Setup_null) refers to u8g2_ll_hvline.o(i.u8g2_ll_hvline_vertical_top_lsb) for u8g2_ll_hvline_vertical_top_lsb
    u8g2_setup.o(i.u8g2_Setup_null) refers to u8g2_setup.o(.data) for .data
    u8g2_setup.o(i.u8g2_apply_clip_window) refers to u8g2_intersection.o(i.u8g2_IsIntersection) for u8g2_IsIntersection
    u8g2_setup.o(i.u8g2_draw_l90_mirrorr_r0) refers to u8g2_hvline.o(i.u8g2_draw_hv_line_2dir) for u8g2_draw_hv_line_2dir
    u8g2_setup.o(i.u8g2_draw_l90_r0) refers to u8g2_hvline.o(i.u8g2_draw_hv_line_2dir) for u8g2_draw_hv_line_2dir
    u8g2_setup.o(i.u8g2_draw_l90_r1) refers to u8g2_hvline.o(i.u8g2_draw_hv_line_2dir) for u8g2_draw_hv_line_2dir
    u8g2_setup.o(i.u8g2_draw_l90_r2) refers to u8g2_hvline.o(i.u8g2_draw_hv_line_2dir) for u8g2_draw_hv_line_2dir
    u8g2_setup.o(i.u8g2_draw_l90_r3) refers to u8g2_hvline.o(i.u8g2_draw_hv_line_2dir) for u8g2_draw_hv_line_2dir
    u8g2_setup.o(i.u8g2_draw_mirror_vertical_r0) refers to u8g2_hvline.o(i.u8g2_draw_hv_line_2dir) for u8g2_draw_hv_line_2dir
    u8g2_setup.o(i.u8g2_update_dimension_r0) refers to u8g2_setup.o(i.u8g2_update_dimension_common) for u8g2_update_dimension_common
    u8g2_setup.o(i.u8g2_update_dimension_r1) refers to u8g2_setup.o(i.u8g2_update_dimension_common) for u8g2_update_dimension_common
    u8g2_setup.o(i.u8g2_update_dimension_r2) refers to u8g2_setup.o(i.u8g2_update_dimension_common) for u8g2_update_dimension_common
    u8g2_setup.o(i.u8g2_update_dimension_r3) refers to u8g2_setup.o(i.u8g2_update_dimension_common) for u8g2_update_dimension_common
    u8g2_setup.o(i.u8g2_update_page_win_r0) refers to u8g2_setup.o(i.u8g2_apply_clip_window) for u8g2_apply_clip_window
    u8g2_setup.o(i.u8g2_update_page_win_r1) refers to u8g2_setup.o(i.u8g2_apply_clip_window) for u8g2_apply_clip_window
    u8g2_setup.o(i.u8g2_update_page_win_r2) refers to u8g2_setup.o(i.u8g2_apply_clip_window) for u8g2_apply_clip_window
    u8g2_setup.o(i.u8g2_update_page_win_r3) refers to u8g2_setup.o(i.u8g2_apply_clip_window) for u8g2_apply_clip_window
    u8g2_setup.o(.constdata) refers to u8g2_setup.o(i.u8g2_update_dimension_r0) for u8g2_update_dimension_r0
    u8g2_setup.o(.constdata) refers to u8g2_setup.o(i.u8g2_update_page_win_r0) for u8g2_update_page_win_r0
    u8g2_setup.o(.constdata) refers to u8g2_setup.o(i.u8g2_draw_l90_r0) for u8g2_draw_l90_r0
    u8g2_setup.o(.constdata) refers to u8g2_setup.o(i.u8g2_update_dimension_r1) for u8g2_update_dimension_r1
    u8g2_setup.o(.constdata) refers to u8g2_setup.o(i.u8g2_update_page_win_r1) for u8g2_update_page_win_r1
    u8g2_setup.o(.constdata) refers to u8g2_setup.o(i.u8g2_draw_l90_r1) for u8g2_draw_l90_r1
    u8g2_setup.o(.constdata) refers to u8g2_setup.o(i.u8g2_update_dimension_r2) for u8g2_update_dimension_r2
    u8g2_setup.o(.constdata) refers to u8g2_setup.o(i.u8g2_update_page_win_r2) for u8g2_update_page_win_r2
    u8g2_setup.o(.constdata) refers to u8g2_setup.o(i.u8g2_draw_l90_r2) for u8g2_draw_l90_r2
    u8g2_setup.o(.constdata) refers to u8g2_setup.o(i.u8g2_update_dimension_r3) for u8g2_update_dimension_r3
    u8g2_setup.o(.constdata) refers to u8g2_setup.o(i.u8g2_update_page_win_r3) for u8g2_update_page_win_r3
    u8g2_setup.o(.constdata) refers to u8g2_setup.o(i.u8g2_draw_l90_r3) for u8g2_draw_l90_r3
    u8g2_setup.o(.constdata) refers to u8g2_setup.o(i.u8g2_update_dimension_r0) for u8g2_update_dimension_r0
    u8g2_setup.o(.constdata) refers to u8g2_setup.o(i.u8g2_update_page_win_r0) for u8g2_update_page_win_r0
    u8g2_setup.o(.constdata) refers to u8g2_setup.o(i.u8g2_draw_l90_mirrorr_r0) for u8g2_draw_l90_mirrorr_r0
    u8g2_setup.o(.constdata) refers to u8g2_setup.o(i.u8g2_update_dimension_r0) for u8g2_update_dimension_r0
    u8g2_setup.o(.constdata) refers to u8g2_setup.o(i.u8g2_update_page_win_r0) for u8g2_update_page_win_r0
    u8g2_setup.o(.constdata) refers to u8g2_setup.o(i.u8g2_draw_mirror_vertical_r0) for u8g2_draw_mirror_vertical_r0
    u8log.o(i.u8log_Init) refers to memseta.o(.text) for __aeabi_memclr4
    u8log.o(i.u8log_Init) refers to u8log.o(i.u8log_clear_screen) for u8log_clear_screen
    u8log.o(i.u8log_WriteChar) refers to u8log.o(i.u8log_write_char) for u8log_write_char
    u8log.o(i.u8log_WriteDec16) refers to u8x8_u16toa.o(i.u8x8_u16toa) for u8x8_u16toa
    u8log.o(i.u8log_WriteDec16) refers to u8log.o(i.u8log_WriteString) for u8log_WriteString
    u8log.o(i.u8log_WriteDec8) refers to u8x8_u8toa.o(i.u8x8_u8toa) for u8x8_u8toa
    u8log.o(i.u8log_WriteDec8) refers to u8log.o(i.u8log_WriteString) for u8log_WriteString
    u8log.o(i.u8log_WriteHex16) refers to u8log.o(i.u8log_WriteHex8) for u8log_WriteHex8
    u8log.o(i.u8log_WriteHex32) refers to u8log.o(i.u8log_WriteHex16) for u8log_WriteHex16
    u8log.o(i.u8log_WriteHex8) refers to u8log.o(i.u8log_WriteHexHalfByte) for u8log_WriteHexHalfByte
    u8log.o(i.u8log_WriteHexHalfByte) refers to u8log.o(i.u8log_WriteChar) for u8log_WriteChar
    u8log.o(i.u8log_WriteString) refers to u8log.o(i.u8log_WriteChar) for u8log_WriteChar
    u8log.o(i.u8log_write_char) refers to u8log.o(i.u8log_cursor_on_screen) for u8log_cursor_on_screen
    u8log.o(i.u8log_write_char) refers to u8log.o(i.u8log_clear_screen) for u8log_clear_screen
    u8log_u8g2.o(i.u8g2_DrawLog) refers to u8g2_font.o(i.u8g2_SetFontDirection) for u8g2_SetFontDirection
    u8log_u8g2.o(i.u8g2_DrawLog) refers to u8g2_font.o(i.u8g2_DrawGlyph) for u8g2_DrawGlyph
    u8log_u8g2.o(i.u8log_u8g2_cb) refers to u8g2_buffer.o(i.u8g2_FirstPage) for u8g2_FirstPage
    u8log_u8g2.o(i.u8log_u8g2_cb) refers to u8log_u8g2.o(i.u8g2_DrawLog) for u8g2_DrawLog
    u8log_u8g2.o(i.u8log_u8g2_cb) refers to u8g2_buffer.o(i.u8g2_NextPage) for u8g2_NextPage
    u8log_u8x8.o(i.u8log_u8x8_cb) refers to u8log_u8x8.o(i.u8x8_DrawLog) for u8x8_DrawLog
    u8log_u8x8.o(i.u8log_u8x8_cb) refers to u8log_u8x8.o(i.u8x8_DrawLogLine) for u8x8_DrawLogLine
    u8log_u8x8.o(i.u8x8_DrawLog) refers to u8log_u8x8.o(i.u8x8_DrawLogLine) for u8x8_DrawLogLine
    u8log_u8x8.o(i.u8x8_DrawLogLine) refers to u8x8_8x8.o(i.u8x8_DrawGlyph) for u8x8_DrawGlyph
    u8x8_8x8.o(i.u8x8_Draw1x2Glyph) refers to u8x8_8x8.o(i.u8x8_get_glyph_data) for u8x8_get_glyph_data
    u8x8_8x8.o(i.u8x8_Draw1x2Glyph) refers to u8x8_8x8.o(i.u8x8_upscale_byte) for u8x8_upscale_byte
    u8x8_8x8.o(i.u8x8_Draw1x2Glyph) refers to u8x8_display.o(i.u8x8_DrawTile) for u8x8_DrawTile
    u8x8_8x8.o(i.u8x8_Draw1x2String) refers to u8x8_8x8.o(i.u8x8_draw_1x2_string) for u8x8_draw_1x2_string
    u8x8_8x8.o(i.u8x8_Draw1x2String) refers to u8x8_8x8.o(i.u8x8_ascii_next) for u8x8_ascii_next
    u8x8_8x8.o(i.u8x8_Draw1x2UTF8) refers to u8x8_8x8.o(i.u8x8_draw_1x2_string) for u8x8_draw_1x2_string
    u8x8_8x8.o(i.u8x8_Draw1x2UTF8) refers to u8x8_8x8.o(i.u8x8_utf8_next) for u8x8_utf8_next
    u8x8_8x8.o(i.u8x8_Draw2x2Glyph) refers to u8x8_8x8.o(i.u8x8_draw_2x2_subglyph) for u8x8_draw_2x2_subglyph
    u8x8_8x8.o(i.u8x8_Draw2x2String) refers to u8x8_8x8.o(i.u8x8_draw_2x2_string) for u8x8_draw_2x2_string
    u8x8_8x8.o(i.u8x8_Draw2x2String) refers to u8x8_8x8.o(i.u8x8_ascii_next) for u8x8_ascii_next
    u8x8_8x8.o(i.u8x8_Draw2x2UTF8) refers to u8x8_8x8.o(i.u8x8_draw_2x2_string) for u8x8_draw_2x2_string
    u8x8_8x8.o(i.u8x8_Draw2x2UTF8) refers to u8x8_8x8.o(i.u8x8_utf8_next) for u8x8_utf8_next
    u8x8_8x8.o(i.u8x8_DrawGlyph) refers to u8x8_8x8.o(i.u8x8_get_glyph_data) for u8x8_get_glyph_data
    u8x8_8x8.o(i.u8x8_DrawGlyph) refers to u8x8_display.o(i.u8x8_DrawTile) for u8x8_DrawTile
    u8x8_8x8.o(i.u8x8_DrawString) refers to u8x8_8x8.o(i.u8x8_draw_string) for u8x8_draw_string
    u8x8_8x8.o(i.u8x8_DrawString) refers to u8x8_8x8.o(i.u8x8_ascii_next) for u8x8_ascii_next
    u8x8_8x8.o(i.u8x8_DrawUTF8) refers to u8x8_8x8.o(i.u8x8_draw_string) for u8x8_draw_string
    u8x8_8x8.o(i.u8x8_DrawUTF8) refers to u8x8_8x8.o(i.u8x8_utf8_next) for u8x8_utf8_next
    u8x8_8x8.o(i.u8x8_GetUTF8Len) refers to u8x8_8x8.o(i.u8x8_utf8_next) for u8x8_utf8_next
    u8x8_8x8.o(i.u8x8_draw_1x2_string) refers to u8x8_8x8.o(i.u8x8_Draw1x2Glyph) for u8x8_Draw1x2Glyph
    u8x8_8x8.o(i.u8x8_draw_2x2_string) refers to u8x8_8x8.o(i.u8x8_Draw2x2Glyph) for u8x8_Draw2x2Glyph
    u8x8_8x8.o(i.u8x8_draw_2x2_subglyph) refers to u8x8_8x8.o(i.u8x8_get_glyph_data) for u8x8_get_glyph_data
    u8x8_8x8.o(i.u8x8_draw_2x2_subglyph) refers to u8x8_8x8.o(i.u8x8_upscale_byte) for u8x8_upscale_byte
    u8x8_8x8.o(i.u8x8_draw_2x2_subglyph) refers to u8x8_8x8.o(i.u8x8_upscale_buf) for u8x8_upscale_buf
    u8x8_8x8.o(i.u8x8_draw_2x2_subglyph) refers to u8x8_display.o(i.u8x8_DrawTile) for u8x8_DrawTile
    u8x8_8x8.o(i.u8x8_draw_string) refers to u8x8_8x8.o(i.u8x8_DrawGlyph) for u8x8_DrawGlyph
    u8x8_byte.o(i.i2c_clear_scl) refers to u8x8_gpio.o(i.u8x8_gpio_call) for u8x8_gpio_call
    u8x8_byte.o(i.i2c_clear_sda) refers to u8x8_gpio.o(i.u8x8_gpio_call) for u8x8_gpio_call
    u8x8_byte.o(i.i2c_delay) refers to u8x8_gpio.o(i.u8x8_gpio_call) for u8x8_gpio_call
    u8x8_byte.o(i.i2c_read_scl_and_delay) refers to u8x8_gpio.o(i.u8x8_gpio_call) for u8x8_gpio_call
    u8x8_byte.o(i.i2c_read_scl_and_delay) refers to u8x8_byte.o(i.i2c_delay) for i2c_delay
    u8x8_byte.o(i.i2c_read_sda) refers to u8x8_gpio.o(i.u8x8_gpio_call) for u8x8_gpio_call
    u8x8_byte.o(i.i2c_write_bit) refers to u8x8_byte.o(i.i2c_read_sda) for i2c_read_sda
    u8x8_byte.o(i.i2c_write_bit) refers to u8x8_byte.o(i.i2c_clear_sda) for i2c_clear_sda
    u8x8_byte.o(i.i2c_write_bit) refers to u8x8_byte.o(i.i2c_delay) for i2c_delay
    u8x8_byte.o(i.i2c_write_bit) refers to u8x8_byte.o(i.i2c_read_scl_and_delay) for i2c_read_scl_and_delay
    u8x8_byte.o(i.i2c_write_bit) refers to u8x8_byte.o(i.i2c_clear_scl) for i2c_clear_scl
    u8x8_byte.o(i.i2c_write_byte) refers to u8x8_byte.o(i.i2c_write_bit) for i2c_write_bit
    u8x8_byte.o(i.i2c_write_byte) refers to u8x8_byte.o(i.i2c_read_sda) for i2c_read_sda
    u8x8_byte.o(i.i2c_write_byte) refers to u8x8_byte.o(i.i2c_delay) for i2c_delay
    u8x8_byte.o(i.i2c_write_byte) refers to u8x8_byte.o(i.i2c_read_scl_and_delay) for i2c_read_scl_and_delay
    u8x8_byte.o(i.i2c_write_byte) refers to u8x8_byte.o(i.i2c_clear_scl) for i2c_clear_scl
    u8x8_byte.o(i.u8x8_byte_3wire_sw_spi) refers to u8x8_gpio.o(i.u8x8_gpio_call) for u8x8_gpio_call
    u8x8_byte.o(i.u8x8_byte_3wire_sw_spi) refers to u8x8_byte.o(.data) for .data
    u8x8_byte.o(i.u8x8_byte_4wire_sw_spi) refers to u8x8_gpio.o(i.u8x8_gpio_call) for u8x8_gpio_call
    u8x8_byte.o(i.u8x8_byte_8bit_6800mode) refers to u8x8_gpio.o(i.u8x8_gpio_call) for u8x8_gpio_call
    u8x8_byte.o(i.u8x8_byte_8bit_8080mode) refers to u8x8_gpio.o(i.u8x8_gpio_call) for u8x8_gpio_call
    u8x8_byte.o(i.u8x8_byte_ks0108) refers to u8x8_gpio.o(i.u8x8_gpio_call) for u8x8_gpio_call
    u8x8_byte.o(i.u8x8_byte_ks0108) refers to u8x8_byte.o(i.u8x8_byte_set_ks0108_cs) for u8x8_byte_set_ks0108_cs
    u8x8_byte.o(i.u8x8_byte_sed1520) refers to u8x8_gpio.o(i.u8x8_gpio_call) for u8x8_gpio_call
    u8x8_byte.o(i.u8x8_byte_sed1520) refers to u8x8_byte.o(.data) for .data
    u8x8_byte.o(i.u8x8_byte_set_ks0108_cs) refers to u8x8_gpio.o(i.u8x8_gpio_call) for u8x8_gpio_call
    u8x8_byte.o(i.u8x8_byte_sw_i2c) refers to u8x8_byte.o(i.i2c_write_byte) for i2c_write_byte
    u8x8_byte.o(i.u8x8_byte_sw_i2c) refers to u8x8_gpio.o(i.u8x8_gpio_call) for u8x8_gpio_call
    u8x8_byte.o(i.u8x8_byte_sw_i2c) refers to u8x8_byte.o(i.i2c_delay) for i2c_delay
    u8x8_byte.o(i.u8x8_byte_sw_i2c) refers to u8x8_byte.o(i.i2c_read_sda) for i2c_read_sda
    u8x8_byte.o(i.u8x8_byte_sw_i2c) refers to u8x8_byte.o(i.i2c_read_scl_and_delay) for i2c_read_scl_and_delay
    u8x8_byte.o(i.u8x8_byte_sw_i2c) refers to u8x8_byte.o(i.i2c_clear_sda) for i2c_clear_sda
    u8x8_byte.o(i.u8x8_byte_sw_i2c) refers to u8x8_byte.o(i.i2c_clear_scl) for i2c_clear_scl
    u8x8_cad.o(i.u8x8_SendF) refers to u8x8_cad.o(i.u8x8_cad_vsendf) for u8x8_cad_vsendf
    u8x8_cad.o(i.u8x8_cad_001) refers to u8x8_byte.o(i.u8x8_byte_SetDC) for u8x8_byte_SetDC
    u8x8_cad.o(i.u8x8_cad_001) refers to u8x8_byte.o(i.u8x8_byte_SendByte) for u8x8_byte_SendByte
    u8x8_cad.o(i.u8x8_cad_011) refers to u8x8_byte.o(i.u8x8_byte_SetDC) for u8x8_byte_SetDC
    u8x8_cad.o(i.u8x8_cad_011) refers to u8x8_byte.o(i.u8x8_byte_SendByte) for u8x8_byte_SendByte
    u8x8_cad.o(i.u8x8_cad_011_ssd13xx_i2c) refers to u8x8_byte.o(i.u8x8_byte_StartTransfer) for u8x8_byte_StartTransfer
    u8x8_cad.o(i.u8x8_cad_011_ssd13xx_i2c) refers to u8x8_byte.o(i.u8x8_byte_SendByte) for u8x8_byte_SendByte
    u8x8_cad.o(i.u8x8_cad_011_ssd13xx_i2c) refers to u8x8_byte.o(i.u8x8_byte_EndTransfer) for u8x8_byte_EndTransfer
    u8x8_cad.o(i.u8x8_cad_011_ssd13xx_i2c) refers to u8x8_cad.o(i.u8x8_i2c_data_transfer) for u8x8_i2c_data_transfer
    u8x8_cad.o(i.u8x8_cad_100) refers to u8x8_byte.o(i.u8x8_byte_SetDC) for u8x8_byte_SetDC
    u8x8_cad.o(i.u8x8_cad_100) refers to u8x8_byte.o(i.u8x8_byte_SendByte) for u8x8_byte_SendByte
    u8x8_cad.o(i.u8x8_cad_110) refers to u8x8_byte.o(i.u8x8_byte_SetDC) for u8x8_byte_SetDC
    u8x8_cad.o(i.u8x8_cad_110) refers to u8x8_byte.o(i.u8x8_byte_SendByte) for u8x8_byte_SendByte
    u8x8_cad.o(i.u8x8_cad_SendSequence) refers to u8x8_cad.o(i.u8x8_cad_SendData) for u8x8_cad_SendData
    u8x8_cad.o(i.u8x8_cad_SendSequence) refers to u8x8_gpio.o(i.u8x8_gpio_call) for u8x8_gpio_call
    u8x8_cad.o(i.u8x8_cad_empty) refers to u8x8_byte.o(i.u8x8_byte_SendByte) for u8x8_byte_SendByte
    u8x8_cad.o(i.u8x8_cad_ld7032_i2c) refers to u8x8_byte.o(i.u8x8_byte_EndTransfer) for u8x8_byte_EndTransfer
    u8x8_cad.o(i.u8x8_cad_ld7032_i2c) refers to u8x8_byte.o(i.u8x8_byte_StartTransfer) for u8x8_byte_StartTransfer
    u8x8_cad.o(i.u8x8_cad_ld7032_i2c) refers to u8x8_byte.o(i.u8x8_byte_SendByte) for u8x8_byte_SendByte
    u8x8_cad.o(i.u8x8_cad_ld7032_i2c) refers to u8x8_cad.o(.data) for .data
    u8x8_cad.o(i.u8x8_cad_ssd13xx_fast_i2c) refers to u8x8_byte.o(i.u8x8_byte_EndTransfer) for u8x8_byte_EndTransfer
    u8x8_cad.o(i.u8x8_cad_ssd13xx_fast_i2c) refers to u8x8_byte.o(i.u8x8_byte_StartTransfer) for u8x8_byte_StartTransfer
    u8x8_cad.o(i.u8x8_cad_ssd13xx_fast_i2c) refers to u8x8_byte.o(i.u8x8_byte_SendByte) for u8x8_byte_SendByte
    u8x8_cad.o(i.u8x8_cad_ssd13xx_fast_i2c) refers to u8x8_cad.o(i.u8x8_i2c_data_transfer) for u8x8_i2c_data_transfer
    u8x8_cad.o(i.u8x8_cad_ssd13xx_fast_i2c) refers to u8x8_cad.o(.data) for .data
    u8x8_cad.o(i.u8x8_cad_ssd13xx_i2c) refers to u8x8_byte.o(i.u8x8_byte_StartTransfer) for u8x8_byte_StartTransfer
    u8x8_cad.o(i.u8x8_cad_ssd13xx_i2c) refers to u8x8_byte.o(i.u8x8_byte_SendByte) for u8x8_byte_SendByte
    u8x8_cad.o(i.u8x8_cad_ssd13xx_i2c) refers to u8x8_byte.o(i.u8x8_byte_EndTransfer) for u8x8_byte_EndTransfer
    u8x8_cad.o(i.u8x8_cad_ssd13xx_i2c) refers to u8x8_cad.o(i.u8x8_i2c_data_transfer) for u8x8_i2c_data_transfer
    u8x8_cad.o(i.u8x8_cad_st75256_i2c) refers to u8x8_byte.o(i.u8x8_byte_StartTransfer) for u8x8_byte_StartTransfer
    u8x8_cad.o(i.u8x8_cad_st75256_i2c) refers to u8x8_byte.o(i.u8x8_byte_SendByte) for u8x8_byte_SendByte
    u8x8_cad.o(i.u8x8_cad_st75256_i2c) refers to u8x8_byte.o(i.u8x8_byte_EndTransfer) for u8x8_byte_EndTransfer
    u8x8_cad.o(i.u8x8_cad_st75256_i2c) refers to u8x8_cad.o(i.u8x8_i2c_data_transfer) for u8x8_i2c_data_transfer
    u8x8_cad.o(i.u8x8_cad_st7920_spi) refers to u8x8_byte.o(i.u8x8_byte_SendByte) for u8x8_byte_SendByte
    u8x8_cad.o(i.u8x8_cad_st7920_spi) refers to u8x8_gpio.o(i.u8x8_gpio_call) for u8x8_gpio_call
    u8x8_cad.o(i.u8x8_cad_st7920_spi) refers to u8x8_byte.o(i.u8x8_byte_SendBytes) for u8x8_byte_SendBytes
    u8x8_cad.o(i.u8x8_cad_st7920_spi) refers to u8x8_cad.o(.bss) for .bss
    u8x8_cad.o(i.u8x8_cad_uc1638_i2c) refers to u8x8_byte.o(i.u8x8_byte_EndTransfer) for u8x8_byte_EndTransfer
    u8x8_cad.o(i.u8x8_cad_uc1638_i2c) refers to u8x8_byte.o(i.u8x8_byte_StartTransfer) for u8x8_byte_StartTransfer
    u8x8_cad.o(i.u8x8_cad_uc1638_i2c) refers to u8x8_byte.o(i.u8x8_byte_SendByte) for u8x8_byte_SendByte
    u8x8_cad.o(i.u8x8_cad_uc1638_i2c) refers to u8x8_cad.o(.data) for .data
    u8x8_cad.o(i.u8x8_cad_uc16xx_i2c) refers to u8x8_byte.o(i.u8x8_byte_EndTransfer) for u8x8_byte_EndTransfer
    u8x8_cad.o(i.u8x8_cad_uc16xx_i2c) refers to u8x8_byte.o(i.u8x8_byte_StartTransfer) for u8x8_byte_StartTransfer
    u8x8_cad.o(i.u8x8_cad_uc16xx_i2c) refers to u8x8_byte.o(i.u8x8_byte_SendByte) for u8x8_byte_SendByte
    u8x8_cad.o(i.u8x8_cad_uc16xx_i2c) refers to u8x8_cad.o(.data) for .data
    u8x8_cad.o(i.u8x8_cad_vsendf) refers to u8x8_cad.o(i.u8x8_cad_StartTransfer) for u8x8_cad_StartTransfer
    u8x8_cad.o(i.u8x8_cad_vsendf) refers to u8x8_cad.o(i.u8x8_cad_SendArg) for u8x8_cad_SendArg
    u8x8_cad.o(i.u8x8_cad_vsendf) refers to u8x8_cad.o(i.u8x8_cad_SendCmd) for u8x8_cad_SendCmd
    u8x8_cad.o(i.u8x8_cad_vsendf) refers to u8x8_cad.o(i.u8x8_cad_SendData) for u8x8_cad_SendData
    u8x8_cad.o(i.u8x8_cad_vsendf) refers to u8x8_cad.o(i.u8x8_cad_EndTransfer) for u8x8_cad_EndTransfer
    u8x8_cad.o(i.u8x8_gu800_cad_110) refers to u8x8_byte.o(i.u8x8_byte_SetDC) for u8x8_byte_SetDC
    u8x8_cad.o(i.u8x8_gu800_cad_110) refers to u8x8_byte.o(i.u8x8_byte_StartTransfer) for u8x8_byte_StartTransfer
    u8x8_cad.o(i.u8x8_gu800_cad_110) refers to u8x8_byte.o(i.u8x8_byte_SendByte) for u8x8_byte_SendByte
    u8x8_cad.o(i.u8x8_gu800_cad_110) refers to u8x8_byte.o(i.u8x8_byte_EndTransfer) for u8x8_byte_EndTransfer
    u8x8_cad.o(i.u8x8_i2c_data_transfer) refers to u8x8_byte.o(i.u8x8_byte_StartTransfer) for u8x8_byte_StartTransfer
    u8x8_cad.o(i.u8x8_i2c_data_transfer) refers to u8x8_byte.o(i.u8x8_byte_SendByte) for u8x8_byte_SendByte
    u8x8_cad.o(i.u8x8_i2c_data_transfer) refers to u8x8_byte.o(i.u8x8_byte_EndTransfer) for u8x8_byte_EndTransfer
    u8x8_capture.o(i.u8x8_capture_write_pbm_pre) refers to u8x8_u16toa.o(i.u8x8_utoa) for u8x8_utoa
    u8x8_capture.o(i.u8x8_capture_write_xbm_pre) refers to u8x8_u16toa.o(i.u8x8_utoa) for u8x8_utoa
    u8x8_d_ssd1306_128x32.o(i.u8x8_d_sh1106_128x32_visionox) refers to u8x8_display.o(i.u8x8_d_helper_display_setup_memory) for u8x8_d_helper_display_setup_memory
    u8x8_d_ssd1306_128x32.o(i.u8x8_d_sh1106_128x32_visionox) refers to u8x8_display.o(i.u8x8_d_helper_display_init) for u8x8_d_helper_display_init
    u8x8_d_ssd1306_128x32.o(i.u8x8_d_sh1106_128x32_visionox) refers to u8x8_cad.o(i.u8x8_cad_SendSequence) for u8x8_cad_SendSequence
    u8x8_d_ssd1306_128x32.o(i.u8x8_d_sh1106_128x32_visionox) refers to u8x8_d_ssd1306_128x32.o(i.u8x8_d_ssd1306_128x32_generic) for u8x8_d_ssd1306_128x32_generic
    u8x8_d_ssd1306_128x32.o(i.u8x8_d_sh1106_128x32_visionox) refers to u8x8_d_ssd1306_128x32.o(.constdata) for .constdata
    u8x8_d_ssd1306_128x32.o(i.u8x8_d_ssd1306_128x32_generic) refers to u8x8_display.o(i.u8x8_d_helper_display_init) for u8x8_d_helper_display_init
    u8x8_d_ssd1306_128x32.o(i.u8x8_d_ssd1306_128x32_generic) refers to u8x8_cad.o(i.u8x8_cad_SendSequence) for u8x8_cad_SendSequence
    u8x8_d_ssd1306_128x32.o(i.u8x8_d_ssd1306_128x32_generic) refers to u8x8_cad.o(i.u8x8_cad_StartTransfer) for u8x8_cad_StartTransfer
    u8x8_d_ssd1306_128x32.o(i.u8x8_d_ssd1306_128x32_generic) refers to u8x8_cad.o(i.u8x8_cad_SendCmd) for u8x8_cad_SendCmd
    u8x8_d_ssd1306_128x32.o(i.u8x8_d_ssd1306_128x32_generic) refers to u8x8_cad.o(i.u8x8_cad_SendArg) for u8x8_cad_SendArg
    u8x8_d_ssd1306_128x32.o(i.u8x8_d_ssd1306_128x32_generic) refers to u8x8_cad.o(i.u8x8_cad_SendData) for u8x8_cad_SendData
    u8x8_d_ssd1306_128x32.o(i.u8x8_d_ssd1306_128x32_generic) refers to u8x8_cad.o(i.u8x8_cad_EndTransfer) for u8x8_cad_EndTransfer
    u8x8_d_ssd1306_128x32.o(i.u8x8_d_ssd1306_128x32_generic) refers to u8x8_d_ssd1306_128x32.o(.constdata) for .constdata
    u8x8_d_ssd1306_128x32.o(i.u8x8_d_ssd1306_128x32_univision) refers to u8x8_d_ssd1306_128x32.o(i.u8x8_d_ssd1306_128x32_generic) for u8x8_d_ssd1306_128x32_generic
    u8x8_d_ssd1306_128x32.o(i.u8x8_d_ssd1306_128x32_univision) refers to u8x8_display.o(i.u8x8_d_helper_display_setup_memory) for u8x8_d_helper_display_setup_memory
    u8x8_d_ssd1306_128x32.o(i.u8x8_d_ssd1306_128x32_univision) refers to u8x8_d_ssd1306_128x32.o(.constdata) for .constdata
    u8x8_d_ssd1306_128x32.o(i.u8x8_d_ssd1306_128x32_winstar) refers to u8x8_d_ssd1306_128x32.o(i.u8x8_d_ssd1306_128x32_generic) for u8x8_d_ssd1306_128x32_generic
    u8x8_d_ssd1306_128x32.o(i.u8x8_d_ssd1306_128x32_winstar) refers to u8x8_display.o(i.u8x8_d_helper_display_setup_memory) for u8x8_d_helper_display_setup_memory
    u8x8_d_ssd1306_128x32.o(i.u8x8_d_ssd1306_128x32_winstar) refers to u8x8_d_ssd1306_128x32.o(.constdata) for .constdata
    u8x8_debounce.o(i.u8x8_GetMenuEvent) refers to u8x8_gpio.o(i.u8x8_gpio_call) for u8x8_gpio_call
    u8x8_display.o(i.u8x8_ClearDisplay) refers to u8x8_display.o(i.u8x8_ClearDisplayWithTile) for u8x8_ClearDisplayWithTile
    u8x8_display.o(i.u8x8_FillDisplay) refers to u8x8_display.o(i.u8x8_ClearDisplayWithTile) for u8x8_ClearDisplayWithTile
    u8x8_display.o(i.u8x8_d_helper_display_init) refers to u8x8_gpio.o(i.u8x8_gpio_call) for u8x8_gpio_call
    u8x8_input_value.o(i.u8x8_UserInterfaceInputValue) refers to u8x8_string.o(i.u8x8_GetStringLineCnt) for u8x8_GetStringLineCnt
    u8x8_input_value.o(i.u8x8_UserInterfaceInputValue) refers to u8x8_8x8.o(i.u8x8_GetUTF8Len) for u8x8_GetUTF8Len
    u8x8_input_value.o(i.u8x8_UserInterfaceInputValue) refers to u8x8_display.o(i.u8x8_ClearDisplay) for u8x8_ClearDisplay
    u8x8_input_value.o(i.u8x8_UserInterfaceInputValue) refers to u8x8_string.o(i.u8x8_DrawUTF8Lines) for u8x8_DrawUTF8Lines
    u8x8_input_value.o(i.u8x8_UserInterfaceInputValue) refers to u8x8_8x8.o(i.u8x8_DrawUTF8) for u8x8_DrawUTF8
    u8x8_input_value.o(i.u8x8_UserInterfaceInputValue) refers to u8x8_debounce.o(i.u8x8_GetMenuEvent) for u8x8_GetMenuEvent
    u8x8_input_value.o(i.u8x8_UserInterfaceInputValue) refers to u8x8_u8toa.o(i.u8x8_u8toa) for u8x8_u8toa
    u8x8_message.o(i.u8x8_UserInterfaceMessage) refers to u8x8_string.o(i.u8x8_GetStringLineCnt) for u8x8_GetStringLineCnt
    u8x8_message.o(i.u8x8_UserInterfaceMessage) refers to u8x8_display.o(i.u8x8_ClearDisplay) for u8x8_ClearDisplay
    u8x8_message.o(i.u8x8_UserInterfaceMessage) refers to u8x8_string.o(i.u8x8_DrawUTF8Lines) for u8x8_DrawUTF8Lines
    u8x8_message.o(i.u8x8_UserInterfaceMessage) refers to u8x8_string.o(i.u8x8_DrawUTF8Line) for u8x8_DrawUTF8Line
    u8x8_message.o(i.u8x8_UserInterfaceMessage) refers to u8x8_message.o(i.u8x8_draw_button_line) for u8x8_draw_button_line
    u8x8_message.o(i.u8x8_UserInterfaceMessage) refers to u8x8_debounce.o(i.u8x8_GetMenuEvent) for u8x8_GetMenuEvent
    u8x8_message.o(i.u8x8_draw_button_line) refers to u8x8_string.o(i.u8x8_GetStringLineCnt) for u8x8_GetStringLineCnt
    u8x8_message.o(i.u8x8_draw_button_line) refers to u8x8_string.o(i.u8x8_GetStringLineStart) for u8x8_GetStringLineStart
    u8x8_message.o(i.u8x8_draw_button_line) refers to u8x8_8x8.o(i.u8x8_GetUTF8Len) for u8x8_GetUTF8Len
    u8x8_message.o(i.u8x8_draw_button_line) refers to u8x8_8x8.o(i.u8x8_DrawUTF8) for u8x8_DrawUTF8
    u8x8_selection_list.o(i.u8x8_UserInterfaceSelectionList) refers to u8x8_string.o(i.u8x8_GetStringLineCnt) for u8x8_GetStringLineCnt
    u8x8_selection_list.o(i.u8x8_UserInterfaceSelectionList) refers to u8x8_string.o(i.u8x8_DrawUTF8Lines) for u8x8_DrawUTF8Lines
    u8x8_selection_list.o(i.u8x8_UserInterfaceSelectionList) refers to u8x8_selection_list.o(i.u8x8_DrawSelectionList) for u8x8_DrawSelectionList
    u8x8_selection_list.o(i.u8x8_UserInterfaceSelectionList) refers to u8x8_debounce.o(i.u8x8_GetMenuEvent) for u8x8_GetMenuEvent
    u8x8_selection_list.o(i.u8x8_UserInterfaceSelectionList) refers to u8x8_selection_list.o(i.u8sl_Prev) for u8sl_Prev
    u8x8_selection_list.o(i.u8x8_UserInterfaceSelectionList) refers to u8x8_selection_list.o(i.u8sl_Next) for u8sl_Next
    u8x8_selection_list.o(i.u8x8_UserInterfaceSelectionList) refers to u8x8_selection_list.o(i.u8x8_sl_string_line_cb) for u8x8_sl_string_line_cb
    u8x8_selection_list.o(i.u8x8_sl_string_line_cb) refers to u8x8_string.o(i.u8x8_GetStringLineStart) for u8x8_GetStringLineStart
    u8x8_selection_list.o(i.u8x8_sl_string_line_cb) refers to u8x8_string.o(i.u8x8_DrawUTF8Line) for u8x8_DrawUTF8Line
    u8x8_setup.o(i.u8x8_Setup) refers to u8x8_setup.o(i.u8x8_SetupDefaults) for u8x8_SetupDefaults
    u8x8_setup.o(i.u8x8_Setup) refers to u8x8_display.o(i.u8x8_SetupMemory) for u8x8_SetupMemory
    u8x8_setup.o(i.u8x8_SetupDefaults) refers to u8x8_setup.o(i.u8x8_dummy_cb) for u8x8_dummy_cb
    u8x8_setup.o(i.u8x8_d_null_cb) refers to u8x8_display.o(i.u8x8_d_helper_display_setup_memory) for u8x8_d_helper_display_setup_memory
    u8x8_setup.o(i.u8x8_d_null_cb) refers to u8x8_display.o(i.u8x8_d_helper_display_init) for u8x8_d_helper_display_init
    u8x8_setup.o(i.u8x8_d_null_cb) refers to u8x8_setup.o(.constdata) for .constdata
    u8x8_string.o(i.u8x8_CopyStringLine) refers to u8x8_string.o(i.u8x8_GetStringLineStart) for u8x8_GetStringLineStart
    u8x8_string.o(i.u8x8_DrawUTF8Line) refers to u8x8_8x8.o(i.u8x8_GetUTF8Len) for u8x8_GetUTF8Len
    u8x8_string.o(i.u8x8_DrawUTF8Line) refers to u8x8_8x8.o(i.u8x8_DrawUTF8) for u8x8_DrawUTF8
    u8x8_string.o(i.u8x8_DrawUTF8Lines) refers to u8x8_string.o(i.u8x8_GetStringLineCnt) for u8x8_GetStringLineCnt
    u8x8_string.o(i.u8x8_DrawUTF8Lines) refers to u8x8_string.o(i.u8x8_GetStringLineStart) for u8x8_GetStringLineStart
    u8x8_string.o(i.u8x8_DrawUTF8Lines) refers to u8x8_string.o(i.u8x8_DrawUTF8Line) for u8x8_DrawUTF8Line
    u8x8_u8toa.o(i.u8x8_s8toa) refers to u8x8_u8toa.o(i.u8x8_u8toap) for u8x8_u8toap
    u8x8_u8toa.o(i.u8x8_s8toa) refers to u8x8_u8toa.o(.data) for .data
    u8x8_u8toa.o(i.u8x8_u8toa) refers to u8x8_u8toa.o(i.u8x8_u8toap) for u8x8_u8toap
    u8x8_u8toa.o(i.u8x8_u8toa) refers to u8x8_u8toa.o(.data) for .data
    u8x8_u8toa.o(i.u8x8_u8toap) refers to u8x8_u8toa.o(.constdata) for .constdata
    u8x8_u16toa.o(i.u8x8_u16toa) refers to u8x8_u16toa.o(i.u8x8_u16toap) for u8x8_u16toap
    u8x8_u16toa.o(i.u8x8_u16toa) refers to u8x8_u16toa.o(.data) for .data
    u8x8_u16toa.o(i.u8x8_utoa) refers to u8x8_u16toa.o(i.u8x8_u16toa) for u8x8_u16toa
    ringbuffer.o(i.rt_ringbuffer_data_len) refers to ringbuffer.o(i.rt_ringbuffer_status) for rt_ringbuffer_status
    ringbuffer.o(i.rt_ringbuffer_get) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_get) refers to memmovea.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_getchar) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_peek) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_put) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_put) refers to memmovea.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_put_force) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_put_force) refers to memmovea.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_putchar) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_putchar_force) refers to ringbuffer.o(i.rt_ringbuffer_status) for rt_ringbuffer_status
    gd25qxx.o(i.spi_flash_buffer_read) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gd25qxx.o(i.spi_flash_buffer_read) refers to gd25qxx.o(i.spi_flash_send_byte) for spi_flash_send_byte
    gd25qxx.o(i.spi_flash_buffer_write) refers to gd25qxx.o(i.spi_flash_page_write) for spi_flash_page_write
    gd25qxx.o(i.spi_flash_bulk_erase) refers to gd25qxx.o(i.spi_flash_write_enable) for spi_flash_write_enable
    gd25qxx.o(i.spi_flash_bulk_erase) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gd25qxx.o(i.spi_flash_bulk_erase) refers to gd25qxx.o(i.spi_flash_send_byte) for spi_flash_send_byte
    gd25qxx.o(i.spi_flash_bulk_erase) refers to gd25qxx.o(i.spi_flash_wait_for_write_end) for spi_flash_wait_for_write_end
    gd25qxx.o(i.spi_flash_init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gd25qxx.o(i.spi_flash_page_write) refers to gd25qxx.o(i.spi_flash_write_enable) for spi_flash_write_enable
    gd25qxx.o(i.spi_flash_page_write) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gd25qxx.o(i.spi_flash_page_write) refers to gd25qxx.o(i.spi_flash_send_byte) for spi_flash_send_byte
    gd25qxx.o(i.spi_flash_page_write) refers to gd25qxx.o(i.spi_flash_wait_for_write_end) for spi_flash_wait_for_write_end
    gd25qxx.o(i.spi_flash_read_byte) refers to gd25qxx.o(i.spi_flash_send_byte) for spi_flash_send_byte
    gd25qxx.o(i.spi_flash_read_id) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gd25qxx.o(i.spi_flash_read_id) refers to gd25qxx.o(i.spi_flash_send_byte) for spi_flash_send_byte
    gd25qxx.o(i.spi_flash_sector_erase) refers to gd25qxx.o(i.spi_flash_write_enable) for spi_flash_write_enable
    gd25qxx.o(i.spi_flash_sector_erase) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gd25qxx.o(i.spi_flash_sector_erase) refers to gd25qxx.o(i.spi_flash_send_byte) for spi_flash_send_byte
    gd25qxx.o(i.spi_flash_sector_erase) refers to gd25qxx.o(i.spi_flash_wait_for_write_end) for spi_flash_wait_for_write_end
    gd25qxx.o(i.spi_flash_send_byte) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) for HAL_SPI_TransmitReceive
    gd25qxx.o(i.spi_flash_send_byte) refers to spi.o(.bss) for hspi2
    gd25qxx.o(i.spi_flash_send_halfword) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) for HAL_SPI_TransmitReceive
    gd25qxx.o(i.spi_flash_send_halfword) refers to spi.o(.bss) for hspi2
    gd25qxx.o(i.spi_flash_start_read_sequence) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gd25qxx.o(i.spi_flash_start_read_sequence) refers to gd25qxx.o(i.spi_flash_send_byte) for spi_flash_send_byte
    gd25qxx.o(i.spi_flash_wait_for_write_end) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gd25qxx.o(i.spi_flash_wait_for_write_end) refers to gd25qxx.o(i.spi_flash_send_byte) for spi_flash_send_byte
    gd25qxx.o(i.spi_flash_write_enable) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gd25qxx.o(i.spi_flash_write_enable) refers to gd25qxx.o(i.spi_flash_send_byte) for spi_flash_send_byte
    lfs.o(i.lfs_alloc) refers to lfs.o(i.lfs_min) for lfs_min
    lfs.o(i.lfs_alloc) refers to memseta.o(.text) for __aeabi_memclr4
    lfs.o(i.lfs_alloc) refers to lfs.o(i.lfs_fs_traverse) for lfs_fs_traverse
    lfs.o(i.lfs_alloc) refers to printfa.o(i.__0printf) for __2printf
    lfs.o(i.lfs_alloc) refers to lfs.o(i.lfs_alloc_lookahead) for lfs_alloc_lookahead
    lfs.o(i.lfs_bd_cmp) refers to lfs.o(i.lfs_bd_read) for lfs_bd_read
    lfs.o(i.lfs_bd_flush) refers to lfs.o(i.lfs_alignup) for lfs_alignup
    lfs.o(i.lfs_bd_flush) refers to lfs.o(i.lfs_bd_cmp) for lfs_bd_cmp
    lfs.o(i.lfs_bd_flush) refers to lfs.o(i.lfs_cache_zero) for lfs_cache_zero
    lfs.o(i.lfs_bd_prog) refers to memmovea.o(.text) for __aeabi_memcpy
    lfs.o(i.lfs_bd_prog) refers to lfs.o(i.lfs_bd_flush) for lfs_bd_flush
    lfs.o(i.lfs_bd_read) refers to memmovea.o(.text) for __aeabi_memcpy
    lfs.o(i.lfs_bd_read) refers to lfs.o(i.lfs_alignup) for lfs_alignup
    lfs.o(i.lfs_bd_read) refers to lfs.o(i.lfs_min) for lfs_min
    lfs.o(i.lfs_cache_zero) refers to memseta.o(.text) for __aeabi_memset
    lfs.o(i.lfs_commitattr) refers to lfs.o(i.lfs_dir_find) for lfs_dir_find
    lfs.o(i.lfs_commitattr) refers to lfs.o(i.lfs_dir_fetch) for lfs_dir_fetch
    lfs.o(i.lfs_commitattr) refers to lfs.o(i.lfs_dir_commit) for lfs_dir_commit
    lfs.o(i.lfs_ctz_extend) refers to lfs.o(i.lfs_alloc) for lfs_alloc
    lfs.o(i.lfs_ctz_extend) refers to lfs.o(i.lfs_bd_erase) for lfs_bd_erase
    lfs.o(i.lfs_ctz_extend) refers to lfs.o(i.lfs_ctz_index) for lfs_ctz_index
    lfs.o(i.lfs_ctz_extend) refers to lfs.o(i.lfs_bd_read) for lfs_bd_read
    lfs.o(i.lfs_ctz_extend) refers to lfs.o(i.lfs_bd_prog) for lfs_bd_prog
    lfs.o(i.lfs_ctz_extend) refers to lfs.o(i.lfs_ctz) for lfs_ctz
    lfs.o(i.lfs_ctz_extend) refers to lfs.o(i.lfs_tole32) for lfs_tole32
    lfs.o(i.lfs_ctz_extend) refers to lfs.o(i.lfs_fromle32) for lfs_fromle32
    lfs.o(i.lfs_ctz_extend) refers to printfa.o(i.__0printf) for __2printf
    lfs.o(i.lfs_ctz_find) refers to lfs.o(i.lfs_ctz_index) for lfs_ctz_index
    lfs.o(i.lfs_ctz_find) refers to lfs.o(i.lfs_ctz) for lfs_ctz
    lfs.o(i.lfs_ctz_find) refers to lfs.o(i.lfs_min) for lfs_min
    lfs.o(i.lfs_ctz_find) refers to lfs.o(i.lfs_bd_read) for lfs_bd_read
    lfs.o(i.lfs_ctz_find) refers to lfs.o(i.lfs_fromle32) for lfs_fromle32
    lfs.o(i.lfs_ctz_fromle32) refers to lfs.o(i.lfs_fromle32) for lfs_fromle32
    lfs.o(i.lfs_ctz_index) refers to lfs.o(i.lfs_popc) for lfs_popc
    lfs.o(i.lfs_ctz_traverse) refers to lfs.o(i.lfs_ctz_index) for lfs_ctz_index
    lfs.o(i.lfs_ctz_traverse) refers to lfs.o(i.lfs_bd_read) for lfs_bd_read
    lfs.o(i.lfs_ctz_traverse) refers to lfs.o(i.lfs_fromle32) for lfs_fromle32
    lfs.o(i.lfs_deinit) refers to malloc.o(i.free) for free
    lfs.o(i.lfs_dir_alloc) refers to lfs.o(i.lfs_alloc) for lfs_alloc
    lfs.o(i.lfs_dir_alloc) refers to lfs.o(i.lfs_bd_read) for lfs_bd_read
    lfs.o(i.lfs_dir_alloc) refers to lfs.o(i.lfs_fromle32) for lfs_fromle32
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_pair_cmp) for lfs_pair_cmp
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_file_outline) for lfs_file_outline
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_file_flush) for lfs_file_flush
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_pair_fromle32) for lfs_pair_fromle32
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_gstate_hasmovehere) for lfs_gstate_hasmovehere
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_gstate_xormove) for lfs_gstate_xormove
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_fs_pred) for lfs_fs_pred
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_dir_drop) for lfs_dir_drop
    lfs.o(i.lfs_dir_commit) refers to memmovea.o(.text) for __aeabi_memcpy4
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_pair_tole32) for lfs_pair_tole32
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_dir_traverse) for lfs_dir_traverse
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_gstate_iszero) for lfs_gstate_iszero
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_dir_getgstate) for lfs_dir_getgstate
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_gstate_tole32) for lfs_gstate_tole32
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_dir_commitattr) for lfs_dir_commitattr
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_gstate_fromle32) for lfs_gstate_fromle32
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_dir_commitcrc) for lfs_dir_commitcrc
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_dir_compact) for lfs_dir_compact
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_dir_fetch) for lfs_dir_fetch
    lfs.o(i.lfs_dir_commit) refers to lfs.o(.constdata) for .constdata
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_dir_commit_commit) for lfs_dir_commit_commit
    lfs.o(i.lfs_dir_commit_commit) refers to lfs.o(i.lfs_dir_commitattr) for lfs_dir_commitattr
    lfs.o(i.lfs_dir_commit_size) refers to lfs.o(i.lfs_tag_dsize) for lfs_tag_dsize
    lfs.o(i.lfs_dir_commitattr) refers to lfs.o(i.lfs_tag_dsize) for lfs_tag_dsize
    lfs.o(i.lfs_dir_commitattr) refers to lfs.o(i.lfs_frombe32) for lfs_frombe32
    lfs.o(i.lfs_dir_commitattr) refers to lfs.o(i.lfs_dir_commitprog) for lfs_dir_commitprog
    lfs.o(i.lfs_dir_commitattr) refers to lfs.o(i.lfs_bd_read) for lfs_bd_read
    lfs.o(i.lfs_dir_commitcrc) refers to lfs.o(i.lfs_alignup) for lfs_alignup
    lfs.o(i.lfs_dir_commitcrc) refers to lfs.o(i.lfs_bd_read) for lfs_bd_read
    lfs.o(i.lfs_dir_commitcrc) refers to lfs.o(i.lfs_frombe32) for lfs_frombe32
    lfs.o(i.lfs_dir_commitcrc) refers to lfs_util.o(i.lfs_crc) for lfs_crc
    lfs.o(i.lfs_dir_commitcrc) refers to lfs.o(i.lfs_tole32) for lfs_tole32
    lfs.o(i.lfs_dir_commitcrc) refers to lfs.o(i.lfs_bd_prog) for lfs_bd_prog
    lfs.o(i.lfs_dir_commitcrc) refers to lfs.o(i.lfs_bd_flush) for lfs_bd_flush
    lfs.o(i.lfs_dir_commitprog) refers to lfs.o(i.lfs_bd_prog) for lfs_bd_prog
    lfs.o(i.lfs_dir_commitprog) refers to lfs_util.o(i.lfs_crc) for lfs_crc
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_dir_traverse) for lfs_dir_traverse
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_alignup) for lfs_alignup
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_min) for lfs_min
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_dir_split) for lfs_dir_split
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_pair_cmp) for lfs_pair_cmp
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_fs_size) for lfs_fs_size
    lfs.o(i.lfs_dir_compact) refers to printfa.o(i.__0printf) for __2printf
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_dir_getgstate) for lfs_dir_getgstate
    lfs.o(i.lfs_dir_compact) refers to memmovea.o(.text) for __aeabi_memcpy4
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_bd_erase) for lfs_bd_erase
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_tole32) for lfs_tole32
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_dir_commitprog) for lfs_dir_commitprog
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_fromle32) for lfs_fromle32
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_pair_isnull) for lfs_pair_isnull
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_pair_tole32) for lfs_pair_tole32
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_dir_commitattr) for lfs_dir_commitattr
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_pair_fromle32) for lfs_pair_fromle32
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_gstate_iszero) for lfs_gstate_iszero
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_gstate_tole32) for lfs_gstate_tole32
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_gstate_fromle32) for lfs_gstate_fromle32
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_dir_commitcrc) for lfs_dir_commitcrc
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_gstate_hasmovehere) for lfs_gstate_hasmovehere
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_gstate_xormove) for lfs_gstate_xormove
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_fs_relocate) for lfs_fs_relocate
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_alloc) for lfs_alloc
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_dir_commit_size) for lfs_dir_commit_size
    lfs.o(i.lfs_dir_compact) refers to lfs.o(.constdata) for .constdata
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_dir_commit_commit) for lfs_dir_commit_commit
    lfs.o(i.lfs_dir_drop) refers to lfs.o(i.lfs_dir_getgstate) for lfs_dir_getgstate
    lfs.o(i.lfs_dir_drop) refers to lfs.o(i.lfs_pair_tole32) for lfs_pair_tole32
    lfs.o(i.lfs_dir_drop) refers to lfs.o(i.lfs_dir_commit) for lfs_dir_commit
    lfs.o(i.lfs_dir_drop) refers to lfs.o(i.lfs_pair_fromle32) for lfs_pair_fromle32
    lfs.o(i.lfs_dir_fetch) refers to lfs.o(i.lfs_dir_fetchmatch) for lfs_dir_fetchmatch
    lfs.o(i.lfs_dir_fetchmatch) refers to lfs.o(i.lfs_bd_read) for lfs_bd_read
    lfs.o(i.lfs_dir_fetchmatch) refers to lfs.o(i.lfs_fromle32) for lfs_fromle32
    lfs.o(i.lfs_dir_fetchmatch) refers to lfs.o(i.lfs_tole32) for lfs_tole32
    lfs.o(i.lfs_dir_fetchmatch) refers to lfs_util.o(i.lfs_crc) for lfs_crc
    lfs.o(i.lfs_dir_fetchmatch) refers to lfs.o(i.lfs_tag_dsize) for lfs_tag_dsize
    lfs.o(i.lfs_dir_fetchmatch) refers to lfs.o(i.lfs_frombe32) for lfs_frombe32
    lfs.o(i.lfs_dir_fetchmatch) refers to lfs.o(i.lfs_gstate_hasmovehere) for lfs_gstate_hasmovehere
    lfs.o(i.lfs_dir_fetchmatch) refers to lfs.o(i.lfs_pair_fromle32) for lfs_pair_fromle32
    lfs.o(i.lfs_dir_fetchmatch) refers to lfs.o(i.lfs_min) for lfs_min
    lfs.o(i.lfs_dir_fetchmatch) refers to printfa.o(i.__0printf) for __2printf
    lfs.o(i.lfs_dir_fetchmatch) refers to lfs.o(.constdata) for .constdata
    lfs.o(i.lfs_dir_find) refers to strspn.o(.text) for strspn
    lfs.o(i.lfs_dir_find) refers to strcspn.o(.text) for strcspn
    lfs.o(i.lfs_dir_find) refers to memcmp.o(.text) for memcmp
    lfs.o(i.lfs_dir_find) refers to lfs.o(i.lfs_dir_get) for lfs_dir_get
    lfs.o(i.lfs_dir_find) refers to lfs.o(i.lfs_pair_fromle32) for lfs_pair_fromle32
    lfs.o(i.lfs_dir_find) refers to strchr.o(.text) for strchr
    lfs.o(i.lfs_dir_find) refers to lfs.o(i.lfs_dir_fetchmatch) for lfs_dir_fetchmatch
    lfs.o(i.lfs_dir_find) refers to lfs.o(i.lfs_dir_find_match) for lfs_dir_find_match
    lfs.o(i.lfs_dir_find_match) refers to lfs.o(i.lfs_min) for lfs_min
    lfs.o(i.lfs_dir_find_match) refers to lfs.o(i.lfs_bd_cmp) for lfs_bd_cmp
    lfs.o(i.lfs_dir_get) refers to lfs.o(i.lfs_dir_getslice) for lfs_dir_getslice
    lfs.o(i.lfs_dir_getgstate) refers to lfs.o(i.lfs_dir_get) for lfs_dir_get
    lfs.o(i.lfs_dir_getgstate) refers to lfs.o(i.lfs_gstate_fromle32) for lfs_gstate_fromle32
    lfs.o(i.lfs_dir_getinfo) refers to lfs.o(i.lfs_dir_get) for lfs_dir_get
    lfs.o(i.lfs_dir_getinfo) refers to lfs.o(i.lfs_ctz_fromle32) for lfs_ctz_fromle32
    lfs.o(i.lfs_dir_getread) refers to memmovea.o(.text) for __aeabi_memcpy
    lfs.o(i.lfs_dir_getread) refers to lfs.o(i.lfs_alignup) for lfs_alignup
    lfs.o(i.lfs_dir_getread) refers to lfs.o(i.lfs_min) for lfs_min
    lfs.o(i.lfs_dir_getread) refers to lfs.o(i.lfs_dir_getslice) for lfs_dir_getslice
    lfs.o(i.lfs_dir_getslice) refers to lfs.o(i.lfs_gstate_hasmovehere) for lfs_gstate_hasmovehere
    lfs.o(i.lfs_dir_getslice) refers to lfs.o(i.lfs_tag_dsize) for lfs_tag_dsize
    lfs.o(i.lfs_dir_getslice) refers to lfs.o(i.lfs_bd_read) for lfs_bd_read
    lfs.o(i.lfs_dir_getslice) refers to lfs.o(i.lfs_frombe32) for lfs_frombe32
    lfs.o(i.lfs_dir_getslice) refers to lfs.o(i.lfs_tag_isdelete) for lfs_tag_isdelete
    lfs.o(i.lfs_dir_getslice) refers to lfs.o(i.lfs_min) for lfs_min
    lfs.o(i.lfs_dir_getslice) refers to memseta.o(.text) for __aeabi_memclr
    lfs.o(i.lfs_dir_open) refers to lfs.o(i.lfs_dir_find) for lfs_dir_find
    lfs.o(i.lfs_dir_open) refers to lfs.o(i.lfs_dir_get) for lfs_dir_get
    lfs.o(i.lfs_dir_open) refers to lfs.o(i.lfs_pair_fromle32) for lfs_pair_fromle32
    lfs.o(i.lfs_dir_open) refers to lfs.o(i.lfs_dir_fetch) for lfs_dir_fetch
    lfs.o(i.lfs_dir_read) refers to memseta.o(.text) for __aeabi_memclr4
    lfs.o(i.lfs_dir_read) refers to lfs.o(i.lfs_dir_fetch) for lfs_dir_fetch
    lfs.o(i.lfs_dir_read) refers to lfs.o(i.lfs_dir_getinfo) for lfs_dir_getinfo
    lfs.o(i.lfs_dir_read) refers to strcpy.o(.text) for strcpy
    lfs.o(i.lfs_dir_rewind) refers to lfs.o(i.lfs_dir_fetch) for lfs_dir_fetch
    lfs.o(i.lfs_dir_seek) refers to lfs.o(i.lfs_dir_rewind) for lfs_dir_rewind
    lfs.o(i.lfs_dir_seek) refers to lfs.o(i.lfs_min) for lfs_min
    lfs.o(i.lfs_dir_seek) refers to lfs.o(i.lfs_dir_fetch) for lfs_dir_fetch
    lfs.o(i.lfs_dir_split) refers to lfs.o(i.lfs_dir_alloc) for lfs_dir_alloc
    lfs.o(i.lfs_dir_split) refers to lfs.o(i.lfs_dir_compact) for lfs_dir_compact
    lfs.o(i.lfs_dir_split) refers to lfs.o(i.lfs_pair_cmp) for lfs_pair_cmp
    lfs.o(i.lfs_dir_traverse) refers to lfs.o(i.lfs_tag_dsize) for lfs_tag_dsize
    lfs.o(i.lfs_dir_traverse) refers to lfs.o(i.lfs_bd_read) for lfs_bd_read
    lfs.o(i.lfs_dir_traverse) refers to lfs.o(i.lfs_frombe32) for lfs_frombe32
    lfs.o(i.lfs_dir_traverse) refers to lfs.o(i.lfs_gstate_hasmovehere) for lfs_gstate_hasmovehere
    lfs.o(i.lfs_dir_traverse) refers to lfs.o(i.lfs_dir_traverse_filter) for lfs_dir_traverse_filter
    lfs.o(i.lfs_dir_traverse_filter) refers to lfs.o(i.lfs_tag_isdelete) for lfs_tag_isdelete
    lfs.o(i.lfs_file_close) refers to lfs.o(i.lfs_file_sync) for lfs_file_sync
    lfs.o(i.lfs_file_close) refers to malloc.o(i.free) for free
    lfs.o(i.lfs_file_flush) refers to memmovea.o(.text) for __aeabi_memcpy4
    lfs.o(i.lfs_file_flush) refers to lfs.o(i.lfs_file_read) for lfs_file_read
    lfs.o(i.lfs_file_flush) refers to lfs.o(i.lfs_file_write) for lfs_file_write
    lfs.o(i.lfs_file_flush) refers to lfs.o(i.lfs_bd_flush) for lfs_bd_flush
    lfs.o(i.lfs_file_flush) refers to printfa.o(i.__0printf) for __2printf
    lfs.o(i.lfs_file_flush) refers to lfs.o(i.lfs_file_relocate) for lfs_file_relocate
    lfs.o(i.lfs_file_flush) refers to lfs.o(.constdata) for .constdata
    lfs.o(i.lfs_file_open) refers to lfs.o(i.lfs_file_opencfg) for lfs_file_opencfg
    lfs.o(i.lfs_file_open) refers to lfs.o(.constdata) for .constdata
    lfs.o(i.lfs_file_opencfg) refers to lfs.o(i.lfs_fs_forceconsistency) for lfs_fs_forceconsistency
    lfs.o(i.lfs_file_opencfg) refers to lfs.o(i.lfs_dir_find) for lfs_dir_find
    lfs.o(i.lfs_file_opencfg) refers to strlen.o(.text) for strlen
    lfs.o(i.lfs_file_opencfg) refers to memseta.o(.text) for __aeabi_memclr4
    lfs.o(i.lfs_file_opencfg) refers to lfs.o(i.lfs_dir_commit) for lfs_dir_commit
    lfs.o(i.lfs_file_opencfg) refers to lfs.o(i.lfs_dir_get) for lfs_dir_get
    lfs.o(i.lfs_file_opencfg) refers to lfs.o(i.lfs_ctz_fromle32) for lfs_ctz_fromle32
    lfs.o(i.lfs_file_opencfg) refers to lfs.o(i.lfs_malloc) for lfs_malloc
    lfs.o(i.lfs_file_opencfg) refers to lfs.o(i.lfs_cache_zero) for lfs_cache_zero
    lfs.o(i.lfs_file_opencfg) refers to lfs.o(i.lfs_min) for lfs_min
    lfs.o(i.lfs_file_opencfg) refers to lfs.o(i.lfs_file_close) for lfs_file_close
    lfs.o(i.lfs_file_outline) refers to lfs.o(i.lfs_file_relocate) for lfs_file_relocate
    lfs.o(i.lfs_file_read) refers to lfs.o(i.lfs_file_flush) for lfs_file_flush
    lfs.o(i.lfs_file_read) refers to lfs.o(i.lfs_ctz_find) for lfs_ctz_find
    lfs.o(i.lfs_file_read) refers to lfs.o(i.lfs_dir_getread) for lfs_dir_getread
    lfs.o(i.lfs_file_read) refers to lfs.o(i.lfs_bd_read) for lfs_bd_read
    lfs.o(i.lfs_file_relocate) refers to lfs.o(i.lfs_alloc) for lfs_alloc
    lfs.o(i.lfs_file_relocate) refers to lfs.o(i.lfs_bd_erase) for lfs_bd_erase
    lfs.o(i.lfs_file_relocate) refers to lfs.o(i.lfs_dir_getread) for lfs_dir_getread
    lfs.o(i.lfs_file_relocate) refers to lfs.o(i.lfs_bd_read) for lfs_bd_read
    lfs.o(i.lfs_file_relocate) refers to lfs.o(i.lfs_bd_prog) for lfs_bd_prog
    lfs.o(i.lfs_file_relocate) refers to printfa.o(i.__0printf) for __2printf
    lfs.o(i.lfs_file_relocate) refers to memmovea.o(.text) for __aeabi_memcpy
    lfs.o(i.lfs_file_relocate) refers to lfs.o(i.lfs_cache_zero) for lfs_cache_zero
    lfs.o(i.lfs_file_rewind) refers to lfs.o(i.lfs_file_seek) for lfs_file_seek
    lfs.o(i.lfs_file_seek) refers to lfs.o(i.lfs_file_flush) for lfs_file_flush
    lfs.o(i.lfs_file_sync) refers to lfs.o(i.lfs_file_flush) for lfs_file_flush
    lfs.o(i.lfs_file_sync) refers to lfs.o(i.lfs_pair_isnull) for lfs_pair_isnull
    lfs.o(i.lfs_file_sync) refers to lfs.o(i.lfs_tole32) for lfs_tole32
    lfs.o(i.lfs_file_sync) refers to lfs.o(i.lfs_dir_commit) for lfs_dir_commit
    lfs.o(i.lfs_file_sync) refers to lfs.o(i.lfs_file_outline) for lfs_file_outline
    lfs.o(i.lfs_file_truncate) refers to lfs.o(i.lfs_file_size) for lfs_file_size
    lfs.o(i.lfs_file_truncate) refers to lfs.o(i.lfs_file_flush) for lfs_file_flush
    lfs.o(i.lfs_file_truncate) refers to lfs.o(i.lfs_ctz_find) for lfs_ctz_find
    lfs.o(i.lfs_file_truncate) refers to lfs.o(i.lfs_file_seek) for lfs_file_seek
    lfs.o(i.lfs_file_truncate) refers to lfs.o(i.lfs_file_write) for lfs_file_write
    lfs.o(i.lfs_file_write) refers to lfs.o(i.lfs_file_flush) for lfs_file_flush
    lfs.o(i.lfs_file_write) refers to lfs.o(i.lfs_min) for lfs_min
    lfs.o(i.lfs_file_write) refers to lfs.o(i.lfs_file_outline) for lfs_file_outline
    lfs.o(i.lfs_file_write) refers to lfs.o(i.lfs_ctz_find) for lfs_ctz_find
    lfs.o(i.lfs_file_write) refers to lfs.o(i.lfs_cache_zero) for lfs_cache_zero
    lfs.o(i.lfs_file_write) refers to lfs.o(i.lfs_ctz_extend) for lfs_ctz_extend
    lfs.o(i.lfs_file_write) refers to lfs.o(i.lfs_bd_prog) for lfs_bd_prog
    lfs.o(i.lfs_file_write) refers to lfs.o(i.lfs_file_relocate) for lfs_file_relocate
    lfs.o(i.lfs_format) refers to lfs.o(i.lfs_init) for lfs_init
    lfs.o(i.lfs_format) refers to memseta.o(.text) for __aeabi_memclr4
    lfs.o(i.lfs_format) refers to lfs.o(i.lfs_min) for lfs_min
    lfs.o(i.lfs_format) refers to lfs.o(i.lfs_dir_alloc) for lfs_dir_alloc
    lfs.o(i.lfs_format) refers to memmovea.o(.text) for __aeabi_memcpy4
    lfs.o(i.lfs_format) refers to lfs.o(i.lfs_tole32) for lfs_tole32
    lfs.o(i.lfs_format) refers to lfs.o(i.lfs_dir_commit) for lfs_dir_commit
    lfs.o(i.lfs_format) refers to lfs.o(i.lfs_dir_fetch) for lfs_dir_fetch
    lfs.o(i.lfs_format) refers to lfs.o(i.lfs_deinit) for lfs_deinit
    lfs.o(i.lfs_format) refers to lfs.o(.constdata) for .constdata
    lfs.o(i.lfs_fs_demove) refers to lfs.o(i.lfs_gstate_hasmove) for lfs_gstate_hasmove
    lfs.o(i.lfs_fs_demove) refers to printfa.o(i.__0printf) for __2printf
    lfs.o(i.lfs_fs_demove) refers to lfs.o(i.lfs_dir_fetch) for lfs_dir_fetch
    lfs.o(i.lfs_fs_demove) refers to lfs.o(i.lfs_dir_commit) for lfs_dir_commit
    lfs.o(i.lfs_fs_deorphan) refers to memmovea.o(.text) for __aeabi_memcpy4
    lfs.o(i.lfs_fs_deorphan) refers to lfs.o(i.lfs_dir_fetch) for lfs_dir_fetch
    lfs.o(i.lfs_fs_deorphan) refers to lfs.o(i.lfs_fs_parent) for lfs_fs_parent
    lfs.o(i.lfs_fs_deorphan) refers to lfs.o(i.lfs_dir_get) for lfs_dir_get
    lfs.o(i.lfs_fs_deorphan) refers to lfs.o(i.lfs_pair_fromle32) for lfs_pair_fromle32
    lfs.o(i.lfs_fs_deorphan) refers to printfa.o(i.__0printf) for __2printf
    lfs.o(i.lfs_fs_deorphan) refers to lfs.o(i.lfs_pair_tole32) for lfs_pair_tole32
    lfs.o(i.lfs_fs_deorphan) refers to lfs.o(i.lfs_dir_commit) for lfs_dir_commit
    lfs.o(i.lfs_fs_deorphan) refers to lfs.o(i.lfs_dir_drop) for lfs_dir_drop
    lfs.o(i.lfs_fs_deorphan) refers to lfs.o(i.lfs_pair_isnull) for lfs_pair_isnull
    lfs.o(i.lfs_fs_deorphan) refers to lfs.o(i.lfs_fs_preporphans) for lfs_fs_preporphans
    lfs.o(i.lfs_fs_deorphan) refers to lfs.o(.constdata) for .constdata
    lfs.o(i.lfs_fs_forceconsistency) refers to lfs.o(i.lfs_fs_demove) for lfs_fs_demove
    lfs.o(i.lfs_fs_forceconsistency) refers to lfs.o(i.lfs_fs_deorphan) for lfs_fs_deorphan
    lfs.o(i.lfs_fs_parent) refers to lfs.o(i.lfs_dir_fetchmatch) for lfs_dir_fetchmatch
    lfs.o(i.lfs_fs_parent) refers to lfs.o(i.lfs_pair_isnull) for lfs_pair_isnull
    lfs.o(i.lfs_fs_parent) refers to lfs.o(i.lfs_fs_parent_match) for lfs_fs_parent_match
    lfs.o(i.lfs_fs_parent_match) refers to lfs.o(i.lfs_bd_read) for lfs_bd_read
    lfs.o(i.lfs_fs_parent_match) refers to lfs.o(i.lfs_pair_fromle32) for lfs_pair_fromle32
    lfs.o(i.lfs_fs_parent_match) refers to lfs.o(i.lfs_pair_cmp) for lfs_pair_cmp
    lfs.o(i.lfs_fs_pred) refers to lfs.o(i.lfs_pair_cmp) for lfs_pair_cmp
    lfs.o(i.lfs_fs_pred) refers to lfs.o(i.lfs_dir_fetch) for lfs_dir_fetch
    lfs.o(i.lfs_fs_pred) refers to lfs.o(i.lfs_pair_isnull) for lfs_pair_isnull
    lfs.o(i.lfs_fs_preporphans) refers to lfs.o(i.lfs_gstate_hasorphans) for lfs_gstate_hasorphans
    lfs.o(i.lfs_fs_relocate) refers to lfs.o(i.lfs_pair_cmp) for lfs_pair_cmp
    lfs.o(i.lfs_fs_relocate) refers to printfa.o(i.__0printf) for __2printf
    lfs.o(i.lfs_fs_relocate) refers to lfs.o(i.lfs_fs_parent) for lfs_fs_parent
    lfs.o(i.lfs_fs_relocate) refers to lfs.o(i.lfs_fs_preporphans) for lfs_fs_preporphans
    lfs.o(i.lfs_fs_relocate) refers to lfs.o(i.lfs_pair_tole32) for lfs_pair_tole32
    lfs.o(i.lfs_fs_relocate) refers to lfs.o(i.lfs_dir_commit) for lfs_dir_commit
    lfs.o(i.lfs_fs_relocate) refers to lfs.o(i.lfs_pair_fromle32) for lfs_pair_fromle32
    lfs.o(i.lfs_fs_relocate) refers to lfs.o(i.lfs_fs_pred) for lfs_fs_pred
    lfs.o(i.lfs_fs_size) refers to lfs.o(i.lfs_fs_traverse) for lfs_fs_traverse
    lfs.o(i.lfs_fs_size) refers to lfs.o(i.lfs_fs_size_count) for lfs_fs_size_count
    lfs.o(i.lfs_fs_traverse) refers to memmovea.o(.text) for __aeabi_memcpy4
    lfs.o(i.lfs_fs_traverse) refers to lfs.o(i.lfs_dir_fetch) for lfs_dir_fetch
    lfs.o(i.lfs_fs_traverse) refers to lfs.o(i.lfs_dir_get) for lfs_dir_get
    lfs.o(i.lfs_fs_traverse) refers to lfs.o(i.lfs_ctz_fromle32) for lfs_ctz_fromle32
    lfs.o(i.lfs_fs_traverse) refers to lfs.o(i.lfs_ctz_traverse) for lfs_ctz_traverse
    lfs.o(i.lfs_fs_traverse) refers to lfs.o(i.lfs_pair_isnull) for lfs_pair_isnull
    lfs.o(i.lfs_fs_traverse) refers to lfs.o(.constdata) for .constdata
    lfs.o(i.lfs_getattr) refers to lfs.o(i.lfs_dir_find) for lfs_dir_find
    lfs.o(i.lfs_getattr) refers to lfs.o(i.lfs_dir_fetch) for lfs_dir_fetch
    lfs.o(i.lfs_getattr) refers to lfs.o(i.lfs_min) for lfs_min
    lfs.o(i.lfs_getattr) refers to lfs.o(i.lfs_dir_get) for lfs_dir_get
    lfs.o(i.lfs_gstate_fromle32) refers to lfs.o(i.lfs_fromle32) for lfs_fromle32
    lfs.o(i.lfs_gstate_hasmovehere) refers to lfs.o(i.lfs_pair_cmp) for lfs_pair_cmp
    lfs.o(i.lfs_gstate_tole32) refers to lfs.o(i.lfs_tole32) for lfs_tole32
    lfs.o(i.lfs_init) refers to lfs.o(i.lfs_malloc) for lfs_malloc
    lfs.o(i.lfs_init) refers to lfs.o(i.lfs_cache_zero) for lfs_cache_zero
    lfs.o(i.lfs_init) refers to lfs.o(i.lfs_deinit) for lfs_deinit
    lfs.o(i.lfs_init) refers to lfs.o(.constdata) for .constdata
    lfs.o(i.lfs_malloc) refers to malloc.o(i.malloc) for malloc
    lfs.o(i.lfs_mkdir) refers to lfs.o(i.lfs_fs_forceconsistency) for lfs_fs_forceconsistency
    lfs.o(i.lfs_mkdir) refers to lfs.o(i.lfs_dir_find) for lfs_dir_find
    lfs.o(i.lfs_mkdir) refers to strlen.o(.text) for strlen
    lfs.o(i.lfs_mkdir) refers to lfs.o(i.lfs_dir_alloc) for lfs_dir_alloc
    lfs.o(i.lfs_mkdir) refers to memmovea.o(.text) for __aeabi_memcpy4
    lfs.o(i.lfs_mkdir) refers to lfs.o(i.lfs_dir_fetch) for lfs_dir_fetch
    lfs.o(i.lfs_mkdir) refers to lfs.o(i.lfs_pair_tole32) for lfs_pair_tole32
    lfs.o(i.lfs_mkdir) refers to lfs.o(i.lfs_dir_commit) for lfs_dir_commit
    lfs.o(i.lfs_mkdir) refers to lfs.o(i.lfs_pair_fromle32) for lfs_pair_fromle32
    lfs.o(i.lfs_mkdir) refers to lfs.o(i.lfs_fs_preporphans) for lfs_fs_preporphans
    lfs.o(i.lfs_mkdir) refers to memseta.o(.text) for __aeabi_memclr4
    lfs.o(i.lfs_mkdir) refers to lfs.o(.constdata) for .constdata
    lfs.o(i.lfs_mount) refers to lfs.o(i.lfs_init) for lfs_init
    lfs.o(i.lfs_mount) refers to memmovea.o(.text) for __aeabi_memcpy4
    lfs.o(i.lfs_mount) refers to lfs.o(i.lfs_dir_fetchmatch) for lfs_dir_fetchmatch
    lfs.o(i.lfs_mount) refers to lfs.o(i.lfs_tag_isdelete) for lfs_tag_isdelete
    lfs.o(i.lfs_mount) refers to lfs.o(i.lfs_dir_get) for lfs_dir_get
    lfs.o(i.lfs_mount) refers to lfs.o(i.lfs_fromle32) for lfs_fromle32
    lfs.o(i.lfs_mount) refers to printfa.o(i.__0printf) for __2printf
    lfs.o(i.lfs_mount) refers to lfs.o(i.lfs_dir_getgstate) for lfs_dir_getgstate
    lfs.o(i.lfs_mount) refers to lfs.o(i.lfs_pair_isnull) for lfs_pair_isnull
    lfs.o(i.lfs_mount) refers to lfs.o(i.lfs_deinit) for lfs_deinit
    lfs.o(i.lfs_mount) refers to lfs.o(i.lfs_gstate_hasmove) for lfs_gstate_hasmove
    lfs.o(i.lfs_mount) refers to lfs.o(.constdata) for .constdata
    lfs.o(i.lfs_mount) refers to lfs.o(i.lfs_dir_find_match) for lfs_dir_find_match
    lfs.o(i.lfs_pair_fromle32) refers to lfs.o(i.lfs_fromle32) for lfs_fromle32
    lfs.o(i.lfs_pair_tole32) refers to lfs.o(i.lfs_tole32) for lfs_tole32
    lfs.o(i.lfs_remove) refers to lfs.o(i.lfs_fs_forceconsistency) for lfs_fs_forceconsistency
    lfs.o(i.lfs_remove) refers to lfs.o(i.lfs_dir_find) for lfs_dir_find
    lfs.o(i.lfs_remove) refers to lfs.o(i.lfs_dir_get) for lfs_dir_get
    lfs.o(i.lfs_remove) refers to lfs.o(i.lfs_pair_fromle32) for lfs_pair_fromle32
    lfs.o(i.lfs_remove) refers to lfs.o(i.lfs_dir_fetch) for lfs_dir_fetch
    lfs.o(i.lfs_remove) refers to lfs.o(i.lfs_fs_preporphans) for lfs_fs_preporphans
    lfs.o(i.lfs_remove) refers to lfs.o(i.lfs_dir_commit) for lfs_dir_commit
    lfs.o(i.lfs_remove) refers to lfs.o(i.lfs_fs_pred) for lfs_fs_pred
    lfs.o(i.lfs_remove) refers to lfs.o(i.lfs_dir_drop) for lfs_dir_drop
    lfs.o(i.lfs_removeattr) refers to lfs.o(i.lfs_commitattr) for lfs_commitattr
    lfs.o(i.lfs_rename) refers to lfs.o(i.lfs_fs_forceconsistency) for lfs_fs_forceconsistency
    lfs.o(i.lfs_rename) refers to lfs.o(i.lfs_dir_find) for lfs_dir_find
    lfs.o(i.lfs_rename) refers to strlen.o(.text) for strlen
    lfs.o(i.lfs_rename) refers to lfs.o(i.lfs_dir_get) for lfs_dir_get
    lfs.o(i.lfs_rename) refers to lfs.o(i.lfs_pair_fromle32) for lfs_pair_fromle32
    lfs.o(i.lfs_rename) refers to lfs.o(i.lfs_dir_fetch) for lfs_dir_fetch
    lfs.o(i.lfs_rename) refers to lfs.o(i.lfs_fs_preporphans) for lfs_fs_preporphans
    lfs.o(i.lfs_rename) refers to lfs.o(i.lfs_pair_cmp) for lfs_pair_cmp
    lfs.o(i.lfs_rename) refers to lfs.o(i.lfs_gstate_xormove) for lfs_gstate_xormove
    lfs.o(i.lfs_rename) refers to memseta.o(.text) for __aeabi_memclr4
    lfs.o(i.lfs_rename) refers to lfs.o(i.lfs_dir_commit) for lfs_dir_commit
    lfs.o(i.lfs_rename) refers to lfs.o(i.lfs_fs_pred) for lfs_fs_pred
    lfs.o(i.lfs_rename) refers to lfs.o(i.lfs_dir_drop) for lfs_dir_drop
    lfs.o(i.lfs_setattr) refers to lfs.o(i.lfs_commitattr) for lfs_commitattr
    lfs.o(i.lfs_stat) refers to lfs.o(i.lfs_dir_find) for lfs_dir_find
    lfs.o(i.lfs_stat) refers to lfs.o(i.lfs_dir_getinfo) for lfs_dir_getinfo
    lfs.o(i.lfs_tag_dsize) refers to lfs.o(i.lfs_tag_isdelete) for lfs_tag_isdelete
    lfs.o(i.lfs_tole32) refers to lfs.o(i.lfs_fromle32) for lfs_fromle32
    lfs.o(i.lfs_unmount) refers to lfs.o(i.lfs_deinit) for lfs_deinit
    lfs.o(.constdata) refers to lfs.o(.conststring) for .conststring
    lfs_port.o(i.lfs_deskio_erase) refers to gd25qxx.o(i.spi_flash_sector_erase) for spi_flash_sector_erase
    lfs_port.o(i.lfs_deskio_prog) refers to gd25qxx.o(i.spi_flash_buffer_write) for spi_flash_buffer_write
    lfs_port.o(i.lfs_deskio_read) refers to gd25qxx.o(i.spi_flash_buffer_read) for spi_flash_buffer_read
    lfs_port.o(i.lfs_storage_init) refers to lfs_port.o(i.lfs_deskio_read) for lfs_deskio_read
    lfs_port.o(i.lfs_storage_init) refers to lfs_port.o(i.lfs_deskio_prog) for lfs_deskio_prog
    lfs_port.o(i.lfs_storage_init) refers to lfs_port.o(i.lfs_deskio_erase) for lfs_deskio_erase
    lfs_port.o(i.lfs_storage_init) refers to lfs_port.o(i.lfs_deskio_sync) for lfs_deskio_sync
    lfs_port.o(i.lfs_storage_init) refers to lfs_port.o(.bss) for .bss
    lfs_util.o(i.lfs_crc) refers to lfs_util.o(.constdata) for .constdata
    wououi.o(i.WouoUI_AttachSendBuffFun) refers to wououi_graph.o(i.WouoUI_GraphSetSendBuffFun) for WouoUI_GraphSetSendBuffFun
    wououi.o(i.WouoUI_AttachSendBuffFun) refers to wououi.o(.data) for .data
    wououi.o(i.WouoUI_BlurParaInit) refers to wououi.o(.data) for .data
    wououi.o(i.WouoUI_BlurProc) refers to wououi_graph.o(i.WouoUI_GraphSetPenColor) for WouoUI_GraphSetPenColor
    wououi.o(i.WouoUI_BlurProc) refers to wououi_graph.o(i.WouoUI_BuffAllBlur) for WouoUI_BuffAllBlur
    wououi.o(i.WouoUI_BlurProc) refers to wououi.o(.data) for .data
    wououi.o(i.WouoUI_GetCurrentPage) refers to wououi.o(.data) for .data
    wououi.o(i.WouoUI_IndicatorProc) refers to wououi_anim.o(i.WouoUI_Animation) for WouoUI_Animation
    wououi.o(i.WouoUI_IndicatorProc) refers to wououi.o(.data) for .data
    wououi.o(i.WouoUI_JumpToPage) refers to wououi.o(i.WouoUI_BlurParaInit) for WouoUI_BlurParaInit
    wououi.o(i.WouoUI_JumpToPage) refers to printfa.o(i.__0printf) for __2printf
    wououi.o(i.WouoUI_JumpToPage) refers to wououi.o(.data) for .data
    wououi.o(i.WouoUI_Proc) refers to wououi_msg.o(i.WouoUI_MsgQueIsEmpty) for WouoUI_MsgQueIsEmpty
    wououi.o(i.WouoUI_Proc) refers to wououi_graph.o(i.WouoUI_BuffClear) for WouoUI_BuffClear
    wououi.o(i.WouoUI_Proc) refers to wououi.o(i.WouoUI_BlurProc) for WouoUI_BlurProc
    wououi.o(i.WouoUI_Proc) refers to wououi.o(i.WouoUI_BlurParaInit) for WouoUI_BlurParaInit
    wououi.o(i.WouoUI_Proc) refers to wououi.o(i.WouoUI_ScrollBarProc) for WouoUI_ScrollBarProc
    wououi.o(i.WouoUI_Proc) refers to wououi.o(i.WouoUI_IndicatorProc) for WouoUI_IndicatorProc
    wououi.o(i.WouoUI_Proc) refers to wououi_graph.o(i.WouoUI_BuffSend) for WouoUI_BuffSend
    wououi.o(i.WouoUI_Proc) refers to wououi.o(.data) for .data
    wououi.o(i.WouoUI_ScrollBarProc) refers to wououi_anim.o(i.WouoUI_Animation) for WouoUI_Animation
    wououi.o(i.WouoUI_ScrollBarProc) refers to wououi.o(i.WouoUI_FuncDoNothing) for WouoUI_FuncDoNothing
    wououi.o(i.WouoUI_ScrollBarProc) refers to wououi.o(.data) for .data
    wououi.o(i.WouoUI_SelectDefaultUI) refers to wououi.o(i.WouoUI_SetCurrentUI) for WouoUI_SetCurrentUI
    wououi.o(i.WouoUI_SelectDefaultUI) refers to wououi.o(.data) for .data
    wououi.o(i.WouoUI_SetCurrentUI) refers to wououi_graph.o(i.WouoUI_GraphSetBuff) for WouoUI_GraphSetBuff
    wououi.o(i.WouoUI_SetCurrentUI) refers to wououi_graph.o(i.WouoUI_GraphSetPen) for WouoUI_GraphSetPen
    wououi.o(i.WouoUI_SetCurrentUI) refers to wououi_graph.o(i.WouoUI_GraphSetSendBuffFun) for WouoUI_GraphSetSendBuffFun
    wououi.o(i.WouoUI_SetCurrentUI) refers to wououi.o(.data) for .data
    wououi.o(i.ui_ftoa_f) refers to strlen.o(.text) for strlen
    wououi.o(i.ui_ftoa_f) refers to memseta.o(.text) for __aeabi_memclr
    wououi.o(i.ui_ftoa_f) refers to printfa.o(i.__0sprintf) for __2sprintf
    wououi.o(i.ui_ftoa_f) refers to f2d.o(.text) for __aeabi_f2d
    wououi.o(i.ui_ftoa_f) refers to wououi.o(.bss) for .bss
    wououi.o(i.ui_ftoa_f_str) refers to wououi.o(i.ui_ftoa_f) for ui_ftoa_f
    wououi.o(i.ui_ftoa_f_str) refers to strcpy.o(.text) for strcpy
    wououi.o(i.ui_ftoa_g) refers to strlen.o(.text) for strlen
    wououi.o(i.ui_ftoa_g) refers to memseta.o(.text) for __aeabi_memclr
    wououi.o(i.ui_ftoa_g) refers to printfa.o(i.__0sprintf) for __2sprintf
    wououi.o(i.ui_ftoa_g) refers to f2d.o(.text) for __aeabi_f2d
    wououi.o(i.ui_ftoa_g) refers to wououi.o(.bss) for .bss
    wououi.o(i.ui_ftoa_g_str) refers to strlen.o(.text) for strlen
    wououi.o(i.ui_ftoa_g_str) refers to memseta.o(.text) for __aeabi_memclr
    wououi.o(i.ui_ftoa_g_str) refers to wououi.o(i.ui_ftoa_g) for ui_ftoa_g
    wououi.o(i.ui_ftoa_g_str) refers to strcpy.o(.text) for strcpy
    wououi.o(i.ui_itoa) refers to strlen.o(.text) for strlen
    wououi.o(i.ui_itoa) refers to memseta.o(.text) for __aeabi_memclr
    wououi.o(i.ui_itoa) refers to printfa.o(i.__0sprintf) for __2sprintf
    wououi.o(i.ui_itoa_str) refers to strlen.o(.text) for strlen
    wououi.o(i.ui_itoa_str) refers to memseta.o(.text) for __aeabi_memclr
    wououi.o(i.ui_itoa_str) refers to printfa.o(i.__0sprintf) for __2sprintf
    wououi.o(.data) refers to wououi.o(.data) for g_default_ui_para
    wououi.o(.data) refers to wououi_page.o(i.WouoUI_TitlePageIn) for WouoUI_TitlePageIn
    wououi.o(.data) refers to wououi_page.o(i.WouoUI_TitlePageInParaInit) for WouoUI_TitlePageInParaInit
    wououi.o(.data) refers to wououi_page.o(i.WouoUI_TitlePageShow) for WouoUI_TitlePageShow
    wououi.o(.data) refers to wououi_page.o(i.WouoUI_TitlePageReact) for WouoUI_TitlePageReact
    wououi.o(.data) refers to wououi_page.o(i.WouoUI_TitlePageIndicatorCtrl) for WouoUI_TitlePageIndicatorCtrl
    wououi.o(.data) refers to wououi.o(i.WouoUI_FuncDoNothing) for WouoUI_FuncDoNothing
    wououi.o(.data) refers to wououi_page.o(i.WouoUI_ListPageIn) for WouoUI_ListPageIn
    wououi.o(.data) refers to wououi_page.o(i.WouoUI_ListPageInParaInit) for WouoUI_ListPageInParaInit
    wououi.o(.data) refers to wououi_page.o(i.WouoUI_ListPageShow) for WouoUI_ListPageShow
    wououi.o(.data) refers to wououi_page.o(i.WouoUI_ListPageReact) for WouoUI_ListPageReact
    wououi.o(.data) refers to wououi_page.o(i.WouoUI_ListPageIndicatorCtrl) for WouoUI_ListPageIndicatorCtrl
    wououi.o(.data) refers to wououi_page.o(i.WouoUI_ListPageScrollBarCtrl) for WouoUI_ListPageScrollBarCtrl
    wououi.o(.data) refers to wououi.o(i.WouoUI_FuncDoNothingRetTrue) for WouoUI_FuncDoNothingRetTrue
    wououi.o(.data) refers to wououi_page.o(i.WouoUI_WavePageInParaInit) for WouoUI_WavePageInParaInit
    wououi.o(.data) refers to wououi_page.o(i.WouoUI_WavePageShow) for WouoUI_WavePageShow
    wououi.o(.data) refers to wououi_page.o(i.WouoUI_WavePageReact) for WouoUI_WavePageReact
    wououi.o(.data) refers to wououi_page.o(i.WouoUI_WavePageIndicatorCtrl) for WouoUI_WavePageIndicatorCtrl
    wououi.o(.data) refers to wououi_win.o(i.WouoUI_MsgWinPageIn) for WouoUI_MsgWinPageIn
    wououi.o(.data) refers to wououi_win.o(i.WouoUI_MsgWinPageInParaInit) for WouoUI_MsgWinPageInParaInit
    wououi.o(.data) refers to wououi_win.o(i.WouoUI_MsgWinPageShow) for WouoUI_MsgWinPageShow
    wououi.o(.data) refers to wououi_win.o(i.WouoUI_MsgWinPageReact) for WouoUI_MsgWinPageReact
    wououi.o(.data) refers to wououi_win.o(i.WouoUI_MsgWinPageIndicatorCtrl) for WouoUI_MsgWinPageIndicatorCtrl
    wououi.o(.data) refers to wououi_win.o(i.WouoUI_ConfWinPageIn) for WouoUI_ConfWinPageIn
    wououi.o(.data) refers to wououi_win.o(i.WouoUI_ConfWinPageInParaInit) for WouoUI_ConfWinPageInParaInit
    wououi.o(.data) refers to wououi_win.o(i.WouoUI_ConfWinPageShow) for WouoUI_ConfWinPageShow
    wououi.o(.data) refers to wououi_win.o(i.WouoUI_ConfWinPageReact) for WouoUI_ConfWinPageReact
    wououi.o(.data) refers to wououi_win.o(i.WouoUI_ConfWinPageIndicatorCtrl) for WouoUI_ConfWinPageIndicatorCtrl
    wououi.o(.data) refers to wououi_win.o(i.WouoUI_ValWinPageIn) for WouoUI_ValWinPageIn
    wououi.o(.data) refers to wououi_win.o(i.WouoUI_ValWinPageInParaInit) for WouoUI_ValWinPageInParaInit
    wououi.o(.data) refers to wououi_win.o(i.WouoUI_ValWinPageShow) for WouoUI_ValWinPageShow
    wououi.o(.data) refers to wououi_win.o(i.WouoUI_ValWinPageReact) for WouoUI_ValWinPageReact
    wououi.o(.data) refers to wououi_win.o(i.WouoUI_ValWinPageIndicatorCtrl) for WouoUI_ValWinPageIndicatorCtrl
    wououi.o(.data) refers to wououi_win.o(i.WouoUI_SpinWinPageIn) for WouoUI_SpinWinPageIn
    wououi.o(.data) refers to wououi_win.o(i.WouoUI_SpinWinPageInParaInit) for WouoUI_SpinWinPageInParaInit
    wououi.o(.data) refers to wououi_win.o(i.WouoUI_SpinWinPageShow) for WouoUI_SpinWinPageShow
    wououi.o(.data) refers to wououi_win.o(i.WouoUI_SpinWinPageReact) for WouoUI_SpinWinPageReact
    wououi.o(.data) refers to wououi_win.o(i.WouoUI_SpinWinPageIndicatorCtrl) for WouoUI_SpinWinPageIndicatorCtrl
    wououi.o(.data) refers to wououi_win.o(i.WouoUI_ListWinPageIn) for WouoUI_ListWinPageIn
    wououi.o(.data) refers to wououi_win.o(i.WouoUI_ListWinPageInParaInit) for WouoUI_ListWinPageInParaInit
    wououi.o(.data) refers to wououi_win.o(i.WouoUI_ListWinPageShow) for WouoUI_ListWinPageShow
    wououi.o(.data) refers to wououi_win.o(i.WouoUI_ListWinPageReact) for WouoUI_ListWinPageReact
    wououi.o(.data) refers to wououi_win.o(i.WouoUI_ListWinPageIndicatorCtrl) for WouoUI_ListWinPageIndicatorCtrl
    wououi.o(.data) refers to wououi_win.o(i.WouoUI_ListWinPageScrollBarCtrl) for WouoUI_ListWinPageScrollBarCtrl
    wououi_font.o(.constdata) refers to wououi_font.o(.constdata) for F6x8
    wououi_font.o(.constdata) refers to wououi_font.o(.constdata) for F6x12
    wououi_font.o(.constdata) refers to wououi_font.o(.constdata) for F7x12
    wououi_font.o(.constdata) refers to wououi_font.o(.constdata) for F8X16
    wououi_font.o(.constdata) refers to wououi_font.o(.constdata) for F12X24
    wououi_graph.o(i.WouoUI_BuffAllBlur) refers to wououi_graph.o(i.WouoUI_BuffWriteByte) for WouoUI_BuffWriteByte
    wououi_graph.o(i.WouoUI_BuffAllBlur) refers to wououi_graph.o(.constdata) for .constdata
    wououi_graph.o(i.WouoUI_BuffClear) refers to memseta.o(.text) for __aeabi_memclr
    wououi_graph.o(i.WouoUI_BuffClear) refers to wououi_graph.o(.data) for .data
    wououi_graph.o(i.WouoUI_BuffSend) refers to wououi_graph.o(.data) for .data
    wououi_graph.o(i.WouoUI_BuffWriteByte) refers to wououi_graph.o(.data) for .data
    wououi_graph.o(i.WouoUI_CanvasDrawASCII) refers to wououi_graph.o(i.WouoUI_CanvasWriteByte) for WouoUI_CanvasWriteByte
    wououi_graph.o(i.WouoUI_CanvasDrawBMP) refers to wououi_graph.o(i.WouoUI_CanvasWriteByte) for WouoUI_CanvasWriteByte
    wououi_graph.o(i.WouoUI_CanvasDrawBoxRightAngle) refers to wououi_graph.o(i.WouoUI_CanvasDrawLine_H) for WouoUI_CanvasDrawLine_H
    wououi_graph.o(i.WouoUI_CanvasDrawBoxRightAngle) refers to wououi_graph.o(i.WouoUI_CanvasDrawLine_V) for WouoUI_CanvasDrawLine_V
    wououi_graph.o(i.WouoUI_CanvasDrawDashedLine_V) refers to wououi_graph.o(i.WouoUI_CanvasWriteByte) for WouoUI_CanvasWriteByte
    wououi_graph.o(i.WouoUI_CanvasDrawLine) refers to wououi_graph.o(i.WouoUI_CanvasWriteByte) for WouoUI_CanvasWriteByte
    wououi_graph.o(i.WouoUI_CanvasDrawLine_H) refers to wououi_graph.o(i.WouoUI_CanvasWriteByte) for WouoUI_CanvasWriteByte
    wououi_graph.o(i.WouoUI_CanvasDrawLine_V) refers to wououi_graph.o(i.WouoUI_CanvasWriteByte) for WouoUI_CanvasWriteByte
    wououi_graph.o(i.WouoUI_CanvasDrawPoint) refers to wououi_graph.o(i.WouoUI_CanvasWriteByte) for WouoUI_CanvasWriteByte
    wououi_graph.o(i.WouoUI_CanvasDrawRBox) refers to wououi_graph.o(i.WouoUI_CanvasDrawRBoxCommon) for WouoUI_CanvasDrawRBoxCommon
    wououi_graph.o(i.WouoUI_CanvasDrawRBoxCommon) refers to wououi_graph.o(i.WouoUI_CanvasDrawLine_H) for WouoUI_CanvasDrawLine_H
    wououi_graph.o(i.WouoUI_CanvasDrawRBoxCommon) refers to wououi_graph.o(i.WouoUI_CanvasWriteByte) for WouoUI_CanvasWriteByte
    wououi_graph.o(i.WouoUI_CanvasDrawRBoxEmpty) refers to wououi_graph.o(i.WouoUI_CanvasDrawRBoxCommon) for WouoUI_CanvasDrawRBoxCommon
    wououi_graph.o(i.WouoUI_CanvasDrawSlideStr) refers to wououi_graph.o(i.WouoUI_CanvasDrawStr) for WouoUI_CanvasDrawStr
    wououi_graph.o(i.WouoUI_CanvasDrawSlideStr) refers to wououi_graph.o(i.WouoUI_GetStrWidth) for WouoUI_GetStrWidth
    wououi_graph.o(i.WouoUI_CanvasDrawStr) refers to wououi_graph.o(i.WouoUI_CanvasDrawASCII) for WouoUI_CanvasDrawASCII
    wououi_graph.o(i.WouoUI_CanvasDrawStrAutoNewline) refers to wououi_graph.o(i.WouoUI_CanvasDrawASCII) for WouoUI_CanvasDrawASCII
    wououi_graph.o(i.WouoUI_CanvasDrawStrWithNewline) refers to wououi_graph.o(i.WouoUI_CanvasDrawASCII) for WouoUI_CanvasDrawASCII
    wououi_graph.o(i.WouoUI_CanvasWriteByte) refers to wououi_graph.o(i.WouoUI_BuffWriteByte) for WouoUI_BuffWriteByte
    wououi_graph.o(i.WouoUI_GetStrWidth) refers to strlen.o(.text) for strlen
    wououi_graph.o(i.WouoUI_GraphDrawDashedLine_H) refers to wououi_graph.o(i.WouoUI_CanvasWriteByte) for WouoUI_CanvasWriteByte
    wououi_graph.o(i.WouoUI_GraphReversePenColor) refers to wououi_graph.o(.data) for .data
    wououi_graph.o(i.WouoUI_GraphSetBuff) refers to wououi_graph.o(.data) for .data
    wououi_graph.o(i.WouoUI_GraphSetPen) refers to wououi_graph.o(.data) for .data
    wououi_graph.o(i.WouoUI_GraphSetPenColor) refers to wououi_graph.o(.data) for .data
    wououi_graph.o(i.WouoUI_GraphSetSendBuffFun) refers to wououi_graph.o(.data) for .data
    wououi_msg.o(i.WouoUI_MsgQueClear) refers to wououi_msg.o(i.WouoUI_MsgQueIsEmpty) for WouoUI_MsgQueIsEmpty
    wououi_msg.o(i.WouoUI_MsgQueRead) refers to wououi_msg.o(i.WouoUI_MsgQueIsEmpty) for WouoUI_MsgQueIsEmpty
    wououi_msg.o(i.WouoUI_MsgQueSend) refers to wououi_msg.o(i.WouoUI_MsgQueIsFull) for WouoUI_MsgQueIsFull
    wououi_page.o(i.WouoUI_ListAuotCanvasDrawLineTailValTxt) refers to wououi_graph.o(i.WouoUI_GetStrWidth) for WouoUI_GetStrWidth
    wououi_page.o(i.WouoUI_ListAuotCanvasDrawLineTailValTxt) refers to wououi_graph.o(i.WouoUI_CanvasDrawSlideStr) for WouoUI_CanvasDrawSlideStr
    wououi_page.o(i.WouoUI_ListAuotCanvasDrawLineTailValTxt) refers to wououi_graph.o(i.WouoUI_CanvasDrawStr) for WouoUI_CanvasDrawStr
    wououi_page.o(i.WouoUI_ListAuotCanvasDrawLineTailValTxt) refers to wououi_font.o(.constdata) for Font_6_8
    wououi_page.o(i.WouoUI_ListAuotCanvasDrawLineTailValTxt) refers to wououi.o(.data) for p_cur_ui
    wououi_page.o(i.WouoUI_ListDrawText_CheckBox) refers to strchr.o(.text) for strchr
    wououi_page.o(i.WouoUI_ListDrawText_CheckBox) refers to wououi.o(i.ui_itoa_str) for ui_itoa_str
    wououi_page.o(i.WouoUI_ListDrawText_CheckBox) refers to wououi.o(i.ui_ftoa_f_str) for ui_ftoa_f_str
    wououi_page.o(i.WouoUI_ListDrawText_CheckBox) refers to wououi_graph.o(i.WouoUI_GetStrWidth) for WouoUI_GetStrWidth
    wououi_page.o(i.WouoUI_ListDrawText_CheckBox) refers to wououi_graph.o(i.WouoUI_CanvasDrawRBoxEmpty) for WouoUI_CanvasDrawRBoxEmpty
    wououi_page.o(i.WouoUI_ListDrawText_CheckBox) refers to wououi_graph.o(i.WouoUI_CanvasDrawRBox) for WouoUI_CanvasDrawRBox
    wououi_page.o(i.WouoUI_ListDrawText_CheckBox) refers to wououi_page.o(i.WouoUI_ListAuotCanvasDrawLineTailValTxt) for WouoUI_ListAuotCanvasDrawLineTailValTxt
    wououi_page.o(i.WouoUI_ListDrawText_CheckBox) refers to wououi_graph.o(i.WouoUI_CanvasDrawStr) for WouoUI_CanvasDrawStr
    wououi_page.o(i.WouoUI_ListDrawText_CheckBox) refers to wououi_graph.o(i.WouoUI_CanvasDrawSlideStr) for WouoUI_CanvasDrawSlideStr
    wououi_page.o(i.WouoUI_ListDrawText_CheckBox) refers to wououi_page.o(.constdata) for .constdata
    wououi_page.o(i.WouoUI_ListDrawText_CheckBox) refers to wououi_font.o(.constdata) for Font_6_8
    wououi_page.o(i.WouoUI_ListDrawText_CheckBox) refers to wououi.o(.data) for p_cur_ui
    wououi_page.o(i.WouoUI_ListPageIn) refers to wououi_anim.o(i.WouoUI_Animation) for WouoUI_Animation
    wououi_page.o(i.WouoUI_ListPageIn) refers to wououi_page.o(i.WouoUI_ListDrawText_CheckBox) for WouoUI_ListDrawText_CheckBox
    wououi_page.o(i.WouoUI_ListPageIn) refers to wououi_font.o(.constdata) for Font_6_8
    wououi_page.o(i.WouoUI_ListPageIn) refers to wououi.o(.data) for p_cur_ui
    wououi_page.o(i.WouoUI_ListPageInParaInit) refers to wououi_graph.o(i.WouoUI_CanvasSlideStrReset) for WouoUI_CanvasSlideStrReset
    wououi_page.o(i.WouoUI_ListPageInParaInit) refers to wououi.o(.data) for p_cur_ui
    wououi_page.o(i.WouoUI_ListPageInParaInit) refers to wououi_font.o(.constdata) for Font_6_8
    wououi_page.o(i.WouoUI_ListPageIndicatorCtrl) refers to wououi_graph.o(i.WouoUI_GraphSetPenColor) for WouoUI_GraphSetPenColor
    wououi_page.o(i.WouoUI_ListPageIndicatorCtrl) refers to wououi_graph.o(i.WouoUI_CanvasDrawRBox) for WouoUI_CanvasDrawRBox
    wououi_page.o(i.WouoUI_ListPageIndicatorCtrl) refers to wououi.o(.data) for p_cur_ui
    wououi_page.o(i.WouoUI_ListPageIndicatorCtrl) refers to wououi_font.o(.constdata) for Font_6_8
    wououi_page.o(i.WouoUI_ListPageInit) refers to printfa.o(i.__0printf) for __2printf
    wououi_page.o(i.WouoUI_ListPageInit) refers to wououi_page.o(i.WouoUI_PageInit) for WouoUI_PageInit
    wououi_page.o(i.WouoUI_ListPageInit) refers to wououi_font.o(.constdata) for Font_6_8
    wououi_page.o(i.WouoUI_ListPageInit) refers to wououi_page.o(.conststring) for .conststring
    wououi_page.o(i.WouoUI_ListPageInit) refers to wououi.o(.data) for p_cur_ui
    wououi_page.o(i.WouoUI_ListPageLastItem) refers to wououi_graph.o(i.WouoUI_CanvasSlideStrReset) for WouoUI_CanvasSlideStrReset
    wououi_page.o(i.WouoUI_ListPageLastItem) refers to wououi.o(.data) for p_cur_ui
    wououi_page.o(i.WouoUI_ListPageLastItem) refers to wououi_font.o(.constdata) for Font_6_8
    wououi_page.o(i.WouoUI_ListPageNextItem) refers to wououi_graph.o(i.WouoUI_CanvasSlideStrReset) for WouoUI_CanvasSlideStrReset
    wououi_page.o(i.WouoUI_ListPageNextItem) refers to wououi.o(.data) for p_cur_ui
    wououi_page.o(i.WouoUI_ListPageNextItem) refers to wououi_font.o(.constdata) for Font_6_8
    wououi_page.o(i.WouoUI_ListPageReact) refers to wououi_msg.o(i.WouoUI_MsgQueRead) for WouoUI_MsgQueRead
    wououi_page.o(i.WouoUI_ListPageReact) refers to wououi_page.o(i.WouoUI_ListPageLastItem) for WouoUI_ListPageLastItem
    wououi_page.o(i.WouoUI_ListPageReact) refers to wououi_page.o(i.WouoUI_ListPageNextItem) for WouoUI_ListPageNextItem
    wououi_page.o(i.WouoUI_ListPageReact) refers to strchr.o(.text) for strchr
    wououi_page.o(i.WouoUI_ListPageReact) refers to wououi_page.o(i.WouoUI_PageReturn) for WouoUI_PageReturn
    wououi_page.o(i.WouoUI_ListPageReact) refers to wououi.o(.data) for p_cur_ui
    wououi_page.o(i.WouoUI_ListPageScrollBarCtrl) refers to wououi_graph.o(i.WouoUI_CanvasDrawLine_H) for WouoUI_CanvasDrawLine_H
    wououi_page.o(i.WouoUI_ListPageScrollBarCtrl) refers to wououi_graph.o(i.WouoUI_CanvasDrawLine_V) for WouoUI_CanvasDrawLine_V
    wououi_page.o(i.WouoUI_ListPageScrollBarCtrl) refers to wououi_graph.o(i.WouoUI_CanvasDrawRBox) for WouoUI_CanvasDrawRBox
    wououi_page.o(i.WouoUI_ListPageScrollBarCtrl) refers to wououi.o(.data) for p_cur_ui
    wououi_page.o(i.WouoUI_ListPageShow) refers to wououi_anim.o(i.WouoUI_Animation) for WouoUI_Animation
    wououi_page.o(i.WouoUI_ListPageShow) refers to wououi_page.o(i.WouoUI_ListDrawText_CheckBox) for WouoUI_ListDrawText_CheckBox
    wououi_page.o(i.WouoUI_ListPageShow) refers to wououi_font.o(.constdata) for Font_6_8
    wououi_page.o(i.WouoUI_ListPageShow) refers to wououi.o(.data) for p_cur_ui
    wououi_page.o(i.WouoUI_ListTitlePageGetSelectOpt) refers to printfa.o(i.__0printf) for __2printf
    wououi_page.o(i.WouoUI_ListTitlePageGetSelectOpt) refers to wououi_page.o(.conststring) for .conststring
    wououi_page.o(i.WouoUI_PageInit) refers to wououi.o(.data) for p_cur_ui
    wououi_page.o(i.WouoUI_PageReturn) refers to wououi.o(.data) for p_cur_ui
    wououi_page.o(i.WouoUI_SetPageAutoDealWithMsg) refers to printfa.o(i.__0printf) for __2printf
    wououi_page.o(i.WouoUI_SetPageAutoDealWithMsg) refers to wououi_page.o(.conststring) for .conststring
    wououi_page.o(i.WouoUI_TItlePageNextItem) refers to wououi_graph.o(i.WouoUI_CanvasSlideStrReset) for WouoUI_CanvasSlideStrReset
    wououi_page.o(i.WouoUI_TItlePageNextItem) refers to wououi.o(.data) for p_cur_ui
    wououi_page.o(i.WouoUI_TitlePageIn) refers to wououi_anim.o(i.WouoUI_Animation) for WouoUI_Animation
    wououi_page.o(i.WouoUI_TitlePageIn) refers to wououi_graph.o(i.WouoUI_CanvasDrawRBox) for WouoUI_CanvasDrawRBox
    wououi_page.o(i.WouoUI_TitlePageIn) refers to wououi_graph.o(i.WouoUI_CanvasDrawBMP) for WouoUI_CanvasDrawBMP
    wououi_page.o(i.WouoUI_TitlePageIn) refers to wououi.o(.data) for p_cur_ui
    wououi_page.o(i.WouoUI_TitlePageInParaInit) refers to wououi_graph.o(i.WouoUI_CanvasSlideStrReset) for WouoUI_CanvasSlideStrReset
    wououi_page.o(i.WouoUI_TitlePageInParaInit) refers to wououi.o(.data) for p_cur_ui
    wououi_page.o(i.WouoUI_TitlePageIndicatorCtrl) refers to wououi_graph.o(i.WouoUI_GraphSetPenColor) for WouoUI_GraphSetPenColor
    wououi_page.o(i.WouoUI_TitlePageIndicatorCtrl) refers to wououi_graph.o(i.WouoUI_CanvasDrawBoxRightAngle) for WouoUI_CanvasDrawBoxRightAngle
    wououi_page.o(i.WouoUI_TitlePageIndicatorCtrl) refers to wououi.o(.data) for p_cur_ui
    wououi_page.o(i.WouoUI_TitlePageInit) refers to wououi_page.o(i.WouoUI_PageInit) for WouoUI_PageInit
    wououi_page.o(i.WouoUI_TitlePageInit) refers to printfa.o(i.__0printf) for __2printf
    wououi_page.o(i.WouoUI_TitlePageInit) refers to wououi_page.o(i.WouoUI_TitlePageInParaInit) for WouoUI_TitlePageInParaInit
    wououi_page.o(i.WouoUI_TitlePageInit) refers to wououi.o(.data) for p_cur_ui
    wououi_page.o(i.WouoUI_TitlePageLastItem) refers to wououi_graph.o(i.WouoUI_CanvasSlideStrReset) for WouoUI_CanvasSlideStrReset
    wououi_page.o(i.WouoUI_TitlePageLastItem) refers to wououi.o(.data) for p_cur_ui
    wououi_page.o(i.WouoUI_TitlePageReact) refers to wououi_msg.o(i.WouoUI_MsgQueRead) for WouoUI_MsgQueRead
    wououi_page.o(i.WouoUI_TitlePageReact) refers to wououi_page.o(i.WouoUI_TitlePageLastItem) for WouoUI_TitlePageLastItem
    wououi_page.o(i.WouoUI_TitlePageReact) refers to wououi_page.o(i.WouoUI_TItlePageNextItem) for WouoUI_TItlePageNextItem
    wououi_page.o(i.WouoUI_TitlePageReact) refers to wououi_page.o(i.WouoUI_PageReturn) for WouoUI_PageReturn
    wououi_page.o(i.WouoUI_TitlePageReact) refers to wououi.o(.data) for p_cur_ui
    wououi_page.o(i.WouoUI_TitlePageShow) refers to wououi_anim.o(i.WouoUI_Animation) for WouoUI_Animation
    wououi_page.o(i.WouoUI_TitlePageShow) refers to wououi_graph.o(i.WouoUI_CanvasDrawRBox) for WouoUI_CanvasDrawRBox
    wououi_page.o(i.WouoUI_TitlePageShow) refers to wououi_graph.o(i.WouoUI_CanvasDrawBMP) for WouoUI_CanvasDrawBMP
    wououi_page.o(i.WouoUI_TitlePageShow) refers to wououi.o(.data) for p_cur_ui
    wououi_page.o(i.WouoUI_WavePageCanShiftWave) refers to printfa.o(i.__0printf) for __2printf
    wououi_page.o(i.WouoUI_WavePageInParaInit) refers to wououi_page.o(i._WouoUI_WaveAnimParaReaset) for _WouoUI_WaveAnimParaReaset
    wououi_page.o(i.WouoUI_WavePageInParaInit) refers to wououi.o(.data) for p_cur_ui
    wououi_page.o(i.WouoUI_WavePageIndicatorCtrl) refers to wououi_graph.o(i.WouoUI_CanvasDrawRBoxEmpty) for WouoUI_CanvasDrawRBoxEmpty
    wououi_page.o(i.WouoUI_WavePageIndicatorCtrl) refers to wououi.o(.data) for p_cur_ui
    wououi_page.o(i.WouoUI_WavePageIndicatorCtrl) refers to wououi_font.o(.constdata) for Font_6_8
    wououi_page.o(i.WouoUI_WavePageInit) refers to wououi_page.o(i.WouoUI_PageInit) for WouoUI_PageInit
    wououi_page.o(i.WouoUI_WavePageInit) refers to wououi.o(.data) for p_cur_ui
    wououi_page.o(i.WouoUI_WavePageLeftShiftWave) refers to wououi_page.o(i.WouoUI_WavePageCanShiftWave) for WouoUI_WavePageCanShiftWave
    wououi_page.o(i.WouoUI_WavePageLeftShiftWave) refers to wououi_page.o(i._WouoUI_WaveUpdateRange) for _WouoUI_WaveUpdateRange
    wououi_page.o(i.WouoUI_WavePageReact) refers to wououi_msg.o(i.WouoUI_MsgQueRead) for WouoUI_MsgQueRead
    wououi_page.o(i.WouoUI_WavePageReact) refers to wououi_page.o(i.WouoUI_WavePageShowLastWaveData) for WouoUI_WavePageShowLastWaveData
    wououi_page.o(i.WouoUI_WavePageReact) refers to wououi_page.o(i.WouoUI_WavePageLeftShiftWave) for WouoUI_WavePageLeftShiftWave
    wououi_page.o(i.WouoUI_WavePageReact) refers to wououi_page.o(i.WouoUI_WavePageShowNextWaveData) for WouoUI_WavePageShowNextWaveData
    wououi_page.o(i.WouoUI_WavePageReact) refers to wououi_page.o(i.WouoUI_WavePageRightShiftWave) for WouoUI_WavePageRightShiftWave
    wououi_page.o(i.WouoUI_WavePageReact) refers to wououi_page.o(i.WouoUI_WavePageStopRestartWave) for WouoUI_WavePageStopRestartWave
    wououi_page.o(i.WouoUI_WavePageReact) refers to wououi_page.o(i.WouoUI_PageReturn) for WouoUI_PageReturn
    wououi_page.o(i.WouoUI_WavePageReact) refers to wououi.o(.data) for p_cur_ui
    wououi_page.o(i.WouoUI_WavePageRightShiftWave) refers to wououi_page.o(i.WouoUI_WavePageCanShiftWave) for WouoUI_WavePageCanShiftWave
    wououi_page.o(i.WouoUI_WavePageRightShiftWave) refers to wououi_page.o(i._WouoUI_WaveUpdateRange) for _WouoUI_WaveUpdateRange
    wououi_page.o(i.WouoUI_WavePageShow) refers to wououi_graph.o(i.WouoUI_CanvasDrawLine_V) for WouoUI_CanvasDrawLine_V
    wououi_page.o(i.WouoUI_WavePageShow) refers to wououi_graph.o(i.WouoUI_CanvasDrawPoint) for WouoUI_CanvasDrawPoint
    wououi_page.o(i.WouoUI_WavePageShow) refers to wououi_graph.o(i.WouoUI_CanvasDrawRBox) for WouoUI_CanvasDrawRBox
    wououi_page.o(i.WouoUI_WavePageShow) refers to wououi_anim.o(i.WouoUI_Animation) for WouoUI_Animation
    wououi_page.o(i.WouoUI_WavePageShow) refers to wououi_graph.o(i.WouoUI_CanvasDrawSlideStr) for WouoUI_CanvasDrawSlideStr
    wououi_page.o(i.WouoUI_WavePageShow) refers to wououi.o(i.ui_ftoa_g) for ui_ftoa_g
    wououi_page.o(i.WouoUI_WavePageShow) refers to wououi_font.o(.constdata) for Font_6_8
    wououi_page.o(i.WouoUI_WavePageShow) refers to wououi.o(.data) for p_cur_ui
    wououi_page.o(i.WouoUI_WavePageShowLastWaveData) refers to wououi_page.o(i._WouoUI_WaveAnimParaReaset) for _WouoUI_WaveAnimParaReaset
    wououi_page.o(i.WouoUI_WavePageShowNextWaveData) refers to wououi_page.o(i._WouoUI_WaveAnimParaReaset) for _WouoUI_WaveAnimParaReaset
    wououi_page.o(i.WouoUI_WavePageStopRestartWave) refers to printfa.o(i.__0printf) for __2printf
    wououi_page.o(i.WouoUI_WavePageStopRestartWave) refers to wououi_page.o(i._WouoUI_WaveUpdateRange) for _WouoUI_WaveUpdateRange
    wououi_page.o(i.WouoUI_WavePageUpdateVal) refers to printfa.o(i.__0printf) for __2printf
    wououi_page.o(i.WouoUI_WavePageUpdateVal) refers to wououi.o(i.WouoUI_GetCurrentPage) for WouoUI_GetCurrentPage
    wououi_page.o(i.WouoUI_WavePageUpdateVal) refers to wououi_page.o(i._WouoUI_WaveUpdateRange) for _WouoUI_WaveUpdateRange
    wououi_page.o(i.WouoUI_WavePageUpdateVal) refers to wououi.o(.data) for p_cur_ui
    wououi_page.o(i._WouoUI_WaveAnimParaReaset) refers to wououi_graph.o(i.WouoUI_CanvasSlideStrReset) for WouoUI_CanvasSlideStrReset
    wououi_page.o(i._WouoUI_WaveAnimParaReaset) refers to wououi.o(.data) for p_cur_ui
    wououi_page.o(i._WouoUI_WaveAnimParaReaset) refers to wououi_font.o(.constdata) for Font_6_8
    wououi_page.o(i._WouoUI_WaveUpdateRange) refers to wououi_page.o(i._WouoUI_indexIsInShowRange) for _WouoUI_indexIsInShowRange
    wououi_page.o(i._WouoUI_WaveUpdateRange) refers to wououi_page.o(i._roundToNearestTen) for _roundToNearestTen
    wououi_user.o(i.About_CallBack) refers to wououi_page.o(i.WouoUI_ListTitlePageGetSelectOpt) for WouoUI_ListTitlePageGetSelectOpt
    wououi_user.o(i.About_CallBack) refers to wououi.o(i.WouoUI_JumpToPage) for WouoUI_JumpToPage
    wououi_user.o(i.About_CallBack) refers to wououi_user.o(.bss) for .bss
    wououi_user.o(i.BgBlurSelPage_CallBack) refers to strcmp.o(.text) for strcmp
    wououi_user.o(i.BgBlurSelPage_CallBack) refers to wououi.o(i.WouoUI_JumpToPage) for WouoUI_JumpToPage
    wououi_user.o(i.BgBlurSelPage_CallBack) refers to wououi_win.o(i.WouoUI_ListWinPageLastItem) for WouoUI_ListWinPageLastItem
    wououi_user.o(i.BgBlurSelPage_CallBack) refers to wououi_win.o(i.WouoUI_ListWinPageNextItem) for WouoUI_ListWinPageNextItem
    wououi_user.o(i.BgBlurSelPage_CallBack) refers to wououi_user.o(.bss) for .bss
    wououi_user.o(i.BgBlurSelPage_CallBack) refers to wououi.o(.data) for g_default_ui_para
    wououi_user.o(i.CommonConfPage_CallBack) refers to wououi_user.o(.bss) for .bss
    wououi_user.o(i.CommonConfPage_CallBack) refers to wououi.o(.data) for g_default_ui_para
    wououi_user.o(i.CommonValPage_CallBack) refers to strcmp.o(.text) for strcmp
    wououi_user.o(i.CommonValPage_CallBack) refers to wououi_user.o(.bss) for .bss
    wououi_user.o(i.CommonValPage_CallBack) refers to wououi.o(.data) for g_default_ui_para
    wououi_user.o(i.MainPage_CallBack) refers to wououi_page.o(i.WouoUI_ListTitlePageGetSelectOpt) for WouoUI_ListTitlePageGetSelectOpt
    wououi_user.o(i.MainPage_CallBack) refers to strcmp.o(.text) for strcmp
    wououi_user.o(i.MainPage_CallBack) refers to wououi_win.o(i.WouoUI_MsgWinPageSetContent) for WouoUI_MsgWinPageSetContent
    wououi_user.o(i.MainPage_CallBack) refers to wououi_win.o(i.WouoUI_SpinWinPageSetMinMaxDecimalnum) for WouoUI_SpinWinPageSetMinMaxDecimalnum
    wououi_user.o(i.MainPage_CallBack) refers to wououi.o(i.WouoUI_JumpToPage) for WouoUI_JumpToPage
    wououi_user.o(i.MainPage_CallBack) refers to wououi_user.o(.bss) for .bss
    wououi_user.o(i.SettingPage_CallBack) refers to wououi_page.o(i.WouoUI_ListTitlePageGetSelectOpt) for WouoUI_ListTitlePageGetSelectOpt
    wououi_user.o(i.SettingPage_CallBack) refers to wououi_win.o(i.WouoUI_MsgWinPageSetContent) for WouoUI_MsgWinPageSetContent
    wououi_user.o(i.SettingPage_CallBack) refers to wououi_win.o(i.WouoUI_ValWinPageSetMinStepMax) for WouoUI_ValWinPageSetMinStepMax
    wououi_user.o(i.SettingPage_CallBack) refers to wououi_win.o(i.WouoUI_SpinWinPageSetMinMaxDecimalnum) for WouoUI_SpinWinPageSetMinMaxDecimalnum
    wououi_user.o(i.SettingPage_CallBack) refers to wououi.o(i.WouoUI_JumpToPage) for WouoUI_JumpToPage
    wououi_user.o(i.SettingPage_CallBack) refers to wououi.o(.data) for g_default_ui_para
    wououi_user.o(i.SettingPage_CallBack) refers to wououi_user.o(.bss) for .bss
    wououi_user.o(i.TestUI_Init) refers to wououi.o(i.WouoUI_SelectDefaultUI) for WouoUI_SelectDefaultUI
    wououi_user.o(i.TestUI_Init) refers to wououi_graph.o(i.WouoUI_BuffClear) for WouoUI_BuffClear
    wououi_user.o(i.TestUI_Init) refers to wououi_graph.o(i.WouoUI_BuffSend) for WouoUI_BuffSend
    wououi_user.o(i.TestUI_Init) refers to wououi_graph.o(i.WouoUI_GraphSetPenColor) for WouoUI_GraphSetPenColor
    wououi_user.o(i.TestUI_Init) refers to wououi_page.o(i.WouoUI_TitlePageInit) for WouoUI_TitlePageInit
    wououi_user.o(i.TestUI_Init) refers to wououi_page.o(i.WouoUI_ListPageInit) for WouoUI_ListPageInit
    wououi_user.o(i.TestUI_Init) refers to wououi_page.o(i.WouoUI_WavePageInit) for WouoUI_WavePageInit
    wououi_user.o(i.TestUI_Init) refers to wououi_win.o(i.WouoUI_MsgWinPageInit) for WouoUI_MsgWinPageInit
    wououi_user.o(i.TestUI_Init) refers to wououi_win.o(i.WouoUI_ConfWinPageInit) for WouoUI_ConfWinPageInit
    wououi_user.o(i.TestUI_Init) refers to wououi_win.o(i.WouoUI_ValWinPageInit) for WouoUI_ValWinPageInit
    wououi_user.o(i.TestUI_Init) refers to wououi_win.o(i.WouoUI_SpinWinPageInit) for WouoUI_SpinWinPageInit
    wououi_user.o(i.TestUI_Init) refers to wououi_win.o(i.WouoUI_ListWinPageInit) for WouoUI_ListWinPageInit
    wououi_user.o(i.TestUI_Init) refers to wououi_page.o(i.WouoUI_SetPageAutoDealWithMsg) for WouoUI_SetPageAutoDealWithMsg
    wououi_user.o(i.TestUI_Init) refers to wououi.o(.data) for g_default_ui_para
    wououi_user.o(i.TestUI_Init) refers to wououi_user.o(.data) for .data
    wououi_user.o(i.TestUI_Init) refers to wououi_user.o(i.MainPage_CallBack) for MainPage_CallBack
    wououi_user.o(i.TestUI_Init) refers to wououi_user.o(.constdata) for .constdata
    wououi_user.o(i.TestUI_Init) refers to wououi_user.o(.bss) for .bss
    wououi_user.o(i.TestUI_Init) refers to wououi_user.o(i.SettingPage_CallBack) for SettingPage_CallBack
    wououi_user.o(i.TestUI_Init) refers to wououi_user.o(i.About_CallBack) for About_CallBack
    wououi_user.o(i.TestUI_Init) refers to wououi_user.o(i.WavePage_CallBack) for WavePage_CallBack
    wououi_user.o(i.TestUI_Init) refers to wououi_user.o(i.CommonConfPage_CallBack) for CommonConfPage_CallBack
    wououi_user.o(i.TestUI_Init) refers to wououi_user.o(i.CommonValPage_CallBack) for CommonValPage_CallBack
    wououi_user.o(i.TestUI_Init) refers to wououi_user.o(i.BgBlurSelPage_CallBack) for BgBlurSelPage_CallBack
    wououi_user.o(i.TestUI_Init) refers to wououi_user.o(i.VolumePage_CallBack) for VolumePage_CallBack
    wououi_user.o(i.TestUI_Init) refers to wououi_user.o(i.VolumnConf_CallBack) for VolumnConf_CallBack
    wououi_user.o(i.VolumePage_CallBack) refers to wououi.o(i.WouoUI_JumpToPage) for WouoUI_JumpToPage
    wououi_user.o(i.VolumePage_CallBack) refers to wououi_user.o(.bss) for .bss
    wououi_user.o(i.VolumnConf_CallBack) refers to wououi_user.o(.bss) for .bss
    wououi_user.o(i.WavePage_CallBack) refers to wououi_page.o(i.WouoUI_WavePageCanShiftWave) for WouoUI_WavePageCanShiftWave
    wououi_user.o(i.WavePage_CallBack) refers to wououi_win.o(i.WouoUI_MsgWinPageSetContent) for WouoUI_MsgWinPageSetContent
    wououi_user.o(i.WavePage_CallBack) refers to wououi.o(i.WouoUI_JumpToPage) for WouoUI_JumpToPage
    wououi_user.o(i.WavePage_CallBack) refers to wououi_user.o(.bss) for .bss
    wououi_user.o(.data) refers to wououi_user.o(.conststring) for .conststring
    wououi_win.o(i.WouoUI_ConfWinPageIn) refers to wououi_anim.o(i.WouoUI_Animation) for WouoUI_Animation
    wououi_win.o(i.WouoUI_ConfWinPageIn) refers to wououi_graph.o(i.WouoUI_GraphSetPenColor) for WouoUI_GraphSetPenColor
    wououi_win.o(i.WouoUI_ConfWinPageIn) refers to wououi_graph.o(i.WouoUI_CanvasDrawRBox) for WouoUI_CanvasDrawRBox
    wououi_win.o(i.WouoUI_ConfWinPageIn) refers to wououi_win.o(i._WouoUI_ConfWinPageDraw) for _WouoUI_ConfWinPageDraw
    wououi_win.o(i.WouoUI_ConfWinPageIn) refers to wououi.o(.data) for p_cur_ui
    wououi_win.o(i.WouoUI_ConfWinPageIn) refers to wououi_font.o(.constdata) for Font_6_8
    wououi_win.o(i.WouoUI_ConfWinPageInParaInit) refers to wououi_win.o(i._WouoUI_WinGetBGSelectItem) for _WouoUI_WinGetBGSelectItem
    wououi_win.o(i.WouoUI_ConfWinPageInParaInit) refers to wououi_graph.o(i.WouoUI_GetStrHeightAutoNewLine) for WouoUI_GetStrHeightAutoNewLine
    wououi_win.o(i.WouoUI_ConfWinPageInParaInit) refers to wououi_graph.o(i.WouoUI_GetStrWidth) for WouoUI_GetStrWidth
    wououi_win.o(i.WouoUI_ConfWinPageInParaInit) refers to wououi_graph.o(i.WouoUI_CanvasSlideStrReset) for WouoUI_CanvasSlideStrReset
    wououi_win.o(i.WouoUI_ConfWinPageInParaInit) refers to wououi_font.o(.constdata) for Font_6_8
    wououi_win.o(i.WouoUI_ConfWinPageInParaInit) refers to wououi.o(.data) for p_cur_ui
    wououi_win.o(i.WouoUI_ConfWinPageIndicatorCtrl) refers to wououi_graph.o(i.WouoUI_GraphSetPenColor) for WouoUI_GraphSetPenColor
    wououi_win.o(i.WouoUI_ConfWinPageIndicatorCtrl) refers to wououi_graph.o(i.WouoUI_CanvasDrawRBox) for WouoUI_CanvasDrawRBox
    wououi_win.o(i.WouoUI_ConfWinPageIndicatorCtrl) refers to wououi.o(.data) for p_cur_ui
    wououi_win.o(i.WouoUI_ConfWinPageInit) refers to wououi_page.o(i.WouoUI_PageInit) for WouoUI_PageInit
    wououi_win.o(i.WouoUI_ConfWinPageInit) refers to wououi.o(.data) for p_cur_ui
    wououi_win.o(i.WouoUI_ConfWinPageReact) refers to wououi_msg.o(i.WouoUI_MsgQueRead) for WouoUI_MsgQueRead
    wououi_win.o(i.WouoUI_ConfWinPageReact) refers to wououi_win.o(i.WouoUI_ConfWinPageSlideUpTxt) for WouoUI_ConfWinPageSlideUpTxt
    wououi_win.o(i.WouoUI_ConfWinPageReact) refers to wououi_win.o(i.WouoUI_ConfWinPageSlideDownTxt) for WouoUI_ConfWinPageSlideDownTxt
    wououi_win.o(i.WouoUI_ConfWinPageReact) refers to wououi_win.o(i.WouoUI_ConfWinPageToggleBtn) for WouoUI_ConfWinPageToggleBtn
    wououi_win.o(i.WouoUI_ConfWinPageReact) refers to wououi_win.o(i._WouoUI_WinGetBGSelectItem) for _WouoUI_WinGetBGSelectItem
    wououi_win.o(i.WouoUI_ConfWinPageReact) refers to wououi_page.o(i.WouoUI_PageReturn) for WouoUI_PageReturn
    wououi_win.o(i.WouoUI_ConfWinPageReact) refers to wououi.o(.data) for p_cur_ui
    wououi_win.o(i.WouoUI_ConfWinPageShow) refers to wououi_graph.o(i.WouoUI_GraphSetPenColor) for WouoUI_GraphSetPenColor
    wououi_win.o(i.WouoUI_ConfWinPageShow) refers to wououi_graph.o(i.WouoUI_BuffAllBlur) for WouoUI_BuffAllBlur
    wououi_win.o(i.WouoUI_ConfWinPageShow) refers to wououi_graph.o(i.WouoUI_CanvasDrawRBox) for WouoUI_CanvasDrawRBox
    wououi_win.o(i.WouoUI_ConfWinPageShow) refers to wououi_win.o(i._WouoUI_ConfWinPageDraw) for _WouoUI_ConfWinPageDraw
    wououi_win.o(i.WouoUI_ConfWinPageShow) refers to wououi.o(.data) for p_cur_ui
    wououi_win.o(i.WouoUI_ConfWinPageShow) refers to wououi_font.o(.constdata) for Font_6_8
    wououi_win.o(i.WouoUI_ConfWinPageSlideUpTxt) refers to wououi.o(.data) for p_cur_ui
    wououi_win.o(i.WouoUI_ConfWinPageSlideUpTxt) refers to wououi_font.o(.constdata) for Font_6_8
    wououi_win.o(i.WouoUI_ConfWinPageToggleBtn) refers to wououi_graph.o(i.WouoUI_CanvasSlideStrReset) for WouoUI_CanvasSlideStrReset
    wououi_win.o(i.WouoUI_ConfWinPageToggleBtn) refers to wououi_graph.o(i.WouoUI_GetStrWidth) for WouoUI_GetStrWidth
    wououi_win.o(i.WouoUI_ConfWinPageToggleBtn) refers to wououi.o(.data) for p_cur_ui
    wououi_win.o(i.WouoUI_ConfWinPageToggleBtn) refers to wououi_font.o(.constdata) for Font_6_8
    wououi_win.o(i.WouoUI_ListWinPageIn) refers to wououi_anim.o(i.WouoUI_Animation) for WouoUI_Animation
    wououi_win.o(i.WouoUI_ListWinPageIn) refers to wououi_graph.o(i.WouoUI_GraphSetPenColor) for WouoUI_GraphSetPenColor
    wououi_win.o(i.WouoUI_ListWinPageIn) refers to wououi_graph.o(i.WouoUI_CanvasDrawRBox) for WouoUI_CanvasDrawRBox
    wououi_win.o(i.WouoUI_ListWinPageIn) refers to wououi_win.o(i._WouoUI_listWinPageDraw) for _WouoUI_listWinPageDraw
    wououi_win.o(i.WouoUI_ListWinPageIn) refers to wououi.o(.data) for p_cur_ui
    wououi_win.o(i.WouoUI_ListWinPageInParaInit) refers to wououi_win.o(i._WouoUI_WinGetBGSelectItem) for _WouoUI_WinGetBGSelectItem
    wououi_win.o(i.WouoUI_ListWinPageInParaInit) refers to wououi_graph.o(i.WouoUI_CanvasSlideStrReset) for WouoUI_CanvasSlideStrReset
    wououi_win.o(i.WouoUI_ListWinPageInParaInit) refers to wououi.o(.data) for p_cur_ui
    wououi_win.o(i.WouoUI_ListWinPageInParaInit) refers to wououi_font.o(.constdata) for Font_6_8
    wououi_win.o(i.WouoUI_ListWinPageIndicatorCtrl) refers to wououi_graph.o(i.WouoUI_GetStrWidth) for WouoUI_GetStrWidth
    wououi_win.o(i.WouoUI_ListWinPageIndicatorCtrl) refers to wououi_graph.o(i.WouoUI_GraphSetPenColor) for WouoUI_GraphSetPenColor
    wououi_win.o(i.WouoUI_ListWinPageIndicatorCtrl) refers to wououi_graph.o(i.WouoUI_CanvasDrawRBox) for WouoUI_CanvasDrawRBox
    wououi_win.o(i.WouoUI_ListWinPageIndicatorCtrl) refers to wououi.o(.data) for p_cur_ui
    wououi_win.o(i.WouoUI_ListWinPageIndicatorCtrl) refers to wououi_font.o(.constdata) for Font_6_8
    wououi_win.o(i.WouoUI_ListWinPageInit) refers to wououi_page.o(i.WouoUI_PageInit) for WouoUI_PageInit
    wououi_win.o(i.WouoUI_ListWinPageInit) refers to printfa.o(i.__0printf) for __2printf
    wououi_win.o(i.WouoUI_ListWinPageInit) refers to wououi.o(.data) for p_cur_ui
    wououi_win.o(i.WouoUI_ListWinPageInit) refers to wououi_win.o(.conststring) for .conststring
    wououi_win.o(i.WouoUI_ListWinPageLastItem) refers to wououi_font.o(.constdata) for Font_6_8
    wououi_win.o(i.WouoUI_ListWinPageLastItem) refers to wououi.o(.data) for p_cur_ui
    wououi_win.o(i.WouoUI_ListWinPageNextItem) refers to wououi_font.o(.constdata) for Font_6_8
    wououi_win.o(i.WouoUI_ListWinPageNextItem) refers to wououi.o(.data) for p_cur_ui
    wououi_win.o(i.WouoUI_ListWinPageReact) refers to wououi_msg.o(i.WouoUI_MsgQueRead) for WouoUI_MsgQueRead
    wououi_win.o(i.WouoUI_ListWinPageReact) refers to wououi_graph.o(i.WouoUI_CanvasSlideStrReset) for WouoUI_CanvasSlideStrReset
    wououi_win.o(i.WouoUI_ListWinPageReact) refers to wououi_win.o(i.WouoUI_ListWinPageLastItem) for WouoUI_ListWinPageLastItem
    wououi_win.o(i.WouoUI_ListWinPageReact) refers to wououi_win.o(i.WouoUI_ListWinPageNextItem) for WouoUI_ListWinPageNextItem
    wououi_win.o(i.WouoUI_ListWinPageReact) refers to wououi_win.o(i._WouoUI_WinGetBGSelectItem) for _WouoUI_WinGetBGSelectItem
    wououi_win.o(i.WouoUI_ListWinPageReact) refers to wououi_page.o(i.WouoUI_PageReturn) for WouoUI_PageReturn
    wououi_win.o(i.WouoUI_ListWinPageReact) refers to wououi.o(.data) for p_cur_ui
    wououi_win.o(i.WouoUI_ListWinPageScrollBarCtrl) refers to wououi_graph.o(i.WouoUI_CanvasDrawLine_H) for WouoUI_CanvasDrawLine_H
    wououi_win.o(i.WouoUI_ListWinPageScrollBarCtrl) refers to wououi_graph.o(i.WouoUI_CanvasDrawLine_V) for WouoUI_CanvasDrawLine_V
    wououi_win.o(i.WouoUI_ListWinPageScrollBarCtrl) refers to wououi_graph.o(i.WouoUI_CanvasDrawRBox) for WouoUI_CanvasDrawRBox
    wououi_win.o(i.WouoUI_ListWinPageScrollBarCtrl) refers to wououi.o(.data) for p_cur_ui
    wououi_win.o(i.WouoUI_ListWinPageShow) refers to wououi_graph.o(i.WouoUI_GraphSetPenColor) for WouoUI_GraphSetPenColor
    wououi_win.o(i.WouoUI_ListWinPageShow) refers to wououi_graph.o(i.WouoUI_BuffAllBlur) for WouoUI_BuffAllBlur
    wououi_win.o(i.WouoUI_ListWinPageShow) refers to wououi_graph.o(i.WouoUI_CanvasDrawRBox) for WouoUI_CanvasDrawRBox
    wououi_win.o(i.WouoUI_ListWinPageShow) refers to wououi_anim.o(i.WouoUI_Animation) for WouoUI_Animation
    wououi_win.o(i.WouoUI_ListWinPageShow) refers to wououi_win.o(i._WouoUI_listWinPageDraw) for _WouoUI_listWinPageDraw
    wououi_win.o(i.WouoUI_ListWinPageShow) refers to wououi.o(.data) for p_cur_ui
    wououi_win.o(i.WouoUI_MsgWinPageIn) refers to wououi_graph.o(i.WouoUI_GraphSetPenColor) for WouoUI_GraphSetPenColor
    wououi_win.o(i.WouoUI_MsgWinPageIn) refers to wououi_graph.o(i.WouoUI_CanvasDrawRBox) for WouoUI_CanvasDrawRBox
    wououi_win.o(i.WouoUI_MsgWinPageIn) refers to wououi_graph.o(i.WouoUI_CanvasDrawStrAutoNewline) for WouoUI_CanvasDrawStrAutoNewline
    wououi_win.o(i.WouoUI_MsgWinPageIn) refers to wououi.o(.data) for p_cur_ui
    wououi_win.o(i.WouoUI_MsgWinPageIn) refers to wououi_font.o(.constdata) for Font_6_8
    wououi_win.o(i.WouoUI_MsgWinPageInParaInit) refers to wououi_win.o(i._WouoUI_WinGetBGSelectItem) for _WouoUI_WinGetBGSelectItem
    wououi_win.o(i.WouoUI_MsgWinPageInParaInit) refers to wououi_graph.o(i.WouoUI_GetStrHeightAutoNewLine) for WouoUI_GetStrHeightAutoNewLine
    wououi_win.o(i.WouoUI_MsgWinPageInParaInit) refers to wououi_font.o(.constdata) for Font_6_8
    wououi_win.o(i.WouoUI_MsgWinPageInParaInit) refers to wououi.o(.data) for p_cur_ui
    wououi_win.o(i.WouoUI_MsgWinPageIndicatorCtrl) refers to wououi_graph.o(i.WouoUI_CanvasDrawRBoxEmpty) for WouoUI_CanvasDrawRBoxEmpty
    wououi_win.o(i.WouoUI_MsgWinPageIndicatorCtrl) refers to wououi.o(.data) for p_cur_ui
    wououi_win.o(i.WouoUI_MsgWinPageIndicatorCtrl) refers to wououi_font.o(.constdata) for Font_6_8
    wououi_win.o(i.WouoUI_MsgWinPageInit) refers to wououi_page.o(i.WouoUI_PageInit) for WouoUI_PageInit
    wououi_win.o(i.WouoUI_MsgWinPageInit) refers to wououi.o(.data) for p_cur_ui
    wououi_win.o(i.WouoUI_MsgWinPageReact) refers to wououi_msg.o(i.WouoUI_MsgQueRead) for WouoUI_MsgQueRead
    wououi_win.o(i.WouoUI_MsgWinPageReact) refers to wououi_win.o(i.WouoUI_MsgWinPageSlideUpTxt) for WouoUI_MsgWinPageSlideUpTxt
    wououi_win.o(i.WouoUI_MsgWinPageReact) refers to wououi_win.o(i.WouoUI_MsgWinPageSlideDownTxt) for WouoUI_MsgWinPageSlideDownTxt
    wououi_win.o(i.WouoUI_MsgWinPageReact) refers to wououi_page.o(i.WouoUI_PageReturn) for WouoUI_PageReturn
    wououi_win.o(i.WouoUI_MsgWinPageReact) refers to wououi.o(.data) for p_cur_ui
    wououi_win.o(i.WouoUI_MsgWinPageShow) refers to wououi_graph.o(i.WouoUI_GraphSetPenColor) for WouoUI_GraphSetPenColor
    wououi_win.o(i.WouoUI_MsgWinPageShow) refers to wououi_graph.o(i.WouoUI_BuffAllBlur) for WouoUI_BuffAllBlur
    wououi_win.o(i.WouoUI_MsgWinPageShow) refers to wououi_graph.o(i.WouoUI_CanvasDrawRBox) for WouoUI_CanvasDrawRBox
    wououi_win.o(i.WouoUI_MsgWinPageShow) refers to wououi_graph.o(i.WouoUI_CanvasDrawStrAutoNewline) for WouoUI_CanvasDrawStrAutoNewline
    wououi_win.o(i.WouoUI_MsgWinPageShow) refers to wououi.o(.data) for p_cur_ui
    wououi_win.o(i.WouoUI_MsgWinPageShow) refers to wououi_font.o(.constdata) for Font_6_8
    wououi_win.o(i.WouoUI_MsgWinPageSlideUpTxt) refers to wououi.o(.data) for p_cur_ui
    wououi_win.o(i.WouoUI_SpinWinPageIn) refers to wououi_anim.o(i.WouoUI_Animation) for WouoUI_Animation
    wououi_win.o(i.WouoUI_SpinWinPageIn) refers to wououi_graph.o(i.WouoUI_GraphSetPenColor) for WouoUI_GraphSetPenColor
    wououi_win.o(i.WouoUI_SpinWinPageIn) refers to wououi_graph.o(i.WouoUI_CanvasDrawRBox) for WouoUI_CanvasDrawRBox
    wououi_win.o(i.WouoUI_SpinWinPageIn) refers to wououi_win.o(i._WouoUI_SpinWinPageDraw) for _WouoUI_SpinWinPageDraw
    wououi_win.o(i.WouoUI_SpinWinPageIn) refers to wououi.o(.data) for p_cur_ui
    wououi_win.o(i.WouoUI_SpinWinPageIn) refers to wououi_font.o(.constdata) for Font_7_12
    wououi_win.o(i.WouoUI_SpinWinPageInParaInit) refers to wououi_win.o(i._WouoUI_WinGetBGSelectItem) for _WouoUI_WinGetBGSelectItem
    wououi_win.o(i.WouoUI_SpinWinPageInParaInit) refers to printfa.o(i.__0printf) for __2printf
    wououi_win.o(i.WouoUI_SpinWinPageInParaInit) refers to wououi_graph.o(i.WouoUI_CanvasSlideStrReset) for WouoUI_CanvasSlideStrReset
    wououi_win.o(i.WouoUI_SpinWinPageInParaInit) refers to wououi_font.o(.constdata) for Font_7_12
    wououi_win.o(i.WouoUI_SpinWinPageInParaInit) refers to wououi.o(.data) for p_cur_ui
    wououi_win.o(i.WouoUI_SpinWinPageIndicatorCtrl) refers to wououi_graph.o(i.WouoUI_GraphSetPenColor) for WouoUI_GraphSetPenColor
    wououi_win.o(i.WouoUI_SpinWinPageIndicatorCtrl) refers to wououi_graph.o(i.WouoUI_CanvasDrawRBox) for WouoUI_CanvasDrawRBox
    wououi_win.o(i.WouoUI_SpinWinPageIndicatorCtrl) refers to wououi_font.o(.constdata) for Font_7_12
    wououi_win.o(i.WouoUI_SpinWinPageIndicatorCtrl) refers to wououi.o(.data) for p_cur_ui
    wououi_win.o(i.WouoUI_SpinWinPageInit) refers to printfa.o(i.__0printf) for __2printf
    wououi_win.o(i.WouoUI_SpinWinPageInit) refers to wououi_page.o(i.WouoUI_PageInit) for WouoUI_PageInit
    wououi_win.o(i.WouoUI_SpinWinPageInit) refers to wououi_win.o(i.WouoUI_SpinWinPageSetMinMaxDecimalnum) for WouoUI_SpinWinPageSetMinMaxDecimalnum
    wououi_win.o(i.WouoUI_SpinWinPageInit) refers to wououi_font.o(.constdata) for Font_7_12
    wououi_win.o(i.WouoUI_SpinWinPageInit) refers to wououi_win.o(.conststring) for .conststring
    wououi_win.o(i.WouoUI_SpinWinPageInit) refers to wououi.o(.data) for p_cur_ui
    wououi_win.o(i.WouoUI_SpinWinPageReact) refers to wououi_msg.o(i.WouoUI_MsgQueRead) for WouoUI_MsgQueRead
    wououi_win.o(i.WouoUI_SpinWinPageReact) refers to wououi_win.o(i.WouoUI_SpinWinPageChangeSelbit) for WouoUI_SpinWinPageChangeSelbit
    wououi_win.o(i.WouoUI_SpinWinPageReact) refers to wououi_win.o(i.WouoUI_SpinWinPageShiftSelbit) for WouoUI_SpinWinPageShiftSelbit
    wououi_win.o(i.WouoUI_SpinWinPageReact) refers to wououi_win.o(i.WouoUI_SpinWinPageToggleSelState) for WouoUI_SpinWinPageToggleSelState
    wououi_win.o(i.WouoUI_SpinWinPageReact) refers to wououi_win.o(i._WouoUI_WinGetBGSelectItem) for _WouoUI_WinGetBGSelectItem
    wououi_win.o(i.WouoUI_SpinWinPageReact) refers to wououi_page.o(i.WouoUI_PageReturn) for WouoUI_PageReturn
    wououi_win.o(i.WouoUI_SpinWinPageReact) refers to wououi.o(.data) for p_cur_ui
    wououi_win.o(i.WouoUI_SpinWinPageReact) refers to wououi_font.o(.constdata) for Font_7_12
    wououi_win.o(i.WouoUI_SpinWinPageSetMinMaxDecimalnum) refers to printfa.o(i.__0printf) for __2printf
    wououi_win.o(i.WouoUI_SpinWinPageSetMinMaxDecimalnum) refers to wououi_win.o(.conststring) for .conststring
    wououi_win.o(i.WouoUI_SpinWinPageShow) refers to wououi_graph.o(i.WouoUI_GraphSetPenColor) for WouoUI_GraphSetPenColor
    wououi_win.o(i.WouoUI_SpinWinPageShow) refers to wououi_graph.o(i.WouoUI_BuffAllBlur) for WouoUI_BuffAllBlur
    wououi_win.o(i.WouoUI_SpinWinPageShow) refers to wououi_graph.o(i.WouoUI_CanvasDrawRBox) for WouoUI_CanvasDrawRBox
    wououi_win.o(i.WouoUI_SpinWinPageShow) refers to wououi_win.o(i._WouoUI_SpinWinPageDraw) for _WouoUI_SpinWinPageDraw
    wououi_win.o(i.WouoUI_SpinWinPageShow) refers to wououi.o(.data) for p_cur_ui
    wououi_win.o(i.WouoUI_SpinWinPageShow) refers to wououi_font.o(.constdata) for Font_7_12
    wououi_win.o(i.WouoUI_ValWinPageIn) refers to wououi_anim.o(i.WouoUI_Animation) for WouoUI_Animation
    wououi_win.o(i.WouoUI_ValWinPageIn) refers to wououi_graph.o(i.WouoUI_GraphSetPenColor) for WouoUI_GraphSetPenColor
    wououi_win.o(i.WouoUI_ValWinPageIn) refers to wououi_graph.o(i.WouoUI_CanvasDrawRBox) for WouoUI_CanvasDrawRBox
    wououi_win.o(i.WouoUI_ValWinPageIn) refers to wououi_win.o(i._WouoUI_ValWinPageDraw) for _WouoUI_ValWinPageDraw
    wououi_win.o(i.WouoUI_ValWinPageIn) refers to wououi.o(.data) for p_cur_ui
    wououi_win.o(i.WouoUI_ValWinPageIn) refers to wououi_font.o(.constdata) for Font_6_8
    wououi_win.o(i.WouoUI_ValWinPageInParaInit) refers to wououi_win.o(i._WouoUI_WinGetBGSelectItem) for _WouoUI_WinGetBGSelectItem
    wououi_win.o(i.WouoUI_ValWinPageInParaInit) refers to printfa.o(i.__0printf) for __2printf
    wououi_win.o(i.WouoUI_ValWinPageInParaInit) refers to wououi_graph.o(i.WouoUI_CanvasSlideStrReset) for WouoUI_CanvasSlideStrReset
    wououi_win.o(i.WouoUI_ValWinPageInParaInit) refers to wououi_font.o(.constdata) for Font_6_8
    wououi_win.o(i.WouoUI_ValWinPageInParaInit) refers to wououi.o(.data) for p_cur_ui
    wououi_win.o(i.WouoUI_ValWinPageIndicatorCtrl) refers to wououi_graph.o(i.WouoUI_GraphSetPenColor) for WouoUI_GraphSetPenColor
    wououi_win.o(i.WouoUI_ValWinPageIndicatorCtrl) refers to wououi_graph.o(i.WouoUI_CanvasDrawRBox) for WouoUI_CanvasDrawRBox
    wououi_win.o(i.WouoUI_ValWinPageIndicatorCtrl) refers to wououi.o(.data) for p_cur_ui
    wououi_win.o(i.WouoUI_ValWinPageIndicatorCtrl) refers to wououi_font.o(.constdata) for Font_6_8
    wououi_win.o(i.WouoUI_ValWinPageInit) refers to printfa.o(i.__0printf) for __2printf
    wououi_win.o(i.WouoUI_ValWinPageInit) refers to wououi_page.o(i.WouoUI_PageInit) for WouoUI_PageInit
    wououi_win.o(i.WouoUI_ValWinPageInit) refers to wououi_win.o(i.WouoUI_ValWinPageSetMinStepMax) for WouoUI_ValWinPageSetMinStepMax
    wououi_win.o(i.WouoUI_ValWinPageInit) refers to wououi_font.o(.constdata) for Font_6_8
    wououi_win.o(i.WouoUI_ValWinPageInit) refers to wououi_win.o(.conststring) for .conststring
    wououi_win.o(i.WouoUI_ValWinPageInit) refers to wououi.o(.data) for p_cur_ui
    wououi_win.o(i.WouoUI_ValWinPageReact) refers to wououi_msg.o(i.WouoUI_MsgQueRead) for WouoUI_MsgQueRead
    wououi_win.o(i.WouoUI_ValWinPageReact) refers to wououi_win.o(i.WouoUI_ValWinPageValIncrease) for WouoUI_ValWinPageValIncrease
    wououi_win.o(i.WouoUI_ValWinPageReact) refers to wououi_win.o(i.WouoUI_ValWinPageValDecrease) for WouoUI_ValWinPageValDecrease
    wououi_win.o(i.WouoUI_ValWinPageReact) refers to wououi_win.o(i._WouoUI_WinGetBGSelectItem) for _WouoUI_WinGetBGSelectItem
    wououi_win.o(i.WouoUI_ValWinPageReact) refers to wououi_page.o(i.WouoUI_PageReturn) for WouoUI_PageReturn
    wououi_win.o(i.WouoUI_ValWinPageReact) refers to wououi.o(.data) for p_cur_ui
    wououi_win.o(i.WouoUI_ValWinPageReact) refers to wououi_font.o(.constdata) for Font_6_8
    wououi_win.o(i.WouoUI_ValWinPageSetMinStepMax) refers to printfa.o(i.__0printf) for __2printf
    wououi_win.o(i.WouoUI_ValWinPageSetMinStepMax) refers to wououi_win.o(.conststring) for .conststring
    wououi_win.o(i.WouoUI_ValWinPageShow) refers to wououi_graph.o(i.WouoUI_GraphSetPenColor) for WouoUI_GraphSetPenColor
    wououi_win.o(i.WouoUI_ValWinPageShow) refers to wououi_graph.o(i.WouoUI_BuffAllBlur) for WouoUI_BuffAllBlur
    wououi_win.o(i.WouoUI_ValWinPageShow) refers to wououi_graph.o(i.WouoUI_CanvasDrawRBox) for WouoUI_CanvasDrawRBox
    wououi_win.o(i.WouoUI_ValWinPageShow) refers to wououi_win.o(i._WouoUI_ValWinPageDraw) for _WouoUI_ValWinPageDraw
    wououi_win.o(i.WouoUI_ValWinPageShow) refers to wououi.o(.data) for p_cur_ui
    wououi_win.o(i.WouoUI_ValWinPageShow) refers to wououi_font.o(.constdata) for Font_6_8
    wououi_win.o(i._WouoUI_ConfWinPageDraw) refers to wououi_graph.o(i.WouoUI_CanvasDrawRBoxEmpty) for WouoUI_CanvasDrawRBoxEmpty
    wououi_win.o(i._WouoUI_ConfWinPageDraw) refers to wououi_graph.o(i.WouoUI_CanvasDrawStrAutoNewline) for WouoUI_CanvasDrawStrAutoNewline
    wououi_win.o(i._WouoUI_ConfWinPageDraw) refers to wououi_graph.o(i.WouoUI_CanvasDrawSlideStr) for WouoUI_CanvasDrawSlideStr
    wououi_win.o(i._WouoUI_ConfWinPageDraw) refers to wououi_graph.o(i.WouoUI_GetStrWidth) for WouoUI_GetStrWidth
    wououi_win.o(i._WouoUI_ConfWinPageDraw) refers to wououi_graph.o(i.WouoUI_CanvasDrawStr) for WouoUI_CanvasDrawStr
    wououi_win.o(i._WouoUI_ConfWinPageDraw) refers to wououi.o(.data) for p_cur_ui
    wououi_win.o(i._WouoUI_ConfWinPageDraw) refers to wououi_font.o(.constdata) for Font_6_8
    wououi_win.o(i._WouoUI_SpinWinPageDraw) refers to wououi_graph.o(i.WouoUI_CanvasDrawRBoxEmpty) for WouoUI_CanvasDrawRBoxEmpty
    wououi_win.o(i._WouoUI_SpinWinPageDraw) refers to wououi_graph.o(i.WouoUI_GetStrWidth) for WouoUI_GetStrWidth
    wououi_win.o(i._WouoUI_SpinWinPageDraw) refers to wououi_graph.o(i.WouoUI_CanvasDrawSlideStr) for WouoUI_CanvasDrawSlideStr
    wououi_win.o(i._WouoUI_SpinWinPageDraw) refers to wououi.o(i.ui_ftoa_g_str) for ui_ftoa_g_str
    wououi_win.o(i._WouoUI_SpinWinPageDraw) refers to wououi_graph.o(i.WouoUI_CanvasDrawASCII) for WouoUI_CanvasDrawASCII
    wououi_win.o(i._WouoUI_SpinWinPageDraw) refers to printfa.o(i.__0sprintf) for __2sprintf
    wououi_win.o(i._WouoUI_SpinWinPageDraw) refers to wououi_font.o(.constdata) for Font_7_12
    wououi_win.o(i._WouoUI_SpinWinPageDraw) refers to wououi.o(.data) for p_cur_ui
    wououi_win.o(i._WouoUI_ValWinPageDraw) refers to wououi_graph.o(i.WouoUI_CanvasDrawRBoxEmpty) for WouoUI_CanvasDrawRBoxEmpty
    wououi_win.o(i._WouoUI_ValWinPageDraw) refers to wououi_graph.o(i.WouoUI_GetStrWidth) for WouoUI_GetStrWidth
    wououi_win.o(i._WouoUI_ValWinPageDraw) refers to wououi.o(i.ui_itoa_str) for ui_itoa_str
    wououi_win.o(i._WouoUI_ValWinPageDraw) refers to wououi_graph.o(i.WouoUI_CanvasDrawSlideStr) for WouoUI_CanvasDrawSlideStr
    wououi_win.o(i._WouoUI_ValWinPageDraw) refers to wououi_font.o(.constdata) for Font_6_8
    wououi_win.o(i._WouoUI_ValWinPageDraw) refers to wououi.o(.data) for p_cur_ui
    wououi_win.o(i._WouoUI_WinGetBGSelectItem) refers to printfa.o(i.__0printf) for __2printf
    wououi_win.o(i._WouoUI_listWinPageDraw) refers to wououi_graph.o(i.WouoUI_CanvasDrawRBoxEmpty) for WouoUI_CanvasDrawRBoxEmpty
    wououi_win.o(i._WouoUI_listWinPageDraw) refers to wououi_graph.o(i.WouoUI_CanvasDrawStr) for WouoUI_CanvasDrawStr
    wououi_win.o(i._WouoUI_listWinPageDraw) refers to wououi_graph.o(i.WouoUI_CanvasDrawSlideStr) for WouoUI_CanvasDrawSlideStr
    wououi_win.o(i._WouoUI_listWinPageDraw) refers to wououi.o(.data) for p_cur_ui
    wououi_win.o(i._WouoUI_listWinPageDraw) refers to wououi_font.o(.constdata) for Font_6_8
    scheduler.o(i.scheduler_init) refers to scheduler.o(.data) for .data
    scheduler.o(i.scheduler_run) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    scheduler.o(i.scheduler_run) refers to scheduler.o(.data) for .data
    scheduler.o(.data) refers to led_app.o(i.led_task) for led_task
    scheduler.o(.data) refers to adc_app.o(i.adc_task) for adc_task
    scheduler.o(.data) refers to btn_app.o(i.btn_task) for btn_task
    scheduler.o(.data) refers to usart_app.o(i.uart_task) for uart_task
    scheduler.o(.data) refers to oled_app.o(i.oled_task) for oled_task
    scheduler.o(.data) refers to sampling_control.o(i.sampling_task) for sampling_task
    usart_app.o(i.HAL_UARTEx_RxEventCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) for HAL_UART_DMAStop
    usart_app.o(i.HAL_UARTEx_RxEventCallback) refers to ringbuffer.o(i.rt_ringbuffer_put) for rt_ringbuffer_put
    usart_app.o(i.HAL_UARTEx_RxEventCallback) refers to memseta.o(.text) for __aeabi_memclr4
    usart_app.o(i.HAL_UARTEx_RxEventCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    usart_app.o(i.HAL_UARTEx_RxEventCallback) refers to usart_app.o(.bss) for .bss
    usart_app.o(i.HAL_UARTEx_RxEventCallback) refers to usart.o(.bss) for huart1
    usart_app.o(i.HAL_UART_RxCpltCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    usart_app.o(i.HAL_UART_RxCpltCallback) refers to stm32f4xx_hal.o(.data) for uwTick
    usart_app.o(i.HAL_UART_RxCpltCallback) refers to usart_app.o(.data) for .data
    usart_app.o(i.HAL_UART_RxCpltCallback) refers to usart_app.o(.bss) for .bss
    usart_app.o(i.HAL_UART_RxCpltCallback) refers to usart.o(.bss) for huart1
    usart_app.o(i.convert_rtc_to_unix_timestamp) refers to usart_app.o(.constdata) for .constdata
    usart_app.o(i.format_hex_output) refers to usart_app.o(i.convert_voltage_to_hex_format) for convert_voltage_to_hex_format
    usart_app.o(i.format_hex_output) refers to printfa.o(i.__0sprintf) for __2sprintf
    usart_app.o(i.handle_conf_command) refers to data_storage.o(i.data_storage_write_log) for data_storage_write_log
    usart_app.o(i.handle_conf_command) refers to data_storage_1.o(i.data_storage_write_log) for data_storage_write_log
    usart_app.o(i.handle_conf_command) refers to ini_parser.o(i.ini_parse_file) for ini_parse_file
    usart_app.o(i.handle_conf_command) refers to config_manager.o(i.config_validate_ratio) for config_validate_ratio
    usart_app.o(i.handle_conf_command) refers to config_manager.o(i.config_validate_limit) for config_validate_limit
    usart_app.o(i.handle_conf_command) refers to config_manager.o(i.config_get_params) for config_get_params
    usart_app.o(i.handle_conf_command) refers to config_manager.o(i.config_set_params) for config_set_params
    usart_app.o(i.handle_conf_command) refers to config_manager.o(i.config_save_to_flash) for config_save_to_flash
    usart_app.o(i.handle_conf_command) refers to usart_app.o(i.my_printf) for my_printf
    usart_app.o(i.handle_conf_command) refers to f2d.o(.text) for __aeabi_f2d
    usart_app.o(i.handle_conf_command) refers to printfa.o(i.__0sprintf) for __2sprintf
    usart_app.o(i.handle_conf_command) refers to usart.o(.bss) for huart1
    usart_app.o(i.handle_configread_command) refers to config_manager.o(i.config_load_from_flash) for config_load_from_flash
    usart_app.o(i.handle_configread_command) refers to usart_app.o(i.my_printf) for my_printf
    usart_app.o(i.handle_configread_command) refers to config_manager.o(i.config_get_params) for config_get_params
    usart_app.o(i.handle_configread_command) refers to f2d.o(.text) for __aeabi_f2d
    usart_app.o(i.handle_configread_command) refers to usart.o(.bss) for huart1
    usart_app.o(i.handle_configsave_command) refers to config_manager.o(i.config_get_params) for config_get_params
    usart_app.o(i.handle_configsave_command) refers to f2d.o(.text) for __aeabi_f2d
    usart_app.o(i.handle_configsave_command) refers to usart_app.o(i.my_printf) for my_printf
    usart_app.o(i.handle_configsave_command) refers to config_manager.o(i.config_save_to_flash) for config_save_to_flash
    usart_app.o(i.handle_configsave_command) refers to usart.o(.bss) for huart1
    usart_app.o(i.handle_hide_command) refers to data_storage.o(i.data_storage_write_log) for data_storage_write_log
    usart_app.o(i.handle_hide_command) refers to data_storage_1.o(i.data_storage_write_log) for data_storage_write_log
    usart_app.o(i.handle_hide_command) refers to usart_app.o(.data) for .data
    usart_app.o(i.handle_interactive_input) refers to scanf_fp.o(.text) for _scanf_real
    usart_app.o(i.handle_interactive_input) refers to __0sscanf.o(.text) for __0sscanf
    usart_app.o(i.handle_interactive_input) refers to config_manager.o(i.config_get_params) for config_get_params
    usart_app.o(i.handle_interactive_input) refers to usart_app.o(i.my_printf) for my_printf
    usart_app.o(i.handle_interactive_input) refers to rtc_app.o(i.rtc_set_time_from_string) for rtc_set_time_from_string
    usart_app.o(i.handle_interactive_input) refers to config_manager.o(i.config_validate_ratio) for config_validate_ratio
    usart_app.o(i.handle_interactive_input) refers to f2d.o(.text) for __aeabi_f2d
    usart_app.o(i.handle_interactive_input) refers to config_manager.o(i.config_set_params) for config_set_params
    usart_app.o(i.handle_interactive_input) refers to config_manager.o(i.config_validate_limit) for config_validate_limit
    usart_app.o(i.handle_interactive_input) refers to printfa.o(i.__0sprintf) for __2sprintf
    usart_app.o(i.handle_interactive_input) refers to rtc_app.o(i.rtc_print_current_time) for rtc_print_current_time
    usart_app.o(i.handle_interactive_input) refers to data_storage.o(i.data_storage_write_log) for data_storage_write_log
    usart_app.o(i.handle_interactive_input) refers to data_storage_1.o(i.data_storage_write_log) for data_storage_write_log
    usart_app.o(i.handle_interactive_input) refers to usart_app.o(.data) for .data
    usart_app.o(i.handle_interactive_input) refers to usart.o(.bss) for huart1
    usart_app.o(i.handle_interactive_input) refers to usart_app.o(.conststring) for .conststring
    usart_app.o(i.handle_limit_command) refers to data_storage.o(i.data_storage_write_log) for data_storage_write_log
    usart_app.o(i.handle_limit_command) refers to data_storage_1.o(i.data_storage_write_log) for data_storage_write_log
    usart_app.o(i.handle_limit_command) refers to config_manager.o(i.config_get_params) for config_get_params
    usart_app.o(i.handle_limit_command) refers to usart_app.o(i.my_printf) for my_printf
    usart_app.o(i.handle_limit_command) refers to f2d.o(.text) for __aeabi_f2d
    usart_app.o(i.handle_limit_command) refers to usart.o(.bss) for huart1
    usart_app.o(i.handle_limit_command) refers to usart_app.o(.data) for .data
    usart_app.o(i.handle_ratio_command) refers to data_storage.o(i.data_storage_write_log) for data_storage_write_log
    usart_app.o(i.handle_ratio_command) refers to data_storage_1.o(i.data_storage_write_log) for data_storage_write_log
    usart_app.o(i.handle_ratio_command) refers to config_manager.o(i.config_get_params) for config_get_params
    usart_app.o(i.handle_ratio_command) refers to usart_app.o(i.my_printf) for my_printf
    usart_app.o(i.handle_ratio_command) refers to f2d.o(.text) for __aeabi_f2d
    usart_app.o(i.handle_ratio_command) refers to usart.o(.bss) for huart1
    usart_app.o(i.handle_ratio_command) refers to usart_app.o(.data) for .data
    usart_app.o(i.handle_rtc_config_command) refers to data_storage.o(i.data_storage_write_log) for data_storage_write_log
    usart_app.o(i.handle_rtc_config_command) refers to data_storage_1.o(i.data_storage_write_log) for data_storage_write_log
    usart_app.o(i.handle_rtc_config_command) refers to usart_app.o(i.my_printf) for my_printf
    usart_app.o(i.handle_rtc_config_command) refers to usart.o(.bss) for huart1
    usart_app.o(i.handle_rtc_config_command) refers to usart_app.o(.data) for .data
    usart_app.o(i.handle_sampling_output) refers to sampling_control.o(i.sampling_get_state) for sampling_get_state
    usart_app.o(i.handle_sampling_output) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    usart_app.o(i.handle_sampling_output) refers to sampling_control.o(i.sampling_get_cycle) for sampling_get_cycle
    usart_app.o(i.handle_sampling_output) refers to memseta.o(.text) for __aeabi_memclr4
    usart_app.o(i.handle_sampling_output) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_GetTime) for HAL_RTC_GetTime
    usart_app.o(i.handle_sampling_output) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_GetDate) for HAL_RTC_GetDate
    usart_app.o(i.handle_sampling_output) refers to sampling_control.o(i.sampling_get_voltage) for sampling_get_voltage
    usart_app.o(i.handle_sampling_output) refers to sampling_control.o(i.sampling_check_overlimit) for sampling_check_overlimit
    usart_app.o(i.handle_sampling_output) refers to config_manager.o(i.config_get_params) for config_get_params
    usart_app.o(i.handle_sampling_output) refers to f2d.o(.text) for __aeabi_f2d
    usart_app.o(i.handle_sampling_output) refers to usart_app.o(i.my_printf) for my_printf
    usart_app.o(i.handle_sampling_output) refers to usart_app.o(i.convert_rtc_to_unix_timestamp) for convert_rtc_to_unix_timestamp
    usart_app.o(i.handle_sampling_output) refers to usart_app.o(i.format_hex_output) for format_hex_output
    usart_app.o(i.handle_sampling_output) refers to data_storage.o(i.data_storage_write_sample) for data_storage_write_sample
    usart_app.o(i.handle_sampling_output) refers to data_storage_1.o(i.data_storage_write_sample) for data_storage_write_sample
    usart_app.o(i.handle_sampling_output) refers to data_storage.o(i.data_storage_write_hidedata) for data_storage_write_hidedata
    usart_app.o(i.handle_sampling_output) refers to data_storage_1.o(i.data_storage_write_hidedata) for data_storage_write_hidedata
    usart_app.o(i.handle_sampling_output) refers to data_storage.o(i.data_storage_write_overlimit) for data_storage_write_overlimit
    usart_app.o(i.handle_sampling_output) refers to data_storage_1.o(i.data_storage_write_overlimit) for data_storage_write_overlimit
    usart_app.o(i.handle_sampling_output) refers to usart_app.o(.data) for .data
    usart_app.o(i.handle_sampling_output) refers to rtc.o(.bss) for hrtc
    usart_app.o(i.handle_sampling_output) refers to usart.o(.bss) for huart1
    usart_app.o(i.handle_start_command) refers to sampling_control.o(i.sampling_init) for sampling_init
    usart_app.o(i.handle_start_command) refers to sampling_control.o(i.sampling_start) for sampling_start
    usart_app.o(i.handle_start_command) refers to usart_app.o(i.my_printf) for my_printf
    usart_app.o(i.handle_start_command) refers to sampling_control.o(i.sampling_get_cycle) for sampling_get_cycle
    usart_app.o(i.handle_start_command) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    usart_app.o(i.handle_start_command) refers to printfa.o(i.__0sprintf) for __2sprintf
    usart_app.o(i.handle_start_command) refers to data_storage.o(i.data_storage_write_log) for data_storage_write_log
    usart_app.o(i.handle_start_command) refers to data_storage_1.o(i.data_storage_write_log) for data_storage_write_log
    usart_app.o(i.handle_start_command) refers to usart.o(.bss) for huart1
    usart_app.o(i.handle_start_command) refers to usart_app.o(.data) for .data
    usart_app.o(i.handle_stop_command) refers to sampling_control.o(i.sampling_init) for sampling_init
    usart_app.o(i.handle_stop_command) refers to sampling_control.o(i.sampling_stop) for sampling_stop
    usart_app.o(i.handle_stop_command) refers to usart_app.o(i.my_printf) for my_printf
    usart_app.o(i.handle_stop_command) refers to data_storage.o(i.data_storage_write_log) for data_storage_write_log
    usart_app.o(i.handle_stop_command) refers to data_storage_1.o(i.data_storage_write_log) for data_storage_write_log
    usart_app.o(i.handle_stop_command) refers to usart.o(.bss) for huart1
    usart_app.o(i.handle_stop_command) refers to usart_app.o(.data) for .data
    usart_app.o(i.handle_unhide_command) refers to data_storage.o(i.data_storage_write_log) for data_storage_write_log
    usart_app.o(i.handle_unhide_command) refers to data_storage_1.o(i.data_storage_write_log) for data_storage_write_log
    usart_app.o(i.handle_unhide_command) refers to usart_app.o(.data) for .data
    usart_app.o(i.my_printf) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    usart_app.o(i.my_printf) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    usart_app.o(i.parse_uart_command) refers to strcmp.o(.text) for strcmp
    usart_app.o(i.parse_uart_command) refers to strncmp.o(.text) for strncmp
    usart_app.o(i.parse_uart_command) refers to usart_app.o(i.handle_interactive_input) for handle_interactive_input
    usart_app.o(i.parse_uart_command) refers to system_check.o(i.system_self_check) for system_self_check
    usart_app.o(i.parse_uart_command) refers to usart_app.o(i.test_unix_timestamp_conversion) for test_unix_timestamp_conversion
    usart_app.o(i.parse_uart_command) refers to usart_app.o(i.test_unhide_conversion) for test_unhide_conversion
    usart_app.o(i.parse_uart_command) refers to usart_app.o(i.my_printf) for my_printf
    usart_app.o(i.parse_uart_command) refers to usart_app.o(i.test_cycle_persistence) for test_cycle_persistence
    usart_app.o(i.parse_uart_command) refers to usart_app.o(i.test_timing_accuracy) for test_timing_accuracy
    usart_app.o(i.parse_uart_command) refers to usart_app.o(i.test_storage_status) for test_storage_status
    usart_app.o(i.parse_uart_command) refers to usart_app.o(i.test_button_functions) for test_button_functions
    usart_app.o(i.parse_uart_command) refers to usart_app.o(i.handle_rtc_config_command) for handle_rtc_config_command
    usart_app.o(i.parse_uart_command) refers to rtc_app.o(i.rtc_print_current_time) for rtc_print_current_time
    usart_app.o(i.parse_uart_command) refers to usart_app.o(i.handle_conf_command) for handle_conf_command
    usart_app.o(i.parse_uart_command) refers to usart_app.o(i.handle_ratio_command) for handle_ratio_command
    usart_app.o(i.parse_uart_command) refers to usart_app.o(i.handle_limit_command) for handle_limit_command
    usart_app.o(i.parse_uart_command) refers to usart_app.o(i.handle_configsave_command) for handle_configsave_command
    usart_app.o(i.parse_uart_command) refers to usart_app.o(i.handle_configread_command) for handle_configread_command
    usart_app.o(i.parse_uart_command) refers to usart_app.o(i.handle_start_command) for handle_start_command
    usart_app.o(i.parse_uart_command) refers to usart_app.o(i.handle_stop_command) for handle_stop_command
    usart_app.o(i.parse_uart_command) refers to usart_app.o(i.handle_hide_command) for handle_hide_command
    usart_app.o(i.parse_uart_command) refers to usart_app.o(i.handle_unhide_command) for handle_unhide_command
    usart_app.o(i.parse_uart_command) refers to usart_app.o(.data) for .data
    usart_app.o(i.parse_uart_command) refers to usart.o(.bss) for huart1
    usart_app.o(i.test_button_functions) refers to usart_app.o(i.my_printf) for my_printf
    usart_app.o(i.test_button_functions) refers to sampling_control.o(i.sampling_get_cycle) for sampling_get_cycle
    usart_app.o(i.test_button_functions) refers to sampling_control.o(i.sampling_init) for sampling_init
    usart_app.o(i.test_button_functions) refers to sampling_control.o(i.sampling_set_cycle) for sampling_set_cycle
    usart_app.o(i.test_button_functions) refers to config_manager.o(i.config_get_sampling_cycle) for config_get_sampling_cycle
    usart_app.o(i.test_button_functions) refers to sampling_control.o(i.sampling_get_state) for sampling_get_state
    usart_app.o(i.test_button_functions) refers to usart.o(.bss) for huart1
    usart_app.o(i.test_button_functions) refers to usart_app.o(.conststring) for .conststring
    usart_app.o(i.test_cycle_persistence) refers to usart_app.o(i.my_printf) for my_printf
    usart_app.o(i.test_cycle_persistence) refers to sampling_control.o(i.sampling_get_cycle) for sampling_get_cycle
    usart_app.o(i.test_cycle_persistence) refers to config_manager.o(i.config_get_sampling_cycle) for config_get_sampling_cycle
    usart_app.o(i.test_cycle_persistence) refers to sampling_control.o(i.sampling_set_cycle) for sampling_set_cycle
    usart_app.o(i.test_cycle_persistence) refers to sampling_control.o(i.sampling_init) for sampling_init
    usart_app.o(i.test_cycle_persistence) refers to usart.o(.bss) for huart1
    usart_app.o(i.test_cycle_persistence) refers to usart_app.o(.constdata) for .constdata
    usart_app.o(i.test_storage_status) refers to usart_app.o(i.my_printf) for my_printf
    usart_app.o(i.test_storage_status) refers to data_storage.o(i.data_storage_init) for data_storage_init
    usart_app.o(i.test_storage_status) refers to data_storage_1.o(i.data_storage_init) for data_storage_init
    usart_app.o(i.test_storage_status) refers to sampling_control.o(i.sampling_get_voltage) for sampling_get_voltage
    usart_app.o(i.test_storage_status) refers to f2d.o(.text) for __aeabi_f2d
    usart_app.o(i.test_storage_status) refers to config_manager.o(i.config_get_params) for config_get_params
    usart_app.o(i.test_storage_status) refers to sampling_control.o(i.sampling_check_overlimit) for sampling_check_overlimit
    usart_app.o(i.test_storage_status) refers to data_storage.o(i.data_storage_write_overlimit) for data_storage_write_overlimit
    usart_app.o(i.test_storage_status) refers to data_storage_1.o(i.data_storage_write_overlimit) for data_storage_write_overlimit
    usart_app.o(i.test_storage_status) refers to data_storage.o(i.data_storage_write_hidedata) for data_storage_write_hidedata
    usart_app.o(i.test_storage_status) refers to data_storage_1.o(i.data_storage_write_hidedata) for data_storage_write_hidedata
    usart_app.o(i.test_storage_status) refers to usart.o(.bss) for huart1
    usart_app.o(i.test_storage_status) refers to usart_app.o(.data) for .data
    usart_app.o(i.test_timing_accuracy) refers to usart_app.o(i.my_printf) for my_printf
    usart_app.o(i.test_timing_accuracy) refers to sampling_control.o(i.sampling_get_cycle) for sampling_get_cycle
    usart_app.o(i.test_timing_accuracy) refers to memseta.o(.text) for __aeabi_memclr4
    usart_app.o(i.test_timing_accuracy) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_GetTime) for HAL_RTC_GetTime
    usart_app.o(i.test_timing_accuracy) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_GetDate) for HAL_RTC_GetDate
    usart_app.o(i.test_timing_accuracy) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    usart_app.o(i.test_timing_accuracy) refers to usart.o(.bss) for huart1
    usart_app.o(i.test_timing_accuracy) refers to rtc.o(.bss) for hrtc
    usart_app.o(i.test_timing_accuracy) refers to usart_app.o(.constdata) for .constdata
    usart_app.o(i.test_unhide_conversion) refers to _scanf_int.o(.text) for _scanf_int
    usart_app.o(i.test_unhide_conversion) refers to strlen.o(.text) for strlen
    usart_app.o(i.test_unhide_conversion) refers to usart_app.o(i.my_printf) for my_printf
    usart_app.o(i.test_unhide_conversion) refers to __0sscanf.o(.text) for __0sscanf
    usart_app.o(i.test_unhide_conversion) refers to f2d.o(.text) for __aeabi_f2d
    usart_app.o(i.test_unhide_conversion) refers to usart.o(.bss) for huart1
    usart_app.o(i.test_unix_timestamp_conversion) refers to memseta.o(.text) for __aeabi_memclr4
    usart_app.o(i.test_unix_timestamp_conversion) refers to usart_app.o(i.convert_rtc_to_unix_timestamp) for convert_rtc_to_unix_timestamp
    usart_app.o(i.test_unix_timestamp_conversion) refers to usart_app.o(i.my_printf) for my_printf
    usart_app.o(i.test_unix_timestamp_conversion) refers to usart_app.o(i.format_hex_output) for format_hex_output
    usart_app.o(i.test_unix_timestamp_conversion) refers to usart_app.o(i.convert_voltage_to_hex_format) for convert_voltage_to_hex_format
    usart_app.o(i.test_unix_timestamp_conversion) refers to usart_app.o(i.test_unhide_conversion) for test_unhide_conversion
    usart_app.o(i.test_unix_timestamp_conversion) refers to usart.o(.bss) for huart1
    usart_app.o(i.uart_task) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    usart_app.o(i.uart_task) refers to ringbuffer.o(i.rt_ringbuffer_get) for rt_ringbuffer_get
    usart_app.o(i.uart_task) refers to usart_app.o(i.parse_uart_command) for parse_uart_command
    usart_app.o(i.uart_task) refers to memseta.o(.text) for __aeabi_memclr4
    usart_app.o(i.uart_task) refers to usart_app.o(i.handle_sampling_output) for handle_sampling_output
    usart_app.o(i.uart_task) refers to usart_app.o(.bss) for .bss
    usart_app.o(.constdata) refers to usart_app.o(.conststring) for .conststring
    led_app.o(i.led_disp) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    led_app.o(i.led_disp) refers to led_app.o(.data) for .data
    led_app.o(i.led_task) refers to sampling_control.o(i.sampling_get_state) for sampling_get_state
    led_app.o(i.led_task) refers to sampling_control.o(i.sampling_check_overlimit) for sampling_check_overlimit
    led_app.o(i.led_task) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    led_app.o(i.led_task) refers to led_app.o(i.led_disp) for led_disp
    led_app.o(i.led_task) refers to led_app.o(.data) for .data
    btn_app.o(i.app_btn_init) refers to ebtn.o(i.ebtn_init) for ebtn_init
    btn_app.o(i.app_btn_init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT) for HAL_TIM_Base_Start_IT
    btn_app.o(i.app_btn_init) refers to btn_app.o(i.prv_btn_event) for prv_btn_event
    btn_app.o(i.app_btn_init) refers to btn_app.o(i.prv_btn_get_state) for prv_btn_get_state
    btn_app.o(i.app_btn_init) refers to btn_app.o(.data) for .data
    btn_app.o(i.app_btn_init) refers to tim.o(.bss) for htim14
    btn_app.o(i.btn_task) refers to ebtn.o(i.ebtn_process) for ebtn_process
    btn_app.o(i.btn_task) refers to stm32f4xx_hal.o(.data) for uwTick
    btn_app.o(i.prv_btn_event) refers to wououi_msg.o(i.WouoUI_MsgQueSend) for WouoUI_MsgQueSend
    btn_app.o(i.prv_btn_event) refers to sampling_control.o(i.sampling_init) for sampling_init
    btn_app.o(i.prv_btn_event) refers to sampling_control.o(i.sampling_get_state) for sampling_get_state
    btn_app.o(i.prv_btn_event) refers to sampling_control.o(i.sampling_stop) for sampling_stop
    btn_app.o(i.prv_btn_event) refers to sampling_control.o(i.sampling_start) for sampling_start
    btn_app.o(i.prv_btn_event) refers to sampling_control.o(i.sampling_get_cycle) for sampling_get_cycle
    btn_app.o(i.prv_btn_event) refers to usart_app.o(i.my_printf) for my_printf
    btn_app.o(i.prv_btn_event) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    btn_app.o(i.prv_btn_event) refers to printfa.o(i.__0sprintf) for __2sprintf
    btn_app.o(i.prv_btn_event) refers to data_storage.o(i.data_storage_write_log) for data_storage_write_log
    btn_app.o(i.prv_btn_event) refers to data_storage_1.o(i.data_storage_write_log) for data_storage_write_log
    btn_app.o(i.prv_btn_event) refers to sampling_control.o(i.sampling_set_cycle) for sampling_set_cycle
    btn_app.o(i.prv_btn_event) refers to wououi.o(.data) for p_cur_ui
    btn_app.o(i.prv_btn_event) refers to usart_app.o(.data) for g_last_output_time
    btn_app.o(i.prv_btn_event) refers to usart.o(.bss) for huart1
    btn_app.o(i.prv_btn_get_state) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    btn_app.o(.data) refers to btn_app.o(.constdata) for defaul_ebtn_param
    adc_app.o(i.HAL_ADC_ConvCpltCallback) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_DMA) for HAL_ADC_Stop_DMA
    adc_app.o(i.HAL_ADC_ConvCpltCallback) refers to adc.o(.bss) for hadc1
    adc_app.o(i.HAL_ADC_ConvCpltCallback) refers to adc_app.o(.data) for .data
    adc_app.o(i.adc_task) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) for HAL_ADC_Start_DMA
    adc_app.o(i.adc_task) refers to adc_app.o(.data) for .data
    adc_app.o(i.adc_task) refers to adc_app.o(.bss) for .bss
    adc_app.o(i.adc_task) refers to adc.o(.bss) for hadc1
    adc_app.o(i.adc_tim_dma_init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start) for HAL_TIM_Base_Start
    adc_app.o(i.adc_tim_dma_init) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) for HAL_ADC_Start_DMA
    adc_app.o(i.adc_tim_dma_init) refers to tim.o(.bss) for htim3
    adc_app.o(i.adc_tim_dma_init) refers to adc_app.o(.bss) for .bss
    adc_app.o(i.adc_tim_dma_init) refers to adc.o(.bss) for hadc1
    oled_app.o(i.LeftWheelMenu_CallBack) refers to wououi_page.o(i.WouoUI_ListTitlePageGetSelectOpt) for WouoUI_ListTitlePageGetSelectOpt
    oled_app.o(i.LeftWheelMenu_CallBack) refers to wououi_win.o(i.WouoUI_ValWinPageSetMinStepMax) for WouoUI_ValWinPageSetMinStepMax
    oled_app.o(i.LeftWheelMenu_CallBack) refers to wououi.o(i.WouoUI_JumpToPage) for WouoUI_JumpToPage
    oled_app.o(i.LeftWheelMenu_CallBack) refers to oled_app.o(.bss) for .bss
    oled_app.o(i.MainMenu_CallBack) refers to wououi_page.o(i.WouoUI_ListTitlePageGetSelectOpt) for WouoUI_ListTitlePageGetSelectOpt
    oled_app.o(i.MainMenu_CallBack) refers to strcmp.o(.text) for strcmp
    oled_app.o(i.MainMenu_CallBack) refers to wououi.o(i.WouoUI_JumpToPage) for WouoUI_JumpToPage
    oled_app.o(i.MainMenu_CallBack) refers to oled_app.o(.data) for .data
    oled_app.o(i.MainMenu_CallBack) refers to oled_app.o(.bss) for .bss
    oled_app.o(i.OLED_SendBuff) refers to memmovea.o(.text) for __aeabi_memcpy
    oled_app.o(i.OLED_SendBuff) refers to u8g2_buffer.o(i.u8g2_SendBuffer) for u8g2_SendBuffer
    oled_app.o(i.OLED_SendBuff) refers to main.o(.bss) for u8g2
    oled_app.o(i.PIDMenu_Init) refers to wououi.o(i.WouoUI_SelectDefaultUI) for WouoUI_SelectDefaultUI
    oled_app.o(i.PIDMenu_Init) refers to wououi_graph.o(i.WouoUI_BuffClear) for WouoUI_BuffClear
    oled_app.o(i.PIDMenu_Init) refers to wououi_graph.o(i.WouoUI_BuffSend) for WouoUI_BuffSend
    oled_app.o(i.PIDMenu_Init) refers to wououi_graph.o(i.WouoUI_GraphSetPenColor) for WouoUI_GraphSetPenColor
    oled_app.o(i.PIDMenu_Init) refers to wououi_page.o(i.WouoUI_TitlePageInit) for WouoUI_TitlePageInit
    oled_app.o(i.PIDMenu_Init) refers to wououi_page.o(i.WouoUI_ListPageInit) for WouoUI_ListPageInit
    oled_app.o(i.PIDMenu_Init) refers to wououi_win.o(i.WouoUI_ValWinPageInit) for WouoUI_ValWinPageInit
    oled_app.o(i.PIDMenu_Init) refers to oled_app.o(.data) for .data
    oled_app.o(i.PIDMenu_Init) refers to oled_app.o(i.MainMenu_CallBack) for MainMenu_CallBack
    oled_app.o(i.PIDMenu_Init) refers to oled_app.o(.constdata) for .constdata
    oled_app.o(i.PIDMenu_Init) refers to oled_app.o(.bss) for .bss
    oled_app.o(i.PIDMenu_Init) refers to oled_app.o(i.LeftWheelMenu_CallBack) for LeftWheelMenu_CallBack
    oled_app.o(i.PIDMenu_Init) refers to oled_app.o(i.RightWheelMenu_CallBack) for RightWheelMenu_CallBack
    oled_app.o(i.PIDMenu_Init) refers to oled_app.o(i.ParamAdjust_CallBack) for ParamAdjust_CallBack
    oled_app.o(i.ParamAdjust_CallBack) refers to wououi_page.o(i.WouoUI_ListTitlePageGetSelectOpt) for WouoUI_ListTitlePageGetSelectOpt
    oled_app.o(i.ParamAdjust_CallBack) refers to wououi_win.o(i.WouoUI_ValWinPageValIncrease) for WouoUI_ValWinPageValIncrease
    oled_app.o(i.ParamAdjust_CallBack) refers to wououi_win.o(i.WouoUI_ValWinPageValDecrease) for WouoUI_ValWinPageValDecrease
    oled_app.o(i.ParamAdjust_CallBack) refers to oled_app.o(.bss) for .bss
    oled_app.o(i.ParamAdjust_CallBack) refers to oled_app.o(.data) for .data
    oled_app.o(i.RightWheelMenu_CallBack) refers to wououi_page.o(i.WouoUI_ListTitlePageGetSelectOpt) for WouoUI_ListTitlePageGetSelectOpt
    oled_app.o(i.RightWheelMenu_CallBack) refers to wououi_win.o(i.WouoUI_ValWinPageSetMinStepMax) for WouoUI_ValWinPageSetMinStepMax
    oled_app.o(i.RightWheelMenu_CallBack) refers to wououi.o(i.WouoUI_JumpToPage) for WouoUI_JumpToPage
    oled_app.o(i.RightWheelMenu_CallBack) refers to oled_app.o(.bss) for .bss
    oled_app.o(i.oled_printf) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    oled_app.o(i.oled_printf) refers to oled.o(i.OLED_ShowStr) for OLED_ShowStr
    oled_app.o(i.oled_task) refers to sampling_control.o(i.sampling_get_state) for sampling_get_state
    oled_app.o(i.oled_task) refers to oled_app.o(i.oled_printf) for oled_printf
    oled_app.o(i.oled_task) refers to memseta.o(.text) for __aeabi_memclr4
    oled_app.o(i.oled_task) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_GetTime) for HAL_RTC_GetTime
    oled_app.o(i.oled_task) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_GetDate) for HAL_RTC_GetDate
    oled_app.o(i.oled_task) refers to sampling_control.o(i.sampling_get_voltage) for sampling_get_voltage
    oled_app.o(i.oled_task) refers to f2d.o(.text) for __aeabi_f2d
    oled_app.o(i.oled_task) refers to oled_app.o(.data) for .data
    oled_app.o(i.oled_task) refers to rtc.o(.bss) for hrtc
    oled_app.o(i.u8g2_gpio_and_delay_stm32) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    oled_app.o(i.u8x8_byte_hw_i2c) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) for HAL_I2C_Master_Transmit
    oled_app.o(i.u8x8_byte_hw_i2c) refers to oled_app.o(.data) for .data
    oled_app.o(i.u8x8_byte_hw_i2c) refers to oled_app.o(.bss) for .bss
    oled_app.o(i.u8x8_byte_hw_i2c) refers to i2c.o(.bss) for hi2c1
    oled_app.o(.data) refers to oled_app.o(.conststring) for .conststring
    flash_app.o(i.lfs_basic_test) refers to usart_app.o(i.my_printf) for my_printf
    flash_app.o(i.lfs_basic_test) refers to lfs.o(i.lfs_mount) for lfs_mount
    flash_app.o(i.lfs_basic_test) refers to lfs.o(i.lfs_format) for lfs_format
    flash_app.o(i.lfs_basic_test) refers to lfs.o(i.lfs_mkdir) for lfs_mkdir
    flash_app.o(i.lfs_basic_test) refers to lfs.o(i.lfs_file_open) for lfs_file_open
    flash_app.o(i.lfs_basic_test) refers to lfs.o(i.lfs_file_read) for lfs_file_read
    flash_app.o(i.lfs_basic_test) refers to lfs.o(i.lfs_file_rewind) for lfs_file_rewind
    flash_app.o(i.lfs_basic_test) refers to lfs.o(i.lfs_file_close) for lfs_file_close
    flash_app.o(i.lfs_basic_test) refers to lfs.o(i.lfs_file_write) for lfs_file_write
    flash_app.o(i.lfs_basic_test) refers to flash_app.o(i.list_dir_recursive) for list_dir_recursive
    flash_app.o(i.lfs_basic_test) refers to usart.o(.bss) for huart1
    flash_app.o(i.lfs_basic_test) refers to flash_app.o(.bss) for .bss
    flash_app.o(i.lfs_basic_test) refers to flash_app.o(.conststring) for .conststring
    flash_app.o(i.list_dir_recursive) refers to lfs.o(i.lfs_dir_open) for lfs_dir_open
    flash_app.o(i.list_dir_recursive) refers to usart_app.o(i.my_printf) for my_printf
    flash_app.o(i.list_dir_recursive) refers to lfs.o(i.lfs_dir_read) for lfs_dir_read
    flash_app.o(i.list_dir_recursive) refers to lfs.o(i.lfs_dir_close) for lfs_dir_close
    flash_app.o(i.list_dir_recursive) refers to strcmp.o(.text) for strcmp
    flash_app.o(i.list_dir_recursive) refers to printfa.o(i.__0sprintf) for __2sprintf
    flash_app.o(i.list_dir_recursive) refers to flash_app.o(.bss) for .bss
    flash_app.o(i.list_dir_recursive) refers to usart.o(.bss) for huart1
    flash_app.o(i.test_sd_fatfs) refers to memmovea.o(.text) for __aeabi_memcpy4
    flash_app.o(i.test_sd_fatfs) refers to usart_app.o(i.my_printf) for my_printf
    flash_app.o(i.test_sd_fatfs) refers to ff.o(i.f_mount) for f_mount
    flash_app.o(i.test_sd_fatfs) refers to ff.o(i.f_mkdir) for f_mkdir
    flash_app.o(i.test_sd_fatfs) refers to ff.o(i.f_open) for f_open
    flash_app.o(i.test_sd_fatfs) refers to memseta.o(.text) for __aeabi_memclr4
    flash_app.o(i.test_sd_fatfs) refers to ff.o(i.f_opendir) for f_opendir
    flash_app.o(i.test_sd_fatfs) refers to strlen.o(.text) for strlen
    flash_app.o(i.test_sd_fatfs) refers to ff.o(i.f_write) for f_write
    flash_app.o(i.test_sd_fatfs) refers to ff.o(i.f_close) for f_close
    flash_app.o(i.test_sd_fatfs) refers to ff.o(i.f_read) for f_read
    flash_app.o(i.test_sd_fatfs) refers to strcmp.o(.text) for strcmp
    flash_app.o(i.test_sd_fatfs) refers to ff.o(i.f_readdir) for f_readdir
    flash_app.o(i.test_sd_fatfs) refers to ff.o(i.f_closedir) for f_closedir
    flash_app.o(i.test_sd_fatfs) refers to flash_app.o(.conststring) for .conststring
    flash_app.o(i.test_sd_fatfs) refers to usart.o(.bss) for huart1
    flash_app.o(i.test_sd_fatfs) refers to fatfs.o(.data) for SDPath
    flash_app.o(i.test_sd_fatfs) refers to fatfs.o(.bss) for SDFatFS
    flash_app.o(i.test_sd_fatfs) refers to fatfs.o(.bss) for SDFile
    flash_app.o(i.test_spi_flash) refers to usart_app.o(i.my_printf) for my_printf
    flash_app.o(i.test_spi_flash) refers to gd25qxx.o(i.spi_flash_init) for spi_flash_init
    flash_app.o(i.test_spi_flash) refers to gd25qxx.o(i.spi_flash_read_id) for spi_flash_read_id
    flash_app.o(i.test_spi_flash) refers to gd25qxx.o(i.spi_flash_sector_erase) for spi_flash_sector_erase
    flash_app.o(i.test_spi_flash) refers to gd25qxx.o(i.spi_flash_buffer_read) for spi_flash_buffer_read
    flash_app.o(i.test_spi_flash) refers to strlen.o(.text) for strlen
    flash_app.o(i.test_spi_flash) refers to memseta.o(.text) for __aeabi_memclr4
    flash_app.o(i.test_spi_flash) refers to memmovea.o(.text) for __aeabi_memcpy
    flash_app.o(i.test_spi_flash) refers to gd25qxx.o(i.spi_flash_buffer_write) for spi_flash_buffer_write
    flash_app.o(i.test_spi_flash) refers to memcmp.o(.text) for memcmp
    flash_app.o(i.test_spi_flash) refers to usart.o(.bss) for huart1
    rtc_app.o(i.format_time_output) refers to printfa.o(i.__0snprintf) for __2snprintf
    rtc_app.o(i.parse_time_string) refers to _scanf_int.o(.text) for _scanf_int
    rtc_app.o(i.parse_time_string) refers to __0sscanf.o(.text) for __0sscanf
    rtc_app.o(i.rtc_get_time_info) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_GetTime) for HAL_RTC_GetTime
    rtc_app.o(i.rtc_get_time_info) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_GetDate) for HAL_RTC_GetDate
    rtc_app.o(i.rtc_get_time_info) refers to rtc.o(.bss) for hrtc
    rtc_app.o(i.rtc_print_current_time) refers to memseta.o(.text) for __aeabi_memclr4
    rtc_app.o(i.rtc_print_current_time) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_GetTime) for HAL_RTC_GetTime
    rtc_app.o(i.rtc_print_current_time) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_GetDate) for HAL_RTC_GetDate
    rtc_app.o(i.rtc_print_current_time) refers to usart_app.o(i.my_printf) for my_printf
    rtc_app.o(i.rtc_print_current_time) refers to rtc.o(.bss) for hrtc
    rtc_app.o(i.rtc_print_current_time) refers to usart.o(.bss) for huart1
    rtc_app.o(i.rtc_proc) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_GetTime) for HAL_RTC_GetTime
    rtc_app.o(i.rtc_proc) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_GetDate) for HAL_RTC_GetDate
    rtc_app.o(i.rtc_proc) refers to rtc_app.o(.bss) for .bss
    rtc_app.o(i.rtc_proc) refers to rtc.o(.bss) for hrtc
    rtc_app.o(i.rtc_proc) refers to rtc_app.o(.data) for .data
    rtc_app.o(i.rtc_set_time_from_string) refers to memseta.o(.text) for __aeabi_memclr4
    rtc_app.o(i.rtc_set_time_from_string) refers to rtc_app.o(i.parse_time_string) for parse_time_string
    rtc_app.o(i.rtc_set_time_from_string) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_SetTime) for HAL_RTC_SetTime
    rtc_app.o(i.rtc_set_time_from_string) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_SetDate) for HAL_RTC_SetDate
    rtc_app.o(i.rtc_set_time_from_string) refers to rtc.o(.bss) for hrtc
    system_check.o(i.check_flash_status) refers to gd25qxx.o(i.spi_flash_read_id) for spi_flash_read_id
    system_check.o(i.check_flash_status) refers to strcpy.o(.text) for strcpy
    system_check.o(i.check_flash_status) refers to system_check.o(i.get_flash_model_name) for get_flash_model_name
    system_check.o(i.check_flash_status) refers to system_check.o(.constdata) for .constdata
    system_check.o(i.check_tf_card_status) refers to diskio.o(i.disk_initialize) for disk_initialize
    system_check.o(i.check_tf_card_status) refers to diskio.o(i.disk_ioctl) for disk_ioctl
    system_check.o(i.get_flash_model_name) refers to system_check.o(.constdata) for .constdata
    system_check.o(i.print_rtc_time) refers to rtc_app.o(i.rtc_print_current_time) for rtc_print_current_time
    system_check.o(i.print_system_info) refers to usart_app.o(i.my_printf) for my_printf
    system_check.o(i.print_system_info) refers to rtc_app.o(i.rtc_print_current_time) for rtc_print_current_time
    system_check.o(i.print_system_info) refers to usart.o(.bss) for huart1
    system_check.o(i.system_self_check) refers to memseta.o(.text) for __aeabi_memclr4
    system_check.o(i.system_self_check) refers to system_check.o(i.check_flash_status) for check_flash_status
    system_check.o(i.system_self_check) refers to system_check.o(i.check_tf_card_status) for check_tf_card_status
    system_check.o(i.system_self_check) refers to system_check.o(i.print_system_info) for print_system_info
    system_check.o(.constdata) refers to system_check.o(.conststring) for .conststring
    config_manager.o(i.config_calculate_crc32) refers to config_manager.o(.constdata) for .constdata
    config_manager.o(i.config_get_params) refers to memmovea.o(.text) for __aeabi_memcpy4
    config_manager.o(i.config_get_params) refers to config_manager.o(.data) for .data
    config_manager.o(i.config_get_params) refers to config_manager.o(.bss) for .bss
    config_manager.o(i.config_get_sampling_cycle) refers to config_manager.o(.data) for .data
    config_manager.o(i.config_get_sampling_cycle) refers to config_manager.o(.bss) for .bss
    config_manager.o(i.config_init) refers to config_manager.o(i.config_load_from_flash) for config_load_from_flash
    config_manager.o(i.config_init) refers to memmovea.o(.text) for __aeabi_memcpy4
    config_manager.o(i.config_init) refers to config_manager.o(i.config_calculate_crc32) for config_calculate_crc32
    config_manager.o(i.config_init) refers to config_manager.o(.data) for .data
    config_manager.o(i.config_init) refers to config_manager.o(.constdata) for .constdata
    config_manager.o(i.config_init) refers to config_manager.o(.bss) for .bss
    config_manager.o(i.config_load_from_flash) refers to gd25qxx.o(i.spi_flash_buffer_read) for spi_flash_buffer_read
    config_manager.o(i.config_load_from_flash) refers to config_manager.o(i.config_calculate_crc32) for config_calculate_crc32
    config_manager.o(i.config_load_from_flash) refers to config_manager.o(i.config_validate_ratio) for config_validate_ratio
    config_manager.o(i.config_load_from_flash) refers to config_manager.o(i.config_validate_limit) for config_validate_limit
    config_manager.o(i.config_load_from_flash) refers to config_manager.o(i.config_validate_sampling_cycle) for config_validate_sampling_cycle
    config_manager.o(i.config_load_from_flash) refers to memmovea.o(.text) for __aeabi_memcpy4
    config_manager.o(i.config_load_from_flash) refers to config_manager.o(.bss) for .bss
    config_manager.o(i.config_reset_to_default) refers to memmovea.o(.text) for __aeabi_memcpy4
    config_manager.o(i.config_reset_to_default) refers to config_manager.o(i.config_calculate_crc32) for config_calculate_crc32
    config_manager.o(i.config_reset_to_default) refers to config_manager.o(.constdata) for .constdata
    config_manager.o(i.config_reset_to_default) refers to config_manager.o(.bss) for .bss
    config_manager.o(i.config_reset_to_default) refers to config_manager.o(.data) for .data
    config_manager.o(i.config_save_to_flash) refers to config_manager.o(i.config_calculate_crc32) for config_calculate_crc32
    config_manager.o(i.config_save_to_flash) refers to gd25qxx.o(i.spi_flash_sector_erase) for spi_flash_sector_erase
    config_manager.o(i.config_save_to_flash) refers to gd25qxx.o(i.spi_flash_buffer_write) for spi_flash_buffer_write
    config_manager.o(i.config_save_to_flash) refers to config_manager.o(.data) for .data
    config_manager.o(i.config_save_to_flash) refers to config_manager.o(.bss) for .bss
    config_manager.o(i.config_set_params) refers to config_manager.o(i.config_validate_ratio) for config_validate_ratio
    config_manager.o(i.config_set_params) refers to config_manager.o(i.config_validate_limit) for config_validate_limit
    config_manager.o(i.config_set_params) refers to config_manager.o(i.config_validate_sampling_cycle) for config_validate_sampling_cycle
    config_manager.o(i.config_set_params) refers to config_manager.o(i.config_calculate_crc32) for config_calculate_crc32
    config_manager.o(i.config_set_params) refers to config_manager.o(.data) for .data
    config_manager.o(i.config_set_params) refers to config_manager.o(.bss) for .bss
    config_manager.o(i.config_set_sampling_cycle) refers to config_manager.o(i.config_validate_sampling_cycle) for config_validate_sampling_cycle
    config_manager.o(i.config_set_sampling_cycle) refers to config_manager.o(i.config_calculate_crc32) for config_calculate_crc32
    config_manager.o(i.config_set_sampling_cycle) refers to config_manager.o(.data) for .data
    config_manager.o(i.config_set_sampling_cycle) refers to config_manager.o(.bss) for .bss
    ini_parser.o(i.ini_parse_file) refers to ff.o(i.f_open) for f_open
    ini_parser.o(i.ini_parse_file) refers to ini_parser.o(i.ini_parse_line) for ini_parse_line
    ini_parser.o(i.ini_parse_file) refers to ff.o(i.f_close) for f_close
    ini_parser.o(i.ini_parse_file) refers to ff.o(i.f_gets) for f_gets
    ini_parser.o(i.ini_parse_float) refers to strtof.o(i.__hardfp_strtof) for __hardfp_strtof
    ini_parser.o(i.ini_parse_line) refers to strncpy.o(.text) for strncpy
    ini_parser.o(i.ini_parse_line) refers to ini_parser.o(i.ini_trim_string) for ini_trim_string
    ini_parser.o(i.ini_parse_line) refers to strlen.o(.text) for strlen
    ini_parser.o(i.ini_parse_line) refers to strchr.o(.text) for strchr
    ini_parser.o(i.ini_parse_line) refers to strcmp.o(.text) for strcmp
    ini_parser.o(i.ini_parse_line) refers to ini_parser.o(i.ini_parse_float) for ini_parse_float
    ini_parser.o(i.ini_parse_line) refers to ini_parser.o(.data) for .data
    ini_parser.o(i.ini_trim_string) refers to ctype_o.o(.text) for __rt_ctype_table
    ini_parser.o(i.ini_trim_string) refers to strlen.o(.text) for strlen
    ini_parser.o(i.ini_trim_string) refers to memmovea.o(.text) for __aeabi_memmove
    sampling_control.o(i.sampling_check_overlimit) refers to config_manager.o(i.config_get_params) for config_get_params
    sampling_control.o(i.sampling_check_overlimit) refers to sampling_control.o(i.sampling_get_voltage) for sampling_get_voltage
    sampling_control.o(i.sampling_get_cycle) refers to sampling_control.o(.data) for .data
    sampling_control.o(i.sampling_get_cycle) refers to sampling_control.o(.bss) for .bss
    sampling_control.o(i.sampling_get_led_blink_state) refers to sampling_control.o(.data) for .data
    sampling_control.o(i.sampling_get_led_blink_state) refers to sampling_control.o(.bss) for .bss
    sampling_control.o(i.sampling_get_state) refers to sampling_control.o(.data) for .data
    sampling_control.o(i.sampling_get_state) refers to sampling_control.o(.bss) for .bss
    sampling_control.o(i.sampling_get_voltage) refers to config_manager.o(i.config_get_params) for config_get_params
    sampling_control.o(i.sampling_get_voltage) refers to adc_app.o(.data) for voltage
    sampling_control.o(i.sampling_init) refers to config_manager.o(i.config_init) for config_init
    sampling_control.o(i.sampling_init) refers to config_manager.o(i.config_get_sampling_cycle) for config_get_sampling_cycle
    sampling_control.o(i.sampling_init) refers to sampling_control.o(.data) for .data
    sampling_control.o(i.sampling_init) refers to sampling_control.o(.bss) for .bss
    sampling_control.o(i.sampling_set_cycle) refers to config_manager.o(i.config_set_sampling_cycle) for config_set_sampling_cycle
    sampling_control.o(i.sampling_set_cycle) refers to config_manager.o(i.config_save_to_flash) for config_save_to_flash
    sampling_control.o(i.sampling_set_cycle) refers to sampling_control.o(.data) for .data
    sampling_control.o(i.sampling_set_cycle) refers to sampling_control.o(.bss) for .bss
    sampling_control.o(i.sampling_should_sample) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    sampling_control.o(i.sampling_should_sample) refers to sampling_control.o(.data) for .data
    sampling_control.o(i.sampling_should_sample) refers to sampling_control.o(.bss) for .bss
    sampling_control.o(i.sampling_start) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    sampling_control.o(i.sampling_start) refers to sampling_control.o(.data) for .data
    sampling_control.o(i.sampling_start) refers to sampling_control.o(.bss) for .bss
    sampling_control.o(i.sampling_stop) refers to sampling_control.o(.data) for .data
    sampling_control.o(i.sampling_stop) refers to sampling_control.o(.bss) for .bss
    sampling_control.o(i.sampling_task) refers to sampling_control.o(i.sampling_update_led_blink) for sampling_update_led_blink
    sampling_control.o(i.sampling_task) refers to sampling_control.o(i.sampling_should_sample) for sampling_should_sample
    sampling_control.o(i.sampling_task) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    sampling_control.o(i.sampling_task) refers to sampling_control.o(i.sampling_get_voltage) for sampling_get_voltage
    sampling_control.o(i.sampling_task) refers to sampling_control.o(i.sampling_check_overlimit) for sampling_check_overlimit
    sampling_control.o(i.sampling_task) refers to sampling_control.o(.data) for .data
    sampling_control.o(i.sampling_task) refers to sampling_control.o(.bss) for .bss
    sampling_control.o(i.sampling_update_led_blink) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    sampling_control.o(i.sampling_update_led_blink) refers to sampling_control.o(.data) for .data
    sampling_control.o(i.sampling_update_led_blink) refers to sampling_control.o(.bss) for .bss
    data_storage.o(i.check_and_create_new_file) refers to ff.o(i.f_close) for f_close
    data_storage.o(i.check_and_create_new_file) refers to data_storage.o(i.generate_filename) for generate_filename
    data_storage.o(i.check_and_create_new_file) refers to printfa.o(i.__0sprintf) for __2sprintf
    data_storage.o(i.check_and_create_new_file) refers to ff.o(i.f_open) for f_open
    data_storage.o(i.check_and_create_new_file) refers to strcpy.o(.text) for strcpy
    data_storage.o(i.check_and_create_new_file) refers to data_storage.o(.bss) for .bss
    data_storage.o(i.check_and_create_new_file) refers to data_storage.o(.data) for .data
    data_storage.o(i.create_storage_directories) refers to ff.o(i.f_mkdir) for f_mkdir
    data_storage.o(i.create_storage_directories) refers to usart_app.o(i.my_printf) for my_printf
    data_storage.o(i.create_storage_directories) refers to data_storage.o(.data) for .data
    data_storage.o(i.create_storage_directories) refers to usart.o(.bss) for huart1
    data_storage.o(i.data_storage_init) refers to memseta.o(.text) for __aeabi_memclr4
    data_storage.o(i.data_storage_init) refers to usart_app.o(i.my_printf) for my_printf
    data_storage.o(i.data_storage_init) refers to ff.o(i.f_mount) for f_mount
    data_storage.o(i.data_storage_init) refers to data_storage.o(i.create_storage_directories) for create_storage_directories
    data_storage.o(i.data_storage_init) refers to ff.o(i.f_open) for f_open
    data_storage.o(i.data_storage_init) refers to ff.o(i.f_read) for f_read
    data_storage.o(i.data_storage_init) refers to ff.o(i.f_close) for f_close
    data_storage.o(i.data_storage_init) refers to ff.o(i.f_write) for f_write
    data_storage.o(i.data_storage_init) refers to data_storage.o(.bss) for .bss
    data_storage.o(i.data_storage_init) refers to usart.o(.bss) for huart1
    data_storage.o(i.data_storage_init) refers to fatfs.o(.data) for SDPath
    data_storage.o(i.data_storage_init) refers to fatfs.o(.bss) for SDFatFS
    data_storage.o(i.data_storage_init) refers to data_storage.o(.conststring) for .conststring
    data_storage.o(i.data_storage_init) refers to data_storage.o(.data) for .data
    data_storage.o(i.data_storage_test) refers to usart_app.o(i.my_printf) for my_printf
    data_storage.o(i.data_storage_test) refers to usart.o(.bss) for huart1
    data_storage.o(i.data_storage_write_hidedata) refers to data_storage.o(i.format_hidedata) for format_hidedata
    data_storage.o(i.data_storage_write_hidedata) refers to data_storage.o(i.write_data_to_file) for write_data_to_file
    data_storage.o(i.data_storage_write_log) refers to data_storage.o(i.format_log_data) for format_log_data
    data_storage.o(i.data_storage_write_log) refers to data_storage.o(i.write_data_to_file) for write_data_to_file
    data_storage.o(i.data_storage_write_overlimit) refers to data_storage.o(i.format_overlimit_data) for format_overlimit_data
    data_storage.o(i.data_storage_write_overlimit) refers to data_storage.o(i.write_data_to_file) for write_data_to_file
    data_storage.o(i.data_storage_write_sample) refers to data_storage.o(i.format_sample_data) for format_sample_data
    data_storage.o(i.data_storage_write_sample) refers to data_storage.o(i.write_data_to_file) for write_data_to_file
    data_storage.o(i.format_hidedata) refers to memseta.o(.text) for __aeabi_memclr4
    data_storage.o(i.format_hidedata) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_GetTime) for HAL_RTC_GetTime
    data_storage.o(i.format_hidedata) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_GetDate) for HAL_RTC_GetDate
    data_storage.o(i.format_hidedata) refers to f2d.o(.text) for __aeabi_f2d
    data_storage.o(i.format_hidedata) refers to printfa.o(i.__0sprintf) for __2sprintf
    data_storage.o(i.format_hidedata) refers to usart_app.o(i.convert_rtc_to_unix_timestamp) for convert_rtc_to_unix_timestamp
    data_storage.o(i.format_hidedata) refers to usart_app.o(i.format_hex_output) for format_hex_output
    data_storage.o(i.format_hidedata) refers to rtc.o(.bss) for hrtc
    data_storage.o(i.format_hidedata) refers to data_storage.o(i.format_sample_data) for i.format_sample_data
    data_storage.o(i.format_log_data) refers to memseta.o(.text) for __aeabi_memclr4
    data_storage.o(i.format_log_data) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_GetTime) for HAL_RTC_GetTime
    data_storage.o(i.format_log_data) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_GetDate) for HAL_RTC_GetDate
    data_storage.o(i.format_log_data) refers to printfa.o(i.__0sprintf) for __2sprintf
    data_storage.o(i.format_log_data) refers to rtc.o(.bss) for hrtc
    data_storage.o(i.format_overlimit_data) refers to memseta.o(.text) for __aeabi_memclr4
    data_storage.o(i.format_overlimit_data) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_GetTime) for HAL_RTC_GetTime
    data_storage.o(i.format_overlimit_data) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_GetDate) for HAL_RTC_GetDate
    data_storage.o(i.format_overlimit_data) refers to f2d.o(.text) for __aeabi_f2d
    data_storage.o(i.format_overlimit_data) refers to printfa.o(i.__0sprintf) for __2sprintf
    data_storage.o(i.format_overlimit_data) refers to rtc.o(.bss) for hrtc
    data_storage.o(i.format_sample_data) refers to memseta.o(.text) for __aeabi_memclr4
    data_storage.o(i.format_sample_data) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_GetTime) for HAL_RTC_GetTime
    data_storage.o(i.format_sample_data) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_GetDate) for HAL_RTC_GetDate
    data_storage.o(i.format_sample_data) refers to f2d.o(.text) for __aeabi_f2d
    data_storage.o(i.format_sample_data) refers to printfa.o(i.__0sprintf) for __2sprintf
    data_storage.o(i.format_sample_data) refers to rtc.o(.bss) for hrtc
    data_storage.o(i.generate_datetime_string) refers to memseta.o(.text) for __aeabi_memclr4
    data_storage.o(i.generate_datetime_string) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_GetTime) for HAL_RTC_GetTime
    data_storage.o(i.generate_datetime_string) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_GetDate) for HAL_RTC_GetDate
    data_storage.o(i.generate_datetime_string) refers to printfa.o(i.__0sprintf) for __2sprintf
    data_storage.o(i.generate_datetime_string) refers to rtc.o(.bss) for hrtc
    data_storage.o(i.generate_filename) refers to data_storage.o(i.generate_datetime_string) for generate_datetime_string
    data_storage.o(i.generate_filename) refers to printfa.o(i.__0sprintf) for __2sprintf
    data_storage.o(i.generate_filename) refers to data_storage.o(.data) for .data
    data_storage.o(i.write_data_to_file) refers to data_storage.o(i.check_and_create_new_file) for check_and_create_new_file
    data_storage.o(i.write_data_to_file) refers to strlen.o(.text) for strlen
    data_storage.o(i.write_data_to_file) refers to ff.o(i.f_write) for f_write
    data_storage.o(i.write_data_to_file) refers to ff.o(i.f_sync) for f_sync
    data_storage.o(i.write_data_to_file) refers to data_storage.o(.bss) for .bss
    data_storage.o(.data) refers to data_storage.o(.conststring) for .conststring
    device_id.o(i.device_id_init) refers to device_id.o(i.device_id_read) for device_id_read
    device_id.o(i.device_id_init) refers to device_id.o(i.device_id_set_default) for device_id_set_default
    device_id.o(i.device_id_init) refers to strcpy.o(.text) for strcpy
    device_id.o(i.device_id_init) refers to device_id.o(.bss) for .bss
    device_id.o(i.device_id_print_startup_info) refers to usart_app.o(i.my_printf) for my_printf
    device_id.o(i.device_id_print_startup_info) refers to strlen.o(.text) for strlen
    device_id.o(i.device_id_print_startup_info) refers to usart.o(.bss) for huart1
    device_id.o(i.device_id_print_startup_info) refers to device_id.o(.bss) for .bss
    device_id.o(i.device_id_read) refers to gd25qxx.o(i.spi_flash_buffer_read) for spi_flash_buffer_read
    device_id.o(i.device_id_read) refers to strncmp.o(.text) for strncmp
    device_id.o(i.device_id_read) refers to strncpy.o(.text) for strncpy
    device_id.o(i.device_id_set_default) refers to device_id.o(i.device_id_write) for device_id_write
    device_id.o(i.device_id_write) refers to strlen.o(.text) for strlen
    device_id.o(i.device_id_write) refers to memseta.o(.text) for __aeabi_memclr4
    device_id.o(i.device_id_write) refers to strncpy.o(.text) for strncpy
    device_id.o(i.device_id_write) refers to gd25qxx.o(i.spi_flash_sector_erase) for spi_flash_sector_erase
    device_id.o(i.device_id_write) refers to gd25qxx.o(i.spi_flash_buffer_write) for spi_flash_buffer_write
    device_id.o(i.device_id_write) refers to strcpy.o(.text) for strcpy
    device_id.o(i.device_id_write) refers to device_id.o(.bss) for .bss
    config_management.o(i.config_mgmt_get_params) refers to config_manager.o(i.config_get_params) for config_get_params
    config_management.o(i.config_mgmt_get_params) refers to config_management.o(.data) for .data
    config_management.o(i.config_mgmt_handle_conf_command) refers to data_storage.o(i.data_storage_write_log) for data_storage_write_log
    config_management.o(i.config_mgmt_handle_conf_command) refers to data_storage_1.o(i.data_storage_write_log) for data_storage_write_log
    config_management.o(i.config_mgmt_handle_conf_command) refers to config_management.o(i.config_mgmt_read_from_tfcard) for config_mgmt_read_from_tfcard
    config_management.o(i.config_mgmt_handle_conf_command) refers to config_manager.o(i.config_set_params) for config_set_params
    config_management.o(i.config_mgmt_handle_conf_command) refers to config_management.o(i.config_mgmt_save_to_flash) for config_mgmt_save_to_flash
    config_management.o(i.config_mgmt_handle_conf_command) refers to usart_app.o(i.my_printf) for my_printf
    config_management.o(i.config_mgmt_handle_conf_command) refers to f2d.o(.text) for __aeabi_f2d
    config_management.o(i.config_mgmt_handle_conf_command) refers to printfa.o(i.__0sprintf) for __2sprintf
    config_management.o(i.config_mgmt_handle_conf_command) refers to usart.o(.bss) for huart1
    config_management.o(i.config_mgmt_handle_conf_command) refers to config_management.o(.data) for .data
    config_management.o(i.config_mgmt_handle_config_read_command) refers to config_management.o(i.config_mgmt_load_from_flash) for config_mgmt_load_from_flash
    config_management.o(i.config_mgmt_handle_config_read_command) refers to usart_app.o(i.my_printf) for my_printf
    config_management.o(i.config_mgmt_handle_config_read_command) refers to config_management.o(i.config_mgmt_get_params) for config_mgmt_get_params
    config_management.o(i.config_mgmt_handle_config_read_command) refers to f2d.o(.text) for __aeabi_f2d
    config_management.o(i.config_mgmt_handle_config_read_command) refers to usart.o(.bss) for huart1
    config_management.o(i.config_mgmt_handle_config_save_command) refers to config_management.o(i.config_mgmt_get_params) for config_mgmt_get_params
    config_management.o(i.config_mgmt_handle_config_save_command) refers to f2d.o(.text) for __aeabi_f2d
    config_management.o(i.config_mgmt_handle_config_save_command) refers to usart_app.o(i.my_printf) for my_printf
    config_management.o(i.config_mgmt_handle_config_save_command) refers to config_management.o(i.config_mgmt_save_to_flash) for config_mgmt_save_to_flash
    config_management.o(i.config_mgmt_handle_config_save_command) refers to usart.o(.bss) for huart1
    config_management.o(i.config_mgmt_handle_limit_command) refers to data_storage.o(i.data_storage_write_log) for data_storage_write_log
    config_management.o(i.config_mgmt_handle_limit_command) refers to data_storage_1.o(i.data_storage_write_log) for data_storage_write_log
    config_management.o(i.config_mgmt_handle_limit_command) refers to config_management.o(i.config_mgmt_get_params) for config_mgmt_get_params
    config_management.o(i.config_mgmt_handle_limit_command) refers to f2d.o(.text) for __aeabi_f2d
    config_management.o(i.config_mgmt_handle_limit_command) refers to usart_app.o(i.my_printf) for my_printf
    config_management.o(i.config_mgmt_handle_limit_command) refers to usart.o(.bss) for huart1
    config_management.o(i.config_mgmt_handle_limit_input) refers to config_management.o(i.config_mgmt_validate_limit) for config_mgmt_validate_limit
    config_management.o(i.config_mgmt_handle_limit_input) refers to config_management.o(i.config_mgmt_get_params) for config_mgmt_get_params
    config_management.o(i.config_mgmt_handle_limit_input) refers to usart_app.o(i.my_printf) for my_printf
    config_management.o(i.config_mgmt_handle_limit_input) refers to f2d.o(.text) for __aeabi_f2d
    config_management.o(i.config_mgmt_handle_limit_input) refers to config_management.o(i.config_mgmt_set_limit) for config_mgmt_set_limit
    config_management.o(i.config_mgmt_handle_limit_input) refers to printfa.o(i.__0sprintf) for __2sprintf
    config_management.o(i.config_mgmt_handle_limit_input) refers to data_storage.o(i.data_storage_write_log) for data_storage_write_log
    config_management.o(i.config_mgmt_handle_limit_input) refers to data_storage_1.o(i.data_storage_write_log) for data_storage_write_log
    config_management.o(i.config_mgmt_handle_limit_input) refers to usart.o(.bss) for huart1
    config_management.o(i.config_mgmt_handle_ratio_command) refers to data_storage.o(i.data_storage_write_log) for data_storage_write_log
    config_management.o(i.config_mgmt_handle_ratio_command) refers to data_storage_1.o(i.data_storage_write_log) for data_storage_write_log
    config_management.o(i.config_mgmt_handle_ratio_command) refers to config_management.o(i.config_mgmt_get_params) for config_mgmt_get_params
    config_management.o(i.config_mgmt_handle_ratio_command) refers to f2d.o(.text) for __aeabi_f2d
    config_management.o(i.config_mgmt_handle_ratio_command) refers to usart_app.o(i.my_printf) for my_printf
    config_management.o(i.config_mgmt_handle_ratio_command) refers to usart.o(.bss) for huart1
    config_management.o(i.config_mgmt_handle_ratio_input) refers to config_management.o(i.config_mgmt_validate_ratio) for config_mgmt_validate_ratio
    config_management.o(i.config_mgmt_handle_ratio_input) refers to config_management.o(i.config_mgmt_get_params) for config_mgmt_get_params
    config_management.o(i.config_mgmt_handle_ratio_input) refers to usart_app.o(i.my_printf) for my_printf
    config_management.o(i.config_mgmt_handle_ratio_input) refers to f2d.o(.text) for __aeabi_f2d
    config_management.o(i.config_mgmt_handle_ratio_input) refers to config_management.o(i.config_mgmt_set_ratio) for config_mgmt_set_ratio
    config_management.o(i.config_mgmt_handle_ratio_input) refers to printfa.o(i.__0sprintf) for __2sprintf
    config_management.o(i.config_mgmt_handle_ratio_input) refers to data_storage.o(i.data_storage_write_log) for data_storage_write_log
    config_management.o(i.config_mgmt_handle_ratio_input) refers to data_storage_1.o(i.data_storage_write_log) for data_storage_write_log
    config_management.o(i.config_mgmt_handle_ratio_input) refers to usart.o(.bss) for huart1
    config_management.o(i.config_mgmt_load_from_flash) refers to config_manager.o(i.config_load_from_flash) for config_load_from_flash
    config_management.o(i.config_mgmt_load_from_flash) refers to config_management.o(.data) for .data
    config_management.o(i.config_mgmt_read_from_tfcard) refers to ini_parser.o(i.ini_parse_file) for ini_parse_file
    config_management.o(i.config_mgmt_read_from_tfcard) refers to config_management.o(i.config_mgmt_validate_ratio) for config_mgmt_validate_ratio
    config_management.o(i.config_mgmt_read_from_tfcard) refers to config_management.o(i.config_mgmt_validate_limit) for config_mgmt_validate_limit
    config_management.o(i.config_mgmt_save_to_flash) refers to config_manager.o(i.config_save_to_flash) for config_save_to_flash
    config_management.o(i.config_mgmt_set_limit) refers to config_management.o(i.config_mgmt_validate_limit) for config_mgmt_validate_limit
    config_management.o(i.config_mgmt_set_limit) refers to config_management.o(i.config_mgmt_get_params) for config_mgmt_get_params
    config_management.o(i.config_mgmt_set_limit) refers to config_manager.o(i.config_set_params) for config_set_params
    config_management.o(i.config_mgmt_set_limit) refers to config_management.o(.data) for .data
    config_management.o(i.config_mgmt_set_ratio) refers to config_management.o(i.config_mgmt_validate_ratio) for config_mgmt_validate_ratio
    config_management.o(i.config_mgmt_set_ratio) refers to config_management.o(i.config_mgmt_get_params) for config_mgmt_get_params
    config_management.o(i.config_mgmt_set_ratio) refers to config_manager.o(i.config_set_params) for config_set_params
    config_management.o(i.config_mgmt_set_ratio) refers to config_management.o(.data) for .data
    rtc_config.o(i.rtc_config_get_current_time) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_GetTime) for HAL_RTC_GetTime
    rtc_config.o(i.rtc_config_get_current_time) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_GetDate) for HAL_RTC_GetDate
    rtc_config.o(i.rtc_config_get_current_time) refers to rtc.o(.bss) for hrtc
    rtc_config.o(i.rtc_config_get_current_time_string) refers to rtc_config.o(i.rtc_config_get_current_time) for rtc_config_get_current_time
    rtc_config.o(i.rtc_config_get_current_time_string) refers to printfa.o(i.__0snprintf) for __2snprintf
    rtc_config.o(i.rtc_config_handle_config_command) refers to data_storage.o(i.data_storage_write_log) for data_storage_write_log
    rtc_config.o(i.rtc_config_handle_config_command) refers to data_storage_1.o(i.data_storage_write_log) for data_storage_write_log
    rtc_config.o(i.rtc_config_handle_config_command) refers to usart_app.o(i.my_printf) for my_printf
    rtc_config.o(i.rtc_config_handle_config_command) refers to usart.o(.bss) for huart1
    rtc_config.o(i.rtc_config_handle_now_command) refers to usart_app.o(i.my_printf) for my_printf
    rtc_config.o(i.rtc_config_handle_now_command) refers to rtc_config.o(i.rtc_config_print_current_time) for rtc_config_print_current_time
    rtc_config.o(i.rtc_config_handle_now_command) refers to usart.o(.bss) for huart1
    rtc_config.o(i.rtc_config_handle_time_input) refers to rtc_config.o(i.rtc_config_set_time) for rtc_config_set_time
    rtc_config.o(i.rtc_config_handle_time_input) refers to rtc_config.o(i.rtc_config_print_set_failed) for rtc_config_print_set_failed
    rtc_config.o(i.rtc_config_handle_time_input) refers to rtc_config.o(i.rtc_config_print_set_success) for rtc_config_print_set_success
    rtc_config.o(i.rtc_config_handle_time_input) refers to printfa.o(i.__0snprintf) for __2snprintf
    rtc_config.o(i.rtc_config_handle_time_input) refers to data_storage.o(i.data_storage_write_log) for data_storage_write_log
    rtc_config.o(i.rtc_config_handle_time_input) refers to data_storage_1.o(i.data_storage_write_log) for data_storage_write_log
    rtc_config.o(i.rtc_config_parse_time_string) refers to _scanf_int.o(.text) for _scanf_int
    rtc_config.o(i.rtc_config_parse_time_string) refers to __0sscanf.o(.text) for __0sscanf
    rtc_config.o(i.rtc_config_print_current_time) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_GetTime) for HAL_RTC_GetTime
    rtc_config.o(i.rtc_config_print_current_time) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_GetDate) for HAL_RTC_GetDate
    rtc_config.o(i.rtc_config_print_current_time) refers to usart_app.o(i.my_printf) for my_printf
    rtc_config.o(i.rtc_config_print_current_time) refers to rtc.o(.bss) for hrtc
    rtc_config.o(i.rtc_config_print_current_time) refers to usart.o(.bss) for huart1
    rtc_config.o(i.rtc_config_print_set_failed) refers to usart_app.o(i.my_printf) for my_printf
    rtc_config.o(i.rtc_config_print_set_failed) refers to usart.o(.bss) for huart1
    rtc_config.o(i.rtc_config_print_set_failed) refers to rtc_config.o(.conststring) for .conststring
    rtc_config.o(i.rtc_config_print_set_success) refers to usart_app.o(i.my_printf) for my_printf
    rtc_config.o(i.rtc_config_print_set_success) refers to rtc_config.o(i.rtc_config_print_current_time) for rtc_config_print_current_time
    rtc_config.o(i.rtc_config_print_set_success) refers to usart.o(.bss) for huart1
    rtc_config.o(i.rtc_config_set_time) refers to rtc_config.o(i.rtc_config_parse_time_string) for rtc_config_parse_time_string
    rtc_config.o(i.rtc_config_set_time) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_SetTime) for HAL_RTC_SetTime
    rtc_config.o(i.rtc_config_set_time) refers to stm32f4xx_hal_rtc.o(i.HAL_RTC_SetDate) for HAL_RTC_SetDate
    rtc_config.o(i.rtc_config_set_time) refers to rtc.o(.bss) for hrtc
    system_selftest.o(i.system_selftest_check_flash) refers to gd25qxx.o(i.spi_flash_read_id) for spi_flash_read_id
    system_selftest.o(i.system_selftest_check_flash) refers to strcpy.o(.text) for strcpy
    system_selftest.o(i.system_selftest_check_flash) refers to system_selftest.o(.constdata) for .constdata
    system_selftest.o(i.system_selftest_check_tfcard) refers to diskio.o(i.disk_initialize) for disk_initialize
    system_selftest.o(i.system_selftest_check_tfcard) refers to diskio.o(i.disk_ioctl) for disk_ioctl
    system_selftest.o(i.system_selftest_execute) refers to memseta.o(.text) for __aeabi_memclr4
    system_selftest.o(i.system_selftest_execute) refers to system_selftest.o(i.system_selftest_check_flash) for system_selftest_check_flash
    system_selftest.o(i.system_selftest_execute) refers to system_selftest.o(i.system_selftest_check_tfcard) for system_selftest_check_tfcard
    system_selftest.o(i.system_selftest_print_result) refers to usart_app.o(i.my_printf) for my_printf
    system_selftest.o(i.system_selftest_print_result) refers to rtc_app.o(i.rtc_print_current_time) for rtc_print_current_time
    system_selftest.o(i.system_selftest_print_result) refers to usart.o(.bss) for huart1
    system_selftest.o(.constdata) refers to system_selftest.o(.conststring) for .conststring
    command_handler.o(i.command_handler_get_state) refers to command_handler.o(.data) for .data
    command_handler.o(i.command_handler_set_state) refers to command_handler.o(.data) for .data
    data_processing.o(i.data_proc_convert_from_hide) refers to data_processing.o(i.data_proc_unix_to_datetime) for data_proc_unix_to_datetime
    data_processing.o(i.data_proc_convert_from_hide) refers to data_processing.o(i.data_proc_hex_to_voltage) for data_proc_hex_to_voltage
    data_processing.o(i.data_proc_convert_to_hide) refers to data_processing.o(i.data_proc_datetime_to_unix) for data_proc_datetime_to_unix
    data_processing.o(i.data_proc_convert_to_hide) refers to data_processing.o(i.data_proc_voltage_to_hex) for data_proc_voltage_to_hex
    sampling_control_1.o(i.sampling_ctrl_get_cycle) refers to sampling_control_1.o(.data) for .data
    sampling_control_1.o(i.sampling_ctrl_get_state) refers to sampling_control_1.o(.data) for .data
    sampling_control_1.o(i.sampling_ctrl_set_cycle) refers to sampling_control_1.o(.data) for .data
    sampling_control_1.o(i.sampling_ctrl_start) refers to sampling_control_1.o(.data) for .data
    sampling_control_1.o(i.sampling_ctrl_stop) refers to sampling_control_1.o(.data) for .data
    strtof.o(i.__hardfp_strtof) refers (Special) to iusefp.o(.text) for __I$use$fp
    strtof.o(i.__hardfp_strtof) refers to strtof.o(.text) for __strtof_int
    strtof.o(i.__softfp_strtof) refers (Special) to iusefp.o(.text) for __I$use$fp
    strtof.o(i.__softfp_strtof) refers to strtof.o(.text) for __strtof_int
    strtof.o(i.strtof) refers (Special) to iusefp.o(.text) for __I$use$fp
    strtof.o(i.strtof) refers to strtof.o(.text) for __strtof_int
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000F) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$00000011) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry12b.o(.ARM.Collect$$$$0000000E) for __rt_lib_shutdown_fini
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    ctype_o.o(.text) refers to ctype_o.o(.constdata) for .constdata
    ctype_o.o(.constdata) refers to ctype_o.o(.constdata) for __ctype_table
    isalnum_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isalpha_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isblank_o.o(.text) refers to ctype_o.o(.constdata) for __ctype_table
    iscntrl_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isdigit_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isgraph_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    islower_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isprint_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    ispunct_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isspace_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isupper_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isxdigit_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    malloc.o(i.free) refers to mvars.o(.data) for __microlib_freelist
    malloc.o(i.malloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    malloc.o(i.malloc) refers to mvars.o(.data) for __microlib_freelist
    malloc.o(i.malloc) refers to startup_stm32f429xx.o(HEAP) for __heap_base
    mallocr.o(i.__free$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.__malloc$realloc) refers to mallocr.o(i.internal_alloc) for internal_alloc
    mallocr.o(i.__malloc$realloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    mallocr.o(i.__malloc$realloc) refers to startup_stm32f429xx.o(HEAP) for __heap_base
    mallocr.o(i.__malloc$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.internal_alloc) refers to memmovea.o(.text) for __aeabi_memcpy
    mallocr.o(i.internal_alloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.realloc) refers to mallocr.o(i.__free$realloc) for __free$realloc
    mallocr.o(i.realloc) refers to mallocr.o(i.internal_alloc) for internal_alloc
    mallocr.o(i.realloc) refers to mallocr.o(i.__malloc$realloc) for __malloc$realloc
    mallocr.o(i.realloc) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__aligned_malloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    malloca.o(i.__aligned_malloc) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__aligned_malloc) refers to startup_stm32f429xx.o(HEAP) for __heap_base
    malloca.o(i.__free$memalign) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__malloc$memalign) refers to malloca.o(i.__aligned_malloc) for __aligned_malloc
    mallocra.o(i.__aligned_malloc$realloc) refers to mallocra.o(i.internal_alloc) for internal_alloc
    mallocra.o(i.__aligned_malloc$realloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    mallocra.o(i.__aligned_malloc$realloc) refers to startup_stm32f429xx.o(HEAP) for __heap_base
    mallocra.o(i.__aligned_malloc$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.__free$realloc$memalign) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.__malloc$realloc$memalign) refers to mallocra.o(i.__aligned_malloc$realloc) for __aligned_malloc$realloc
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.__free$realloc$memalign) for __free$realloc$memalign
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.internal_alloc) for internal_alloc
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.__malloc$realloc$memalign) for __malloc$realloc$memalign
    mallocra.o(i.__realloc$memalign) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.internal_alloc) refers to memmovea.o(.text) for __aeabi_memcpy
    mallocra.o(i.internal_alloc) refers to mvars.o(.data) for __microlib_freelist
    __0sscanf.o(.text) refers to scanf_char.o(.text) for __vfscanf_char
    __0sscanf.o(.text) refers to _sgetc.o(.text) for _sgetc
    _scanf_int.o(.text) refers to _chval.o(.text) for _chval
    scanf_fp.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    scanf_fp.o(.text) refers to dfltul.o(.text) for __aeabi_ul2d
    scanf_fp.o(.text) refers to dmul.o(.text) for __aeabi_dmul
    scanf_fp.o(.text) refers to ddiv.o(.text) for __aeabi_ddiv
    scanf_fp.o(.text) refers to scanf_fp.o(i._is_digit) for _is_digit
    scanf_fp.o(.text) refers to d2f.o(.text) for __aeabi_d2f
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f429xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f429xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    scanf_char.o(.text) refers to _scanf.o(.text) for __vfscanf
    scanf_char.o(.text) refers to isspace_o.o(.text) for isspace
    strtof.o(.text) refers to strtod.o(.text) for __strtod_int
    strtof.o(.text) refers to narrow.o(i.__mathlib_narrow) for __mathlib_narrow
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfltul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    narrow.o(i.__hardfp___mathlib_tofloat) refers (Special) to iusefp.o(.text) for __I$use$fp
    narrow.o(i.__hardfp___mathlib_tofloat) refers to frexp.o(i.frexp) for frexp
    narrow.o(i.__hardfp___mathlib_tofloat) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    narrow.o(i.__hardfp___mathlib_tofloat) refers to fpstat.o(.text) for __ieee_status
    narrow.o(i.__hardfp___mathlib_tofloat) refers to d2f.o(.text) for __aeabi_d2f
    narrow.o(i.__hardfp___mathlib_tofloat) refers to errno.o(i.__set_errno) for __set_errno
    narrow.o(i.__hardfp___mathlib_tofloat) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    narrow.o(i.__mathlib_narrow) refers (Special) to iusefp.o(.text) for __I$use$fp
    narrow.o(i.__mathlib_narrow) refers to narrow.o(i.__hardfp___mathlib_tofloat) for __hardfp___mathlib_tofloat
    narrow.o(i.__mathlib_tofloat) refers (Special) to iusefp.o(.text) for __I$use$fp
    narrow.o(i.__mathlib_tofloat) refers to narrow.o(i.__hardfp___mathlib_tofloat) for __hardfp___mathlib_tofloat
    narrow.o(i.__softfp___mathlib_tofloat) refers (Special) to iusefp.o(.text) for __I$use$fp
    narrow.o(i.__softfp___mathlib_tofloat) refers to narrow.o(i.__hardfp___mathlib_tofloat) for __hardfp___mathlib_tofloat
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    isspace_c.o(.text) refers to ctype_c.o(.text) for __ctype_lookup
    _scanf.o(.text) refers (Weak) to scanf_fp.o(.text) for _scanf_real
    _scanf.o(.text) refers (Weak) to _scanf_int.o(.text) for _scanf_int
    strtod.o(.text) refers to scanf_fp.o(.text) for _scanf_real
    strtod.o(.text) refers to _sgetc.o(.text) for _sgetc
    strtod.o(.text) refers to isspace_o.o(.text) for isspace
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    frexp.o(i.__hardfp_frexp) refers (Special) to iusefp.o(.text) for __I$use$fp
    frexp.o(i.__hardfp_frexp) refers to dmul.o(.text) for __aeabi_dmul
    frexp.o(i.__softfp_frexp) refers (Special) to iusefp.o(.text) for __I$use$fp
    frexp.o(i.__softfp_frexp) refers to dmul.o(.text) for __aeabi_dmul
    frexp.o(i.frexp) refers (Special) to iusefp.o(.text) for __I$use$fp
    frexp.o(i.frexp) refers to dmul.o(.text) for __aeabi_dmul
    ctype_c.o(.text) refers to ctype_c.o(.constdata) for .constdata
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    scanf_fp.o(i._is_digit) refers (Special) to iusefp.o(.text) for __I$use$fp

