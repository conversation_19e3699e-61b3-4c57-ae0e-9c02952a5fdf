#include "config_management.h"

// 需要包含的外部接口
extern int my_printf(void* huart, const char *format, ...);
extern void* huart1;

// 外部函数声明
extern int data_storage_write_log(const char* log_message);
extern int config_get_params(config_params_t* params);
extern int config_set_params(const config_params_t* params);
extern int config_save_to_flash(void);
extern int config_load_from_flash(void);
extern int config_validate_ratio(float ratio);
extern int config_validate_limit(float limit);

// INI解析器接口
extern int ini_parse_file(const char *filename, sys_ini_config_t *config);

// INI状态枚举
typedef enum {
    INI_OK = 0,
    INI_ERROR = 1,
    INI_FILE_NOT_FOUND = 2,
    INI_FORMAT_ERROR = 3,
    INI_VALUE_ERROR = 4
} ini_status_t;

// 配置状态枚举
typedef enum {
    CONFIG_OK = 0,
    CONFIG_ERROR = 1,
    CONFIG_INVALID_PARAM = 2
} config_status_t;

// 静态变量存储当前配置
static config_params_t g_current_config = {0.0f, 0.0f};
static bool g_config_initialized = false;

/**
 * @brief 从TF卡读取config.ini配置
 * @param params 配置参数结构体指针
 * @return sys_func_status_t 读取状态
 */
sys_func_status_t config_mgmt_read_from_tfcard(config_params_t* params)
{
    if (params == NULL) {
        return SYS_FUNC_INVALID_PARAM;
    }

    ini_config_t ini_config;

    // 解析config.ini文件
    ini_status_t ini_status = ini_parse_file("config.ini", &ini_config);

    if (ini_status == INI_FILE_NOT_FOUND) {
        return SYS_FUNC_NOT_FOUND;
    }

    if (ini_status != INI_OK) {
        return SYS_FUNC_ERROR;
    }

    // 检查参数是否完整
    if (!ini_config.ratio_found || !ini_config.limit_found) {
        return SYS_FUNC_ERROR;
    }

    // 验证参数范围
    if (config_mgmt_validate_ratio(ini_config.ratio) != SYS_FUNC_OK) {
        return SYS_FUNC_INVALID_PARAM;
    }

    if (config_mgmt_validate_limit(ini_config.limit) != SYS_FUNC_OK) {
        return SYS_FUNC_INVALID_PARAM;
    }

    // 设置参数
    params->ratio = ini_config.ratio;
    params->limit = ini_config.limit;

    return SYS_FUNC_OK;
}

/**
 * @brief 设置变比参数
 * @param ratio 变比值 (0-100)
 * @return sys_func_status_t 设置状态
 */
sys_func_status_t config_mgmt_set_ratio(float ratio)
{
    // 参数验证
    if (config_mgmt_validate_ratio(ratio) != SYS_FUNC_OK) {
        return SYS_FUNC_INVALID_PARAM;
    }

    // 获取当前配置
    config_params_t current_params;
    if (config_mgmt_get_params(&current_params) != SYS_FUNC_OK) {
        return SYS_FUNC_ERROR;
    }

    // 设置新的变比值
    current_params.ratio = ratio;

    // 更新配置
    if (config_set_params(&current_params) != CONFIG_OK) {
        return SYS_FUNC_ERROR;
    }

    // 更新内部状态
    g_current_config.ratio = ratio;

    return SYS_FUNC_OK;
}

/**
 * @brief 设置阈值参数
 * @param limit 阈值 (0-500)
 * @return sys_func_status_t 设置状态
 */
sys_func_status_t config_mgmt_set_limit(float limit)
{
    // 参数验证
    if (config_mgmt_validate_limit(limit) != SYS_FUNC_OK) {
        return SYS_FUNC_INVALID_PARAM;
    }

    // 获取当前配置
    config_params_t current_params;
    if (config_mgmt_get_params(&current_params) != SYS_FUNC_OK) {
        return SYS_FUNC_ERROR;
    }

    // 设置新的阈值
    current_params.limit = limit;

    // 更新配置
    if (config_set_params(&current_params) != CONFIG_OK) {
        return SYS_FUNC_ERROR;
    }

    // 更新内部状态
    g_current_config.limit = limit;

    return SYS_FUNC_OK;
}

/**
 * @brief 获取当前配置参数
 * @param params 配置参数结构体指针
 * @return sys_func_status_t 获取状态
 */
sys_func_status_t config_mgmt_get_params(config_params_t* params)
{
    if (params == NULL) {
        return SYS_FUNC_INVALID_PARAM;
    }

    // 如果未初始化，从底层获取
    if (!g_config_initialized) {
        if (config_get_params(&g_current_config) != CONFIG_OK) {
            return SYS_FUNC_ERROR;
        }
        g_config_initialized = true;
    }

    // 返回当前配置
    *params = g_current_config;

    return SYS_FUNC_OK;
}

/**
 * @brief 保存配置到Flash
 * @return sys_func_status_t 保存状态
 */
sys_func_status_t config_mgmt_save_to_flash(void)
{
    if (config_save_to_flash() != CONFIG_OK) {
        return SYS_FUNC_ERROR;
    }

    return SYS_FUNC_OK;
}

/**
 * @brief 从Flash加载配置
 * @return sys_func_status_t 加载状态
 */
sys_func_status_t config_mgmt_load_from_flash(void)
{
    if (config_load_from_flash() != CONFIG_OK) {
        return SYS_FUNC_ERROR;
    }

    // 重新获取配置
    g_config_initialized = false;

    return SYS_FUNC_OK;
}

/**
 * @brief 验证变比参数
 * @param ratio 变比值
 * @return sys_func_status_t 验证状态
 */
sys_func_status_t config_mgmt_validate_ratio(float ratio)
{
    if (ratio < 0.0f || ratio > 100.0f) {
        return SYS_FUNC_INVALID_PARAM;
    }
    return SYS_FUNC_OK;
}

/**
 * @brief 验证阈值参数
 * @param limit 阈值
 * @return sys_func_status_t 验证状态
 */
sys_func_status_t config_mgmt_validate_limit(float limit)
{
    if (limit < 0.0f || limit > 500.0f) {
        return SYS_FUNC_INVALID_PARAM;
    }
    return SYS_FUNC_OK;
}

/**
 * @brief 处理conf命令 - 从TF卡读取config.ini文件并更新配置参数
 */
void config_mgmt_handle_conf_command(void)
{
    config_params_t params;

    // 记录命令执行的日志
    data_storage_write_log("conf command");

    // 从TF卡读取配置
    sys_func_status_t status = config_mgmt_read_from_tfcard(&params);

    if (status == SYS_FUNC_NOT_FOUND) {
        my_printf(huart1, "config.ini file not found.\r\n");
        return;
    }

    if (status == SYS_FUNC_INVALID_PARAM) {
        my_printf(huart1, "config.ini parameter out of range.\r\n");
        return;
    }

    if (status != SYS_FUNC_OK) {
        my_printf(huart1, "config.ini format error.\r\n");
        return;
    }

    // 更新系统配置
    if (config_set_params(&params) != CONFIG_OK) {
        my_printf(huart1, "config update failed.\r\n");
        return;
    }

    // 保存到Flash
    if (config_mgmt_save_to_flash() != SYS_FUNC_OK) {
        my_printf(huart1, "config save to flash failed.\r\n");
        return;
    }

    // 输出成功结果
    my_printf(huart1, "Ratio = %.1f\r\n", params.ratio);
    my_printf(huart1, "Limit = %.1f\r\n", params.limit);
    my_printf(huart1, "config read success\r\n");

    // 记录成功日志
    char log_msg[128];
    sprintf(log_msg, "config read success - ratio %.1f, limit %.1f", params.ratio, params.limit);
    data_storage_write_log(log_msg);

    // 更新内部状态
    g_current_config = params;
    g_config_initialized = true;
}

/**
 * @brief 处理ratio命令 - 显示当前变比并等待用户输入
 */
void config_mgmt_handle_ratio_command(void)
{
    config_params_t config_params;

    // 记录ratio命令日志
    data_storage_write_log("ratio command");

    // 获取当前配置
    if (config_mgmt_get_params(&config_params) != SYS_FUNC_OK) {
        my_printf(huart1, "config system error.\r\n");
        return;
    }

    // 显示当前ratio值
    my_printf(huart1, "Ratio =%.1f\r\n", config_params.ratio);

    // 提示用户输入
    my_printf(huart1, "Input value(0~100):\r\n");
}

/**
 * @brief 处理limit命令 - 显示当前阈值并等待用户输入
 */
void config_mgmt_handle_limit_command(void)
{
    config_params_t config_params;

    // 记录limit命令日志
    data_storage_write_log("limit command");

    // 获取当前配置
    if (config_mgmt_get_params(&config_params) != SYS_FUNC_OK) {
        my_printf(huart1, "config system error.\r\n");
        return;
    }

    // 显示当前limit值
    my_printf(huart1, "limit = %.1f\r\n", config_params.limit);

    // 提示用户输入
    my_printf(huart1, "Input value(0~500):\r\n");
}

/**
 * @brief 处理config save命令 - 保存当前设置到Flash
 */
void config_mgmt_handle_config_save_command(void)
{
    config_params_t config_params;

    // 获取当前配置参数
    if (config_mgmt_get_params(&config_params) != SYS_FUNC_OK) {
        my_printf(huart1, "config system error.\r\n");
        return;
    }

    // 显示当前配置
    my_printf(huart1, "ratio:%.2f\r\n", config_params.ratio);
    my_printf(huart1, "limit:%.2f\r\n", config_params.limit);

    // 保存配置到Flash
    if (config_mgmt_save_to_flash() != SYS_FUNC_OK) {
        my_printf(huart1, "save parameters to flash failed.\r\n");
        return;
    }

    // 输出成功信息
    my_printf(huart1, "save parameters to flash\r\n");
}

/**
 * @brief 处理config read命令 - 从Flash读取配置
 */
void config_mgmt_handle_config_read_command(void)
{
    config_params_t config_params;

    // 从Flash读取配置
    if (config_mgmt_load_from_flash() != SYS_FUNC_OK) {
        my_printf(huart1, "read parameters from flash failed.\r\n");
        return;
    }

    // 输出成功信息
    my_printf(huart1, "read parameters from flash\r\n");

    // 获取读取的配置参数并显示
    if (config_mgmt_get_params(&config_params) != SYS_FUNC_OK) {
        my_printf(huart1, "config system error.\r\n");
        return;
    }

    // 显示读取的配置
    my_printf(huart1, "ratio: %.2f\r\n", config_params.ratio);
    my_printf(huart1, "limit: %.2f\r\n", config_params.limit);
}

/**
 * @brief 处理ratio数值输入
 * @param value 输入的ratio值
 * @return sys_func_status_t 处理状态
 */
sys_func_status_t config_mgmt_handle_ratio_input(float value)
{
    config_params_t config_params;

    // 验证ratio参数范围
    if (config_mgmt_validate_ratio(value) != SYS_FUNC_OK) {
        // 获取当前配置显示
        if (config_mgmt_get_params(&config_params) == SYS_FUNC_OK) {
            my_printf(huart1, "ratio invalid\r\n");
            my_printf(huart1, "Ratio=%.1f\r\n", config_params.ratio);
        }
        return SYS_FUNC_INVALID_PARAM;
    }

    // 设置ratio参数
    if (config_mgmt_set_ratio(value) == SYS_FUNC_OK) {
        my_printf(huart1, "ratio modified success\r\n");
        my_printf(huart1, "Ratio=%.1f\r\n", value);

        // 记录ratio设置成功日志
        char log_msg[64];
        sprintf(log_msg, "ratio config success to %.1f", value);
        data_storage_write_log(log_msg);

        return SYS_FUNC_OK;
    } else {
        my_printf(huart1, "config update failed.\r\n");
        return SYS_FUNC_ERROR;
    }
}

/**
 * @brief 处理limit数值输入
 * @param value 输入的limit值
 * @return sys_func_status_t 处理状态
 */
sys_func_status_t config_mgmt_handle_limit_input(float value)
{
    config_params_t config_params;

    // 验证limit参数范围
    if (config_mgmt_validate_limit(value) != SYS_FUNC_OK) {
        // 获取当前配置显示
        if (config_mgmt_get_params(&config_params) == SYS_FUNC_OK) {
            my_printf(huart1, "limit invalid\r\n");
            my_printf(huart1, "limit = %.1f\r\n", config_params.limit);
        }
        return SYS_FUNC_INVALID_PARAM;
    }

    // 设置limit参数
    if (config_mgmt_set_limit(value) == SYS_FUNC_OK) {
        my_printf(huart1, "limit modified success\r\n");
        my_printf(huart1, "limit=%.1f\r\n", value);

        // 记录limit设置成功日志
        char log_msg[64];
        sprintf(log_msg, "limit config success to %.1f", value);
        data_storage_write_log(log_msg);

        return SYS_FUNC_OK;
    } else {
        my_printf(huart1, "config update failed.\r\n");
        return SYS_FUNC_ERROR;
    }
}
