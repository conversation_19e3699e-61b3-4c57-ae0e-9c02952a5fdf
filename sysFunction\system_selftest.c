#include "system_selftest.h"

// 系统自检模块实现
// 基于APP/system_check.c重构实现

/**
 * @brief 执行系统自检
 * @param result 自检结果结构体指针
 * @return sys_func_status_t 执行状态
 */
sys_func_status_t system_selftest_execute(system_check_result_t* result)
{
    if (result == NULL) {
        return SYS_FUNC_INVALID_PARAM;
    }
    
    // 初始化结果结构
    memset(result, 0, sizeof(system_check_result_t));
    
    // 执行Flash检测
    sys_func_status_t flash_status = system_selftest_check_flash(&result->system_info.flash_info);
    
    // 执行TF卡检测
    sys_func_status_t tf_status = system_selftest_check_tfcard(&result->system_info.tf_card_info);
    
    // RTC状态默认OK
    result->system_info.rtc_status = SYSTEM_CHECK_OK;
    
    // 判断整体测试是否通过
    result->test_passed = (flash_status == SYS_FUNC_OK) && (tf_status == SYS_FUNC_OK);
    
    return SYS_FUNC_OK;
}

/**
 * @brief 打印系统自检结果
 * @param result 自检结果结构体指针
 */
void system_selftest_print_result(const system_check_result_t* result)
{
    if (result == NULL) {
        return;
    }
    
    // TODO: 实现精确的输出格式
    // ======system selftest======
    // flash............ok
    // TF card............ok
    // flash ID: 0xCxxxxx
    // TF card memory:xxxxxKB
    // RTC:2025-01-01 01:00:50
    // ======system selftest======
}

/**
 * @brief 检测Flash状态
 * @param flash_info Flash信息结构体指针
 * @return sys_func_status_t 检测状态
 */
sys_func_status_t system_selftest_check_flash(flash_info_t* flash_info)
{
    if (flash_info == NULL) {
        return SYS_FUNC_INVALID_PARAM;
    }
    
    // TODO: 实现Flash检测逻辑
    // 基于APP/system_check.c中的check_flash_status函数
    
    return SYS_FUNC_OK;
}

/**
 * @brief 检测TF卡状态
 * @param tf_info TF卡信息结构体指针
 * @return sys_func_status_t 检测状态
 */
sys_func_status_t system_selftest_check_tfcard(tf_card_info_t* tf_info)
{
    if (tf_info == NULL) {
        return SYS_FUNC_INVALID_PARAM;
    }
    
    // TODO: 实现TF卡检测逻辑
    // 基于APP/system_check.c中的check_tf_card_status函数
    
    return SYS_FUNC_OK;
}
