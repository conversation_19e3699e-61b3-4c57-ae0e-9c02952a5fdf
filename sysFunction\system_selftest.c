#include "system_selftest.h"

// 需要包含的外部接口
extern int my_printf(void* huart, const char *format, ...);
extern void rtc_print_current_time(void);
extern uint32_t spi_flash_read_id(void);
extern int disk_initialize(int drive);
extern int disk_ioctl(int drive, int cmd, void* buff);

// 外部变量声明
extern void* huart1;

// FATFS相关定义
#define GET_SECTOR_COUNT 1
#define GET_SECTOR_SIZE  2
#define RES_OK 0

// Flash型号识别表
typedef struct {
    uint32_t id;
    const char *model;
    uint32_t capacity_mb;
} flash_model_t;

static const flash_model_t flash_models[] = {
    {0xC84017, "GD25Q64", 8}, // 8MB
    {0xC84016, "GD25Q32", 4}, // 4MB
    {0xC84015, "GD25Q16", 2}, // 2MB
    {0xC84014, "GD25Q80", 1}, // 1MB
    {0xC84013, "GD25Q40", 1}, // 512KB = 0.5MB显示为1MB
    {0xEF4017, "W25Q64", 8},  // Winbond 8MB
    {0xEF4016, "W25Q32", 4},  // Winbond 4MB
    {0xEF4015, "W25Q16", 2},  // Winbond 2MB
    {0x000000, "Unknown", 0}  // 未知型号
};

// 根据Flash ID获取型号信息
static const char *get_flash_model_name(uint32_t flash_id)
{
    for (int i = 0; flash_models[i].id != 0x000000; i++) {
        if (flash_models[i].id == flash_id) {
            return flash_models[i].model;
        }
    }
    return "Unknown";
}

// 获取Flash容量
static uint32_t get_flash_capacity(uint32_t flash_id)
{
    for (int i = 0; flash_models[i].id != 0x000000; i++) {
        if (flash_models[i].id == flash_id) {
            return flash_models[i].capacity_mb;
        }
    }
    return 0;
}

// 获取SD卡容量(KB)
static uint32_t sd_card_capacity_get(void)
{
    uint32_t sector_count = 0;
    uint16_t sector_size = 0;

    // 获取扇区数量
    if (disk_ioctl(0, GET_SECTOR_COUNT, &sector_count) == RES_OK) {
        // 获取扇区大小
        if (disk_ioctl(0, GET_SECTOR_SIZE, &sector_size) == RES_OK) {
            // 计算容量(KB) = 扇区数量 * 扇区大小 / 1024
            return (uint32_t)((uint64_t)sector_count * sector_size / 1024);
        }
    }
    return 0;
}

/**
 * @brief 执行系统自检
 * @param result 自检结果结构体指针
 * @return sys_func_status_t 执行状态
 */
sys_func_status_t system_selftest_execute(sys_check_result_t* result)
{
    if (result == NULL) {
        return SYS_FUNC_INVALID_PARAM;
    }

    // 初始化结果结构
    memset(result, 0, sizeof(sys_check_result_t));

    // 执行Flash检测
    sys_func_status_t flash_status = system_selftest_check_flash(&result->system_info.flash_info);

    // 执行TF卡检测
    sys_func_status_t tf_status = system_selftest_check_tfcard(&result->system_info.tf_card_info);

    // RTC状态默认OK
    result->system_info.rtc_status = SYS_CHECK_OK;

    // 判断整体测试是否通过
    result->test_passed = (flash_status == SYS_FUNC_OK) && (tf_status == SYS_FUNC_OK);

    return SYS_FUNC_OK;
}

/**
 * @brief 检测Flash状态
 * @param flash_info Flash信息结构体指针
 * @return sys_func_status_t 检测状态
 */
sys_func_status_t system_selftest_check_flash(sys_flash_info_t* flash_info)
{
    if (flash_info == NULL) {
        return SYS_FUNC_INVALID_PARAM;
    }

    // 获取Flash ID
    flash_info->flash_id = spi_flash_read_id();

    // 检查是否为有效ID
    if (flash_info->flash_id == 0x000000 || flash_info->flash_id == 0xFFFFFF) {
        flash_info->status = SYS_CHECK_NOT_FOUND;
        strcpy(flash_info->model_name, "Not Found");
        flash_info->capacity_mb = 0;
        return SYS_FUNC_NOT_FOUND;
    }

    // 获取型号和容量信息
    const char *model = get_flash_model_name(flash_info->flash_id);
    strcpy(flash_info->model_name, model);
    flash_info->capacity_mb = get_flash_capacity(flash_info->flash_id);
    flash_info->status = SYS_CHECK_OK;

    return SYS_FUNC_OK;
}

/**
 * @brief 检测TF卡状态
 * @param tf_info TF卡信息结构体指针
 * @return sys_func_status_t 检测状态
 */
sys_func_status_t system_selftest_check_tfcard(sys_tf_card_info_t* tf_info)
{
    if (tf_info == NULL) {
        return SYS_FUNC_INVALID_PARAM;
    }

    // 使用disk_initialize检测SD卡
    int sd_status = disk_initialize(0);
    if (sd_status == 0) {
        // SD卡初始化成功，获取容量信息
        uint32_t capacity_kb = sd_card_capacity_get();

        tf_info->capacity_mb = capacity_kb / 1024; // 转换为MB
        tf_info->sector_size = 512;                // 标准扇区大小
        tf_info->sector_count = capacity_kb * 2;   // KB转换为扇区数(512字节/扇区)
        tf_info->status = SYS_CHECK_OK;

        return SYS_FUNC_OK;
    } else {
        // SD卡检测失败
        tf_info->status = SYS_CHECK_NOT_FOUND;
        tf_info->capacity_mb = 0;
        tf_info->sector_count = 0;
        tf_info->sector_size = 0;

        return SYS_FUNC_NOT_FOUND;
    }
}

/**
 * @brief 打印系统自检结果
 * @param result 自检结果结构体指针
 */
void system_selftest_print_result(const sys_check_result_t* result)
{
    if (result == NULL) {
        return;
    }

    const sys_system_info_t* info = &result->system_info;

    // 开始标识
    my_printf(huart1, "======system selftest======\r\n");

    // Flash检测结果
    if (info->flash_info.status == SYS_CHECK_OK) {
        my_printf(huart1, "flash............ok\r\n");
    } else {
        my_printf(huart1, "flash............error\r\n");
    }

    // TF卡检测结果
    if (info->tf_card_info.status == SYS_CHECK_OK) {
        my_printf(huart1, "TF card............ok\r\n");
    } else {
        my_printf(huart1, "TF card...........error\r\n");
    }

    // 显示Flash ID
    my_printf(huart1, "flash ID: 0x%06X\r\n", info->flash_info.flash_id);

    // TF卡容量信息或错误信息
    if (info->tf_card_info.status == SYS_CHECK_OK) {
        // 按照KB单位进行显示
        uint32_t capacity_kb = info->tf_card_info.capacity_mb * 1024;
        my_printf(huart1, "TF card memory:%dKB\r\n", capacity_kb);
    } else {
        my_printf(huart1, "can not find TF card\r\n");
    }

    // RTC时间
    my_printf(huart1, "RTC:");
    rtc_print_current_time();

    // 结束标识
    my_printf(huart1, "======system selftest======\r\n");
}
