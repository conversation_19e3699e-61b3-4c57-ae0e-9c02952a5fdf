#ifndef DATA_PROCESSING_H
#define DATA_PROCESSING_H

#include "sys_common.h"

// 数据处理状态
typedef enum {
    DATA_PROC_OK = 0,
    DATA_PROC_ERROR,
    DATA_PROC_INVALID_DATA,
    DATA_PROC_BUFFER_TOO_SMALL
} data_proc_status_t;

// 隐藏数据结构 (8字节总长度)
typedef struct {
    uint32_t unix_timestamp;    // 4字节Unix时间戳
    uint16_t voltage_integer;   // 2字节电压整数部分
    uint16_t voltage_decimal;   // 2字节电压小数部分
} hide_data_t;

// 函数声明
sys_func_status_t data_proc_convert_to_hide(const datetime_t* time, float voltage, hide_data_t* hide_data);
sys_func_status_t data_proc_convert_from_hide(const hide_data_t* hide_data, datetime_t* time, float* voltage);
sys_func_status_t data_proc_datetime_to_unix(const datetime_t* time, uint32_t* unix_timestamp);
sys_func_status_t data_proc_unix_to_datetime(uint32_t unix_timestamp, datetime_t* time);
sys_func_status_t data_proc_voltage_to_hex(float voltage, uint16_t* integer_part, uint16_t* decimal_part);
sys_func_status_t data_proc_hex_to_voltage(uint16_t integer_part, uint16_t decimal_part, float* voltage);

// 输出函数
void data_proc_print_hide_format(const hide_data_t* hide_data, bool is_overlimit);
void data_proc_print_hide_hex_string(const hide_data_t* hide_data, char* hex_string, size_t buffer_size);

// 命令处理函数
void data_proc_handle_hide_command(void);
void data_proc_handle_unhide_command(void);

#endif // DATA_PROCESSING_H
