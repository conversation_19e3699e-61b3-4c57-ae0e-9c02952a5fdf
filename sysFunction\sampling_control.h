#ifndef SAMPLING_CONTROL_H
#define SAMPLING_CONTROL_H

#include "sys_common.h"

// 采样控制状态
typedef enum {
    SAMPLING_CTRL_OK = 0,
    SAMPLING_CTRL_ERROR,
    SAMPLING_CTRL_INVALID_CYCLE,
    SAMPLING_CTRL_BUSY
} sampling_ctrl_status_t;

// 函数声明
sys_func_status_t sampling_ctrl_start(void);
sys_func_status_t sampling_ctrl_stop(void);
sys_func_status_t sampling_ctrl_set_cycle(sampling_cycle_t cycle);
sampling_cycle_t sampling_ctrl_get_cycle(void);
sampling_state_t sampling_ctrl_get_state(void);
bool sampling_ctrl_check_overlimit(float voltage, float limit);
float sampling_ctrl_get_voltage(void);

// 命令处理函数
void sampling_ctrl_handle_start_command(void);
void sampling_ctrl_handle_stop_command(void);
void sampling_ctrl_handle_cycle_adjust(sampling_cycle_t cycle);

// 任务函数
void sampling_ctrl_task(void);
void sampling_ctrl_output_data(float voltage, bool is_overlimit, float limit);

#endif // SAMPLING_CONTROL_H
