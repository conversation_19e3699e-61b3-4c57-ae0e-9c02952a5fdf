#include "rtc_config.h"

// 需要包含的外部接口
extern int my_printf(void* huart, const char *format, ...);
extern void* huart1;
extern void* hrtc;

// HAL库函数声明
extern int HAL_RTC_GetTime(void* hrtc, void* sTime, uint32_t Format);
extern int HAL_RTC_GetDate(void* hrtc, void* sDate, uint32_t Format);
extern int HAL_RTC_SetTime(void* hrtc, void* sTime, uint32_t Format);
extern int HAL_RTC_SetDate(void* hrtc, void* sDate, uint32_t Format);

// HAL常量定义
#define RTC_FORMAT_BIN 0x00000000U
#define HAL_OK 0x00U
#define HAL_ERROR 0x01U

// RTC结构体定义（基于STM32 HAL）
typedef struct {
    uint8_t Hours;
    uint8_t Minutes;
    uint8_t Seconds;
    uint8_t DayLightSaving;
    uint8_t StoreOperation;
} RTC_TimeTypeDef;

typedef struct {
    uint8_t WeekDay;
    uint8_t Month;
    uint8_t Date;
    uint8_t Year;
} RTC_DateTypeDef;

// HAL常量
#define RTC_DAYLIGHTSAVING_NONE 0x00000000U
#define RTC_STOREOPERATION_RESET 0x00000000U
#define RTC_WEEKDAY_MONDAY 0x01U

// 外部数据存储接口
extern int data_storage_write_log(const char* log_message);

/**
 * @brief 解析时间字符串
 * @param time_string 时间字符串
 * @param time 时间结构体指针
 * @return sys_func_status_t 解析状态
 */
sys_func_status_t rtc_config_parse_time_string(const char* time_string, datetime_t* time)
{
    if (time_string == NULL || time == NULL) {
        return SYS_FUNC_INVALID_PARAM;
    }

    int year, month, day, hour, minute, second;
    int parsed = 0;

    // 尝试解析中文格式: "2025年01月01日12:00:30"
    parsed = sscanf(time_string, "%d年%d月%d日%d:%d:%d", &year, &month, &day, &hour, &minute, &second);

    // 如果中文格式解析失败，尝试标准格式: "2025-01-01 12:00:30"
    if (parsed != 6) {
        parsed = sscanf(time_string, "%d-%d-%d %d:%d:%d", &year, &month, &day, &hour, &minute, &second);
    }

    // 如果标准格式解析失败，尝试连接格式: "2025-01-01 01-30-10"
    if (parsed != 6) {
        parsed = sscanf(time_string, "%d-%d-%d %d-%d-%d", &year, &month, &day, &hour, &minute, &second);
    }

    // 如果所有格式都解析失败
    if (parsed != 6) {
        return SYS_FUNC_INVALID_PARAM;
    }

    // 参数验证
    if (year < 2000 || year > 2099 || month < 1 || month > 12 ||
        day < 1 || day > 31 || hour > 23 || minute > 59 || second > 59) {
        return SYS_FUNC_INVALID_PARAM;
    }

    // 填充时间结构体
    time->year = year;
    time->month = month;
    time->day = day;
    time->hour = hour;
    time->minute = minute;
    time->second = second;

    return SYS_FUNC_OK;
}

/**
 * @brief 设置RTC时间
 * @param time_string 时间字符串
 * @return sys_func_status_t 设置状态
 */
sys_func_status_t rtc_config_set_time(const char* time_string)
{
    if (time_string == NULL) {
        return SYS_FUNC_INVALID_PARAM;
    }

    datetime_t time;
    RTC_TimeTypeDef sTime = {0};
    RTC_DateTypeDef sDate = {0};

    // 解析时间字符串
    if (rtc_config_parse_time_string(time_string, &time) != SYS_FUNC_OK) {
        return SYS_FUNC_INVALID_PARAM;
    }

    // 填充HAL时间结构体
    sTime.Hours = time.hour;
    sTime.Minutes = time.minute;
    sTime.Seconds = time.second;
    sTime.DayLightSaving = RTC_DAYLIGHTSAVING_NONE;
    sTime.StoreOperation = RTC_STOREOPERATION_RESET;

    // 填充HAL日期结构体 (年份需要转换为2位数)
    sDate.Year = time.year - 2000; // 2025 -> 25
    sDate.Month = time.month;
    sDate.Date = time.day;
    sDate.WeekDay = RTC_WEEKDAY_MONDAY; // 默认设置为周一，实际应该有计算

    // 设置RTC时间
    if (HAL_RTC_SetTime(hrtc, &sTime, RTC_FORMAT_BIN) != HAL_OK) {
        return SYS_FUNC_ERROR;
    }

    // 设置RTC日期
    if (HAL_RTC_SetDate(hrtc, &sDate, RTC_FORMAT_BIN) != HAL_OK) {
        return SYS_FUNC_ERROR;
    }

    return SYS_FUNC_OK;
}

/**
 * @brief 获取当前RTC时间
 * @param current_time 时间结构体指针
 * @return sys_func_status_t 获取状态
 */
sys_func_status_t rtc_config_get_current_time(datetime_t* current_time)
{
    if (current_time == NULL) {
        return SYS_FUNC_INVALID_PARAM;
    }

    RTC_TimeTypeDef hal_time = {0};
    RTC_DateTypeDef hal_date = {0};

    // 获取当前时间和日期
    if (HAL_RTC_GetTime(hrtc, &hal_time, RTC_FORMAT_BIN) != HAL_OK) {
        return SYS_FUNC_ERROR;
    }
    if (HAL_RTC_GetDate(hrtc, &hal_date, RTC_FORMAT_BIN) != HAL_OK) {
        return SYS_FUNC_ERROR;
    }

    // 转换为通用时间结构体
    current_time->year = hal_date.Year + 2000; // 转换为4位年份
    current_time->month = hal_date.Month;
    current_time->day = hal_date.Date;
    current_time->hour = hal_time.Hours;
    current_time->minute = hal_time.Minutes;
    current_time->second = hal_time.Seconds;

    return SYS_FUNC_OK;
}

/**
 * @brief 获取当前RTC时间字符串
 * @param time_buffer 时间字符串缓冲区
 * @param buffer_size 缓冲区大小
 * @return sys_func_status_t 获取状态
 */
sys_func_status_t rtc_config_get_current_time_string(char* time_buffer, size_t buffer_size)
{
    if (time_buffer == NULL || buffer_size < 20) { // 至少需要20字符: "2025-01-01 12:00:30"
        return SYS_FUNC_INVALID_PARAM;
    }

    datetime_t current_time;

    // 获取当前时间
    if (rtc_config_get_current_time(&current_time) != SYS_FUNC_OK) {
        return SYS_FUNC_ERROR;
    }

    // 格式化时间字符串
    snprintf(time_buffer, buffer_size, "%04d-%02d-%02d %02d:%02d:%02d",
             current_time.year,
             current_time.month,
             current_time.day,
             current_time.hour,
             current_time.minute,
             current_time.second);

    return SYS_FUNC_OK;
}

/**
 * @brief 打印当前RTC时间
 */
void rtc_config_print_current_time(void)
{
    RTC_TimeTypeDef current_time = {0};
    RTC_DateTypeDef current_date = {0};

    // 获取当前时间和日期
    HAL_RTC_GetTime(hrtc, &current_time, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(hrtc, &current_date, RTC_FORMAT_BIN);

    // 格式化输出时间 "2025-01-01 12:00:30"
    my_printf(huart1, "%04d-%02d-%02d %02d:%02d:%02d\r\n",
              current_date.Year + 2000, // 转换为4位年份
              current_date.Month,
              current_date.Date,
              current_time.Hours,
              current_time.Minutes,
              current_time.Seconds);
}

/**
 * @brief 打印RTC设置成功信息
 */
void rtc_config_print_set_success(void)
{
    my_printf(huart1, "RTC Config success\r\n");

    // 获取设置的时间并显示标准格式
    my_printf(huart1, "Time: ");
    rtc_config_print_current_time();
}

/**
 * @brief 打印RTC设置失败信息
 */
void rtc_config_print_set_failed(void)
{
    my_printf(huart1, "RTC Config failed\r\n");
    my_printf(huart1, "Invalid time format. Please use: 2025-01-01 15:00:10 or 2025年01月01日12:00:30\r\n");
}

/**
 * @brief 处理RTC Config命令 - 显示用户输入时间并等待输入
 */
void rtc_config_handle_config_command(void)
{
    // 记录RTC Config命令日志
    data_storage_write_log("RTC Config command");

    // 提示用户输入时间
    my_printf(huart1, "Input Datetime\r\n");
}

/**
 * @brief 处理RTC now命令 - 显示当前时间
 */
void rtc_config_handle_now_command(void)
{
    my_printf(huart1, "Current Time:");
    rtc_config_print_current_time();
}

/**
 * @brief 处理RTC时间输入 - 设置时间并显示结果
 * @param input 用户输入的时间字符串
 */
void rtc_config_handle_time_input(const char* input)
{
    if (input == NULL) {
        rtc_config_print_set_failed();
        return;
    }

    // 设置RTC时间
    sys_func_status_t status = rtc_config_set_time(input);
    if (status == SYS_FUNC_OK) {
        rtc_config_print_set_success();

        // 记录RTC设置成功日志
        char log_msg[128];
        snprintf(log_msg, sizeof(log_msg), "RTC config success to %s", input);
        data_storage_write_log(log_msg);
    } else {
        rtc_config_print_set_failed();
    }
}
