#include "led_app.h"
#include "gpio.h"
#include "math.h"
#include "sampling_control.h"

uint8_t ucLed[6] = {1, 0, 1, 0, 1, 0};

void led_disp(uint8_t *ucLed)
{
    uint8_t temp = 0x00;
    static uint8_t temp_old = 0xff;

    for (int i = 0; i < 6; i++)
    {
        if (ucLed[i])
            temp |= (1 << i);
    }

    if (temp_old != temp)
    {
        HAL_GPIO_WritePin(GPIOD, GPIO_PIN_8, (temp & 0x01) ? GPIO_PIN_SET : GPIO_PIN_RESET);  // LED 0
        HAL_GPIO_WritePin(GPIOD, GPIO_PIN_9, (temp & 0x02) ? GPIO_PIN_SET : GPIO_PIN_RESET);  // LED 1
        HAL_GPIO_WritePin(GPIOD, GPIO_PIN_10, (temp & 0x04) ? GPIO_PIN_SET : GPIO_PIN_RESET); // LED 2
        HAL_GPIO_WritePin(GPIOD, GPIO_PIN_11, (temp & 0x08) ? GPIO_PIN_SET : GPIO_PIN_RESET); // LED 3
        HAL_GPIO_WritePin(GPIOD, GPIO_PIN_12, (temp & 0x10) ? GPIO_PIN_SET : GPIO_PIN_RESET); // LED 4
        HAL_GPIO_WritePin(GPIOD, GPIO_PIN_13, (temp & 0x20) ? GPIO_PIN_SET : GPIO_PIN_RESET); // LED 5

        temp_old = temp;
    }
}

void led_task(void)
{
    static uint32_t next_blink_time = 0; // LED1下次闪烁的预期时间
    static uint8_t led1_blink_state = 0; // LED1闪烁状态
    static uint8_t last_sampling_state = 0; // 上次采样状态，用于检测状态变化

    // LED1采样状态指示逻辑
    uint8_t current_sampling_state = (sampling_get_state() == SAMPLING_ACTIVE) ? 1 : 0;
    
    if (current_sampling_state)
    {
        // 采样状态：1秒周期闪烁
        uint32_t current_time = HAL_GetTick();

        // 状态切换时重新初始化时间基准
        if (last_sampling_state != current_sampling_state) {
            next_blink_time = current_time + 500; // 500ms后第一次切换
            led1_blink_state = 1; // 启动时先点亮
        }

        // 检查是否到达切换时间
        if (current_time >= next_blink_time)
        {
            led1_blink_state ^= 1; // 翻转LED状态
            next_blink_time += 500; // 基于预期时间累加，避免累积误差
            
            // 防止时间溢出导致的问题
            if (next_blink_time < current_time) {
                next_blink_time = current_time + 500;
            }
        }
        ucLed[0] = led1_blink_state;
    }
    else
    {
        // 停止状态：LED熄灭
        ucLed[0] = 0;
        led1_blink_state = 0;
        next_blink_time = 0; // 重置预期时间
    }
    
    // 更新状态记录
    last_sampling_state = current_sampling_state;

    // LED2超限状态指示逻辑
    if (sampling_check_overlimit())
    {
        // 超限时点亮LED2
        ucLed[1] = 1;
    }
    else
    {
        // 正常时熄灭LED2
        ucLed[1] = 0;
    }

    // 更新LED显示
    led_disp(ucLed);
}
