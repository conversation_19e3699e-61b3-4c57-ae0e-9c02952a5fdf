#include "led_app.h"
#include "gpio.h"
#include "math.h"
uint8_t ucLed[6] = {1, 0, 1, 0, 1, 0};

void led_disp(uint8_t *ucLed)
{

    uint8_t temp = 0x00;

    static uint8_t temp_old = 0xff;

    for (int i = 0; i < 6; i++)
    {

        if (ucLed[i])
            temp |= (1 << i);
    }

    if (temp_old != temp)
    {

        HAL_GPIO_WritePin(GPIOD, GPIO_PIN_8, (temp & 0x01) ? GPIO_PIN_SET : GPIO_PIN_RESET);  // LED 0
        HAL_GPIO_WritePin(GPIOD, GPIO_PIN_9, (temp & 0x02) ? GPIO_PIN_SET : GPIO_PIN_RESET);  // LED 1
        HAL_GPIO_WritePin(GPIOD, GPIO_PIN_10, (temp & 0x04) ? GPIO_PIN_SET : GP<PERSON>_PIN_RESET); // LED 2
        HAL_GPIO_WritePin(GPIOD, GPIO_PIN_11, (temp & 0x08) ? GPIO_PIN_SET : GPIO_PIN_RESET); // LED 3
        HAL_GPIO_WritePin(GPIOD, GPIO_PIN_12, (temp & 0x10) ? GPIO_PIN_SET : GPIO_PIN_RESET); // LED 4
        HAL_GPIO_WritePin(GPIOD, GPIO_PIN_13, (temp & 0x20) ? GPIO_PIN_SET : GPIO_PIN_RESET); // LED 5

        temp_old = temp;
    }
}

void led_task(void)
{
    static uint32_t next_blink_time = 0; // LED1下次闪烁的预期时间
    static uint8_t led1_blink_state = 0; // LED1��˸״̬

    // LED1����״ָ̬ʾ�߼�
    if (sampling_get_state() == SAMPLING_ACTIVE)
    {
        // ����״̬��1s������˸
        uint32_t current_time = HAL_GetTick();

        // 初始化预期时间（首次进入采样状态或状态切换时）
        if (next_blink_time == 0) {
            next_blink_time = current_time + 500; // 500ms闪烁周期，实现1秒完整周期
        }

        if (current_time >= next_blink_time)
        {                          // 500ms切换一次，实现1秒完整闪烁周期
            led1_blink_state ^= 1; // 翻转LED状态
            next_blink_time += 500; // 基于预期时间累加，避免累积误差
        }
        ucLed[0] = led1_blink_state;
    }
    else
    {
        // ֹͣ״̬������
        ucLed[0] = 0;
        led1_blink_state = 0;
        next_blink_time = 0; // 重置预期时间，确保状态切换时正确初始化
    }

    // LED2����״ָ̬ʾ�߼�
    if (sampling_check_overlimit())
    {
        // ����ʱ������LED2
        ucLed[1] = 1;
    }
    else
    {
        // ����ʱ��Ϩ��LED2
        ucLed[1] = 0;
    }

    // ����LED��ʾ
    led_disp(ucLed);
}
