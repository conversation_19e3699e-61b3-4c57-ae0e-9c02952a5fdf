// ???????btn_app.c
// ?????????????????????????????????????????????
// ????????????????
// ?????Copyright (c) 2024 ???????????. All rights reserved.

#include "btn_app.h"
#include "ebtn.h" // 按键库
#include "gpio.h" // GPIO控制
#include "sampling_control.h" // 采样控制系统
#include "usart_app.h" // 串口应用
#include "data_storage.h" // 数据存储

extern uint8_t ucLed[6]; // ??LED????

typedef enum // ??????????
{
    USER_BUTTON_0 = 0, // ????0
    USER_BUTTON_1,     // ????1(????????/??)
    USER_BUTTON_2,     // ????2(5s????)
    USER_BUTTON_3,     // ????3(10s????)
    USER_BUTTON_4,     // ????4(15s????)
    USER_BUTTON_5,     // ????5
    USER_BUTTON_MAX,   // ????????

    //    USER_BUTTON_COMBO_0 = 0x100, // ??????(???)
    //    USER_BUTTON_COMBO_1,
    //    USER_BUTTON_COMBO_2,
    //    USER_BUTTON_COMBO_3,
    //    USER_BUTTON_COMBO_MAX,
} user_button_t;

// ????????????????????20ms?????????0ms????��??????20ms?????????1000ms????????????????????1000ms?????????10??
static const ebtn_btn_param_t defaul_ebtn_param = EBTN_PARAMS_INIT(20, 0, 20, 1000, 0, 1000, 10);

static ebtn_btn_t btns[] = { // ????????????
    EBTN_BUTTON_INIT(USER_BUTTON_0, &defaul_ebtn_param), // ????0
    EBTN_BUTTON_INIT(USER_BUTTON_1, &defaul_ebtn_param), // ????1
    EBTN_BUTTON_INIT(USER_BUTTON_2, &defaul_ebtn_param), // ????2
    EBTN_BUTTON_INIT(USER_BUTTON_3, &defaul_ebtn_param), // ????3
    EBTN_BUTTON_INIT(USER_BUTTON_4, &defaul_ebtn_param), // ????4
    EBTN_BUTTON_INIT(USER_BUTTON_5, &defaul_ebtn_param), // ????5
};

// static ebtn_btn_combo_t btns_combo[] = {
//     EBTN_BUTTON_COMBO_INIT_RAW(USER_BUTTON_COMBO_0, &defaul_ebtn_param, EBTN_EVT_MASK_ONCLICK),
//     EBTN_BUTTON_COMBO_INIT_RAW(USER_BUTTON_COMBO_1, &defaul_ebtn_param, EBTN_EVT_MASK_ONCLICK),
// };

uint8_t prv_btn_get_state(struct ebtn_btn *btn) // ????????? ????:??????????? ????:??????(1=????,0=???)
{
    switch (btn->key_id) // ???????ID???GPIO??
    {
    case USER_BUTTON_0:
        return !HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_15); // ????0(PE15)????????��
    case USER_BUTTON_1:
        return !HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_13); // ????1(PE13)????????��
    case USER_BUTTON_2:
        return !HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_11); // ????2(PE11)????????��
    case USER_BUTTON_3:
        return !HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_9);  // ????3(PE9)????????��
    case USER_BUTTON_4:
        return !HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_7);  // ????4(PE7)????????��
    case USER_BUTTON_5:
        return !HAL_GPIO_ReadPin(GPIOB, GPIO_PIN_0);  // ????5(PB0)????????��
    default:
        return 0; // ��?????????0
    }
}

void prv_btn_event(struct ebtn_btn *btn, ebtn_evt_t evt) // ??????????????? ????:???????????,??????? ????:??
{
    if (evt == EBTN_EVT_ONPRESS) // ???????????????
    {
        switch (btn->key_id) // ???????ID???????????
        {
        case USER_BUTTON_0:
            WOUOUI_MSG_QUE_SEND(msg_up); // ???????????
            break;
        case USER_BUTTON_1:
            WOUOUI_MSG_QUE_SEND(msg_down);

            // 采样启动/停止切换 (KEY1) - 无论在什么状态下都可以正常启动或关闭
            // 确保采样系统已初始化
            sampling_init();

            // 获取当前采样状态
            sampling_state_t current_state = sampling_get_state();

            if (current_state == SAMPLING_IDLE)
            {
                // 当前是空闲状态，启动采样系统
                if (sampling_start() == SAMPLING_OK)
                {
                    sampling_cycle_t cycle = sampling_get_cycle();
                    my_printf(&huart1, "Periodic Sampling\r\n");
                    my_printf(&huart1, "sample cycle: %ds\r\n", (int)cycle);

                    // 启用采样输出功能
                    extern uint8_t g_sampling_output_enabled;
                    extern uint32_t g_last_output_time;
                    g_sampling_output_enabled = 1;
                    g_last_output_time = HAL_GetTick();

                    // 记录启动日志
                    char log_msg[64];
                    sprintf(log_msg, "sample start - cycle %ds (key1)", (int)cycle);
                    data_storage_write_log(log_msg);
                }
                else
                {
                    my_printf(&huart1, "sampling start failed.\r\n");
                }
            }
            else
            {
                // ֹͣ����ϵͳ
                if (sampling_stop() == SAMPLING_OK)
                {
                    my_printf(&huart1, "Periodic Sampling STOP\r\n");

                    // 禁用采样输出功能
                    extern uint8_t g_sampling_output_enabled;
                    g_sampling_output_enabled = 0;

                    // ��¼ֹͣ��־
                    data_storage_write_log("sample stop (key1)");
                }
                else
                {
                    my_printf(&huart1, "sampling stop failed.\r\n");
                }
            }
            break;
        case USER_BUTTON_2:
            WOUOUI_MSG_QUE_SEND(msg_left);

            // 设置5s采样周期 (KEY2) - 停止→修改周期→重新启动→发送串口提示
            sampling_init();

            // 记住当前采样状态
            sampling_state_t was_sampling_key2 = sampling_get_state();

            // 如果正在采样，先停止
            if (was_sampling_key2 == SAMPLING_ACTIVE) {
                sampling_stop();
                extern uint8_t g_sampling_output_enabled;
                g_sampling_output_enabled = 0;
            }

            // 修改采样周期
            if (sampling_set_cycle(CYCLE_5S) == SAMPLING_OK)
            {
                my_printf(&huart1, "sample cycle adjust: 5s\r\n");

                // 如果之前在采样，重新启动采样
                if (was_sampling_key2 == SAMPLING_ACTIVE) {
                    if (sampling_start() == SAMPLING_OK) {
                        my_printf(&huart1, "Periodic Sampling\r\n");
                        my_printf(&huart1, "sample cycle: 5s\r\n");

                        // 重新启用采样输出
                        extern uint8_t g_sampling_output_enabled;
                        extern uint32_t g_last_output_time;
                        g_sampling_output_enabled = 1;
                        g_last_output_time = HAL_GetTick();
                    }
                }

                // 记录周期调整日志
                data_storage_write_log("cycle adjust to 5s (key2)");
            }
            else
            {
                my_printf(&huart1, "cycle adjust failed.\r\n");
            }
            break;
        case USER_BUTTON_3:
            WOUOUI_MSG_QUE_SEND(msg_right);

            // 设置10s采样周期 (KEY3) - 停止→修改周期→重新启动→发送串口提示
            sampling_init();

            // 记住当前采样状态
            sampling_state_t was_sampling_key3 = sampling_get_state();

            // 如果正在采样，先停止
            if (was_sampling_key3 == SAMPLING_ACTIVE) {
                sampling_stop();
                extern uint8_t g_sampling_output_enabled;
                g_sampling_output_enabled = 0;
            }

            // 修改采样周期
            if (sampling_set_cycle(CYCLE_10S) == SAMPLING_OK)
            {
                my_printf(&huart1, "sample cycle adjust: 10s\r\n");

                // 如果之前在采样，重新启动采样
                if (was_sampling_key3 == SAMPLING_ACTIVE) {
                    if (sampling_start() == SAMPLING_OK) {
                        my_printf(&huart1, "Periodic Sampling\r\n");
                        my_printf(&huart1, "sample cycle: 10s\r\n");

                        // 重新启用采样输出
                        extern uint8_t g_sampling_output_enabled;
                        extern uint32_t g_last_output_time;
                        g_sampling_output_enabled = 1;
                        g_last_output_time = HAL_GetTick();
                    }
                }

                // 记录周期调整日志
                data_storage_write_log("cycle adjust to 10s (key3)");
            }
            else
            {
                my_printf(&huart1, "cycle adjust failed.\r\n");
            }
            break;
        case USER_BUTTON_4:
            WOUOUI_MSG_QUE_SEND(msg_return);

            // 设置15s采样周期 (KEY4) - 停止→修改周期→重新启动→发送串口提示
            sampling_init();

            // 记住当前采样状态
            sampling_state_t was_sampling_key4 = sampling_get_state();

            // 如果正在采样，先停止
            if (was_sampling_key4 == SAMPLING_ACTIVE) {
                sampling_stop();
                extern uint8_t g_sampling_output_enabled;
                g_sampling_output_enabled = 0;
            }

            // 修改采样周期
            if (sampling_set_cycle(CYCLE_15S) == SAMPLING_OK)
            {
                my_printf(&huart1, "sample cycle adjust: 15s\r\n");

                // 如果之前在采样，重新启动采样
                if (was_sampling_key4 == SAMPLING_ACTIVE) {
                    if (sampling_start() == SAMPLING_OK) {
                        my_printf(&huart1, "Periodic Sampling\r\n");
                        my_printf(&huart1, "sample cycle: 15s\r\n");

                        // 重新启用采样输出
                        extern uint8_t g_sampling_output_enabled;
                        extern uint32_t g_last_output_time;
                        g_sampling_output_enabled = 1;
                        g_last_output_time = HAL_GetTick();
                    }
                }

                // 记录周期调整日志
                data_storage_write_log("cycle adjust to 15s (key4)");
            }
            else
            {
                my_printf(&huart1, "cycle adjust failed.\r\n");
            }
            break;
        case USER_BUTTON_5:
            WOUOUI_MSG_QUE_SEND(msg_click);
            break;
        default:
            break;
        }
    }
}

void app_btn_init(void)
{
    // ebtn_init(btns, EBTN_ARRAY_SIZE(btns), btns_combo, EBTN_ARRAY_SIZE(btns_combo), prv_btn_get_state, prv_btn_event);
    ebtn_init(btns, EBTN_ARRAY_SIZE(btns), NULL, 0, prv_btn_get_state, prv_btn_event);

    //    ebtn_combo_btn_add_btn(&btns_combo[0], USER_BUTTON_0);
    //    ebtn_combo_btn_add_btn(&btns_combo[0], USER_BUTTON_1);

    //    ebtn_combo_btn_add_btn(&btns_combo[1], USER_BUTTON_2);
    //    ebtn_combo_btn_add_btn(&btns_combo[1], USER_BUTTON_3);

    HAL_TIM_Base_Start_IT(&htim14);
}

void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{
}

void btn_task(void)
{
    ebtn_process(uwTick);
}
