// �ļ�����config_manager.h
// ���ܣ����ù���ϵͳͷ�ļ����ṩ�����洢��Flash�־û��ӿ�
// ���ߣ��״׵��ӹ�����
// ��Ȩ��Copyright (c) 2024 �״׵��ӹ�����. All rights reserved.

#ifndef __CONFIG_MANAGER_H__
#define __CONFIG_MANAGER_H__

#include "stdint.h"
#include "sampling_control.h" // ��������ģ��

#define CONFIG_FLASH_ADDR 0x1F0000 // Flash�洢��ַ
#define CONFIG_MAGIC 0x43464721     // ����ħ����ʶ
#define CONFIG_VERSION 0x02         // ���ð汾��

// 配置参数结构体
#ifndef CONFIG_PARAMS_T_DEFINED
#define CONFIG_PARAMS_T_DEFINED
typedef struct
{
    uint32_t magic;              // 魔数标识
    uint8_t version;             // 版本号
    float ratio;                 // 变比参数
    float limit;                 // 阈值参数
    sampling_cycle_t cycle;      // 采样周期参数(5s/10s/15s)
    uint32_t crc32;              // CRC32校验值
} config_params_t;
#endif

typedef enum // 配置操作状态枚举
{
    CONFIG_OK = 0,           // 操作成功
    CONFIG_ERROR = 1,        // 操作失败
    CONFIG_INVALID = 2,      // 参数无效
    CONFIG_FLASH_ERROR = 3,  // Flash读写错误
    CONFIG_CRC_ERROR = 4     // CRC校验错误
} config_status_t;


// 配置管理核心接口函数
config_status_t config_init(void);                                      // 初始化配置系统 参数:无 返回:操作状态
config_status_t config_get_params(config_params_t *params);             // 获取配置参数 参数:配置结构体指针 返回:操作状态
config_status_t config_set_params(const config_params_t *params);       // 设置配置参数 参数:配置结构体指针 返回:操作状态
config_status_t config_save_to_flash(void);                             // 保存配置到Flash 参数:无 返回:操作状态
config_status_t config_load_from_flash(void);                           // 从Flash加载配置 参数:无 返回:操作状态
config_status_t config_reset_to_default(void);                          // 重置为默认配置 参数:无 返回:操作状态

// 参数验证函数
config_status_t config_validate_ratio(float ratio);                     // 验证变比参数 参数:变比值 返回:验证结果
config_status_t config_validate_limit(float limit);                     // 验证阈值参数 参数:阈值 返回:验证结果
config_status_t config_validate_sampling_cycle(sampling_cycle_t cycle); // 验证采样周期参数 参数:周期值 返回:验证结果

// 采样周期管理函数
config_status_t config_set_sampling_cycle(sampling_cycle_t cycle);      // 设置采样周期 参数:周期值 返回:操作状态
sampling_cycle_t config_get_sampling_cycle(void);                       // 获取采样周期 参数:无 返回:当前周期

// 工具函数
uint32_t config_calculate_crc32(const config_params_t *params);         // 计算CRC32校验值 参数:配置结构体指针 返回:校验值

#endif // __CONFIG_MANAGER_H__
