// �ļ�����sampling_control.c
// ���ܣ���������ϵͳʵ�֣��ṩ���ݲɼ����ƺ�״̬��������
// ���ߣ��״׵��ӹ�����
// ��Ȩ��Copyright (c) 2024 �״׵��ӹ�����. All rights reserved.

#include "sampling_control.h"
#include "stddef.h"
#include "config_manager.h" // ���ù���ϵͳ
#include "adc_app.h"        // ADCӦ��ģ��

static sampling_control_t g_sampling_control = {0}; // ȫ�ֲ�������ʵ��
static uint8_t g_sampling_initialized = 0;          // ��ʼ����־

#define LED_BLINK_PERIOD_MS 500 // LED闪烁周期定义(0.5秒=500ms，实现1秒完整闪烁周期)

sampling_status_t sampling_init(void) // ��ʼ����������ϵͳ ����:�� ����:����״̬
{
    if (g_sampling_initialized) // ����Ƿ��ѳ�ʼ��
    {
        return SAMPLING_OK;
    }

    config_init(); // ��ʼ�����ù���ϵͳ

    g_sampling_control.state = SAMPLING_IDLE;                       // ���ó�ʼ״̬Ϊ����
    g_sampling_control.cycle = config_get_sampling_cycle();         // ������ϵͳ��������
    g_sampling_control.last_sample_time = 0;                        // �����ϴβ���ʱ��
    g_sampling_control.led_blink_time = 0;                          // ����LED��˸ʱ��
    g_sampling_control.led_blink_state = 0;                         // ����LED��˸״̬

    g_sampling_initialized = 1; // ���ó�ʼ����־
    return SAMPLING_OK;
}

sampling_status_t sampling_start(void) // �������� ����:�� ����:����״̬
{
    if (!g_sampling_initialized) // ����ʼ��״̬
        return SAMPLING_ERROR;

    g_sampling_control.state = SAMPLING_ACTIVE;                     // ����Ϊ����״̬
    g_sampling_control.last_sample_time = HAL_GetTick();            // ��¼����ʱ��
    g_sampling_control.led_blink_time = HAL_GetTick();              // ��¼LED��˸ʱ��
    g_sampling_control.led_blink_state = 0;                         // ��ʼ��LED״̬

    return SAMPLING_OK;
}

sampling_status_t sampling_stop(void) // ֹͣ���� ����:�� ����:����״̬
{
    if (!g_sampling_initialized) // ����ʼ��״̬
        return SAMPLING_ERROR;

    g_sampling_control.state = SAMPLING_IDLE;       // ����Ϊ����״̬
    g_sampling_control.led_blink_state = 0;         // LEDϨ��

    return SAMPLING_OK;
}

sampling_status_t sampling_set_cycle(sampling_cycle_t cycle) // ���ò������� ����:����ֵ ����:����״̬
{
    if (!g_sampling_initialized) // ����ʼ��״̬
        return SAMPLING_ERROR;

    if (cycle != CYCLE_5S && cycle != CYCLE_10S && cycle != CYCLE_15S) // ��֤���ڲ���
    {
        return SAMPLING_INVALID;
    }

    g_sampling_control.cycle = cycle; // ���±�����������

    if (config_set_sampling_cycle(cycle) == CONFIG_OK) // ���浽���ù���ϵͳ��д��Flash
    {
        config_save_to_flash();
    }

    return SAMPLING_OK;
}

sampling_state_t sampling_get_state(void) // ��ȡ����״̬ ����:�� ����:��ǰ״̬
{
    if (!g_sampling_initialized) // ����ʼ��״̬
        return SAMPLING_IDLE;
    return g_sampling_control.state;
}

sampling_cycle_t sampling_get_cycle(void) // ��ȡ�������� ����:�� ����:��ǰ����
{
    if (!g_sampling_initialized) // ����ʼ��״̬
        return CYCLE_5S;
    return g_sampling_control.cycle;
}

uint8_t sampling_should_sample(void) // ����Ƿ�Ӧ�ò��� ����:�� ����:������־
{
    if (!g_sampling_initialized || g_sampling_control.state != SAMPLING_ACTIVE) // ����ʼ����״̬
    {
        return 0;
    }

    uint32_t current_time = HAL_GetTick();                                       // ��ȡ��ǰʱ��
    uint32_t elapsed_time = current_time - g_sampling_control.last_sample_time; // ����ʱ����
    uint32_t cycle_ms = g_sampling_control.cycle * 1000;                        // ת��Ϊ����

    return (elapsed_time >= cycle_ms) ? 1 : 0; // �ж��Ƿ񵽴����ʱ��
}

void sampling_update_led_blink(void) // ����LED��˸״̬ ����:�� ����:��
{
    if (!g_sampling_initialized || g_sampling_control.state != SAMPLING_ACTIVE) // ����ʼ����״̬
    {
        g_sampling_control.led_blink_state = 0; // ����ֹͣʱLEDϨ��
        return;
    }

    uint32_t current_time = HAL_GetTick();                                      // ��ȡ��ǰʱ��
    uint32_t elapsed_time = current_time - g_sampling_control.led_blink_time;  // ������˸���

    if (elapsed_time >= LED_BLINK_PERIOD_MS) // ����Ƿ񵽴���˸����
    {
        g_sampling_control.led_blink_state ^= 1;            // ��תLED״̬
        g_sampling_control.led_blink_time = current_time;   // ������˸ʱ��
    }
}

float sampling_get_voltage(void) // ��ȡ��ǰ��ѹֵ ����:�� ����:��ѹֵ
{
    extern __IO float voltage;     // ����adc_app.c��ȫ�ֵ�ѹ����
    config_params_t config_params; // ���ò����ṹ��

    if (config_get_params(&config_params) != CONFIG_OK) // ��ȡ���ò���
    {
        return voltage; // ���û�ȡʧ��ʱ����ԭʼ��ѹֵ
    }

    return voltage * config_params.ratio; // ʹ��ratio�������е�ѹ����
}

uint8_t sampling_check_overlimit(void) // ����Ƿ��� ����:�� ����:���ޱ�־
{
    config_params_t config_params; // ���ò����ṹ��

    if (config_get_params(&config_params) != CONFIG_OK) // ��ȡ���ò���
    {
        return 0; // ���û�ȡʧ��ʱ��Ϊδ����
    }

    float current_voltage = sampling_get_voltage(); // ��ȡ��ǰ��ѹֵ

    return (current_voltage > config_params.limit) ? 1 : 0; // ����Ƿ񳬹�limit��ֵ
}

void sampling_task(void) // ���������� ����:�� ����:��
{
    if (!g_sampling_initialized) // ����ʼ��״̬
        return;

    sampling_update_led_blink(); // ����LED��˸״̬

    if (g_sampling_control.state == SAMPLING_ACTIVE) // ������ڲ���״̬������Ƿ���Ҫ���в���
    {
        if (sampling_should_sample()) // ����Ƿ񵽴����ʱ��
        {
            g_sampling_control.last_sample_time = HAL_GetTick(); // ���²���ʱ���

            float current_voltage = sampling_get_voltage();      // ��ȡ��ǰ��ѹֵ
            uint8_t is_overlimit = sampling_check_overlimit();   // ����Ƿ���

            (void)current_voltage; // ����δʹ�ñ�������
            (void)is_overlimit;    // ����δʹ�ñ�������
        }
    }
}

uint8_t sampling_get_led_blink_state(void) // ��ȡLED��˸״̬ ����:�� ����:��˸״̬
{
    if (!g_sampling_initialized) // ����ʼ��״̬
        return 0;
    return g_sampling_control.led_blink_state;
}
