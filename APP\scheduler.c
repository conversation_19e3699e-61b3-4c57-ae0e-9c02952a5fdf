#include "scheduler.h"

// �ļ�����scheduler.c
// ���ܣ����������ʵ�֣�����ʱ��Ƭ��ѯ�Ķ��������ϵͳ
// ���ߣ��״׵��ӹ�����
// ��Ȩ��Copyright (c) 2024 �״׵��ӹ�����. All rights reserved.

uint8_t task_num; // ����������

typedef struct // ����ṹ�嶨��
{
    void (*task_func)(void); // ������ָ��
    uint32_t rate_ms;        // ����ִ������(����)
    uint32_t last_run;       // �ϴ�ִ��ʱ���
} task_t;

// 系统任务列表（按照优先级从高到低排列）
static task_t scheduler_task[] =
    {
        {led_task, 10, 0},     // LED任务：10ms周期（优化LED闪烁精度）
        {adc_task, 100, 0},    // ADC任务：100ms周期
        {btn_task, 2, 0},      // 按键任务：2ms周期（提高按键响应速度）
        {uart_task, 5, 0},     // 串口通信任务：5ms周期
        {oled_task, 20, 0},    // OLED显示任务：20ms周期（降低频率节省资源）
        {sampling_task, 5, 0}  // 采样任务：5ms周期（提高采样时间精度）
};

void scheduler_init(void) // ��ʼ����������� ����:�� ����:��
{
    task_num = sizeof(scheduler_task) / sizeof(task_t); // ������������
}

void scheduler_run(void) // ���������������ѭ�� ����:�� ����:��
{
    for (uint8_t i = 0; i < task_num; i++) // ������������
    {
        uint32_t now_time = HAL_GetTick(); // ��ȡ��ǰϵͳʱ��

        if (now_time >= scheduler_task[i].rate_ms + scheduler_task[i].last_run) // ��������Ƿ񵽴�ִ��ʱ��
        {
            scheduler_task[i].last_run = now_time; // ���������ϴ�ִ��ʱ��
            scheduler_task[i].task_func();         // ִ��������
        }
    }
}
