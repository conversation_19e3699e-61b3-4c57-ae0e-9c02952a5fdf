// �ļ�����usart_app.h
// ���ܣ�����ͨ��Ӧ��ͷ�ļ����ṩ����������û������ӿ�
// ���ߣ��״׵��ӹ�����
// ��Ȩ��Copyright (c) 2024 �״׵��ӹ�����. All rights reserved.

#ifndef __USART_APP_H__
#define __USART_APP_H__

#include "mydefine.h"     // ȫ�ֶ���ͷ�ļ�
#include "data_storage.h" // ���ݴ洢ģ��

int my_printf(UART_HandleTypeDef *huart, const char *format, ...);        // ��ʽ��������� ����:���ھ��,��ʽ�ַ���,�ɱ���� ����:����ַ���
void uart_task(void);                                                     // ���������� ����:�� ����:��
void parse_uart_command(uint8_t *buffer, uint16_t length);                // ������������ ����:�������,���� ����:��

typedef enum // 命令状态枚举
{
    CMD_STATE_IDLE = 0,       // 空闲状态
    CMD_STATE_WAIT_RATIO = 1, // 等待ratio参数输入
    CMD_STATE_WAIT_LIMIT = 2, // 等待limit参数输入
    CMD_STATE_WAIT_RTC = 3    // 等待RTC时间输入
} cmd_state_t;

// �������������
void handle_conf_command(void);             // ����conf���� ����:�� ����:��
void handle_ratio_command(void);            // ����ratio���� ����:�� ����:��
void handle_limit_command(void);            // ����limit���� ����:�� ����:��
void handle_configsave_command(void);       // ����configsave���� ����:�� ����:��
void handle_configread_command(void);       // ����configread���� ����:�� ����:��
void handle_start_command(void);            // ����start����(/)����:�� ����:��
void handle_stop_command(void);             // ����stop����(/)����:�� ����:��
void handle_hide_command(void);             // ����hide���� ����:�� ����:��
void handle_unhide_command(void);           // ����unhide���� ����:�� ����:��
void handle_rtc_config_command(void);       // ����RTC Config���� ����:�� ����:��
void handle_sampling_output(void);          // 命令处理函数声明ʾ ����:�� ����:��
void handle_interactive_input(char *input); // ��������ʽ���� ����:�����ַ��� ����:��

// 工具函数声明(供其他模块使用)
uint32_t convert_rtc_to_unix_timestamp(RTC_TimeTypeDef *time, RTC_DateTypeDef *date);          // Unix时间戳转换 参数:时间结构体,日期结构体 返回:Unix时间戳
void format_hex_output(uint32_t timestamp, float voltage, uint8_t is_overlimit, char *output); // HEX格式输出 参数:时间戳,电压值,超限标志,输出缓冲区 返回:无

// 全局变量声明(供其他模块使用)
extern uint8_t g_sampling_output_enabled;  // 采样输出使能标志
extern uint32_t g_last_output_time;        // 上次输出时间

#endif
