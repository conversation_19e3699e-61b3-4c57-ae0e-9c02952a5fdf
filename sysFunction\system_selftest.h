#ifndef SYSTEM_SELFTEST_H
#define SYSTEM_SELFTEST_H

#include "sys_common.h"

// 系统检测状态 (sysFunction版本)
typedef enum {
    SYS_CHECK_OK = 0,
    SYS_CHECK_ERROR,
    SYS_CHECK_NOT_FOUND
} sys_check_status_t;

// Flash信息结构 (sysFunction版本)
typedef struct {
    uint32_t flash_id;
    char model_name[32];
    uint32_t capacity_mb;
    sys_check_status_t status;
} sys_flash_info_t;

// TF卡信息结构 (sysFunction版本)
typedef struct {
    uint32_t capacity_mb;
    uint32_t sector_size;
    uint32_t sector_count;
    sys_check_status_t status;
} sys_tf_card_info_t;

// 系统信息结构 (sysFunction版本)
typedef struct {
    sys_flash_info_t flash_info;
    sys_tf_card_info_t tf_card_info;
    sys_check_status_t rtc_status;
} sys_system_info_t;

// 系统自检结果结构 (sysFunction版本)
typedef struct {
    sys_system_info_t system_info;
    bool test_passed;
} sys_check_result_t;

// 函数声明
sys_func_status_t system_selftest_execute(sys_check_result_t* result);
void system_selftest_print_result(const sys_check_result_t* result);
sys_func_status_t system_selftest_check_flash(sys_flash_info_t* flash_info);
sys_func_status_t system_selftest_check_tfcard(sys_tf_card_info_t* tf_info);

#endif // SYSTEM_SELFTEST_H
