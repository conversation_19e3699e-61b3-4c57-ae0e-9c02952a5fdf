#ifndef SYSTEM_SELFTEST_H
#define SYSTEM_SELFTEST_H

#include "sys_common.h"

// 系统检测状态
typedef enum {
    SYSTEM_CHECK_OK = 0,
    SYSTEM_CHECK_ERROR,
    SYSTEM_CHECK_NOT_FOUND
} system_check_status_t;

// Flash信息结构
typedef struct {
    uint32_t flash_id;
    char model_name[32];
    uint32_t capacity_mb;
    system_check_status_t status;
} flash_info_t;

// TF卡信息结构
typedef struct {
    uint32_t capacity_mb;
    uint32_t sector_size;
    uint32_t sector_count;
    system_check_status_t status;
} tf_card_info_t;

// 系统信息结构
typedef struct {
    flash_info_t flash_info;
    tf_card_info_t tf_card_info;
    system_check_status_t rtc_status;
} system_info_t;

// 系统自检结果结构
typedef struct {
    system_info_t system_info;
    bool test_passed;
} system_check_result_t;

// 函数声明
sys_func_status_t system_selftest_execute(system_check_result_t* result);
void system_selftest_print_result(const system_check_result_t* result);
sys_func_status_t system_selftest_check_flash(flash_info_t* flash_info);
sys_func_status_t system_selftest_check_tfcard(tf_card_info_t* tf_info);

#endif // SYSTEM_SELFTEST_H
