#ifndef COMMAND_HANDLER_H
#define COMMAND_HANDLER_H

#include "sys_common.h"

// 命令类型枚举
typedef enum {
    CMD_TYPE_TEST = 0,          // test
    CMD_TYPE_RTC_CONFIG,        // RTC Config
    CMD_TYPE_RTC_NOW,           // RTC now
    CMD_TYPE_CONF,              // conf
    CMD_TYPE_RATIO,             // ratio
    CMD_TYPE_LIMIT,             // limit
    CMD_TYPE_CONFIG_SAVE,       // config save
    CMD_TYPE_CONFIG_READ,       // config read
    CMD_TYPE_START,             // start
    CMD_TYPE_STOP,              // stop
    CMD_TYPE_HIDE,              // hide
    CMD_TYPE_UNHIDE,            // unhide
    CMD_TYPE_UNKNOWN            // 未知命令
} command_type_t;

// 命令状态枚举
typedef enum {
    CMD_STATE_IDLE = 0,
    CMD_STATE_WAIT_RTC,
    CMD_STATE_WAIT_RATIO,
    CMD_STATE_WAIT_LIMIT
} command_state_t;

// 命令处理状态
typedef enum {
    CMD_HANDLER_OK = 0,
    CMD_HANDLER_ERROR,
    CMD_HANDLER_UNKNOWN_CMD,
    CMD_HANDLER_INVALID_PARAM
} cmd_handler_status_t;

// 函数声明
sys_func_status_t command_handler_parse(const char* input, command_type_t* cmd_type);
sys_func_status_t command_handler_execute(command_type_t cmd_type, const char* params);
void command_handler_process_uart_command(uint8_t* buffer, uint16_t length);
void command_handler_process_uart_input(const char* input);

// 状态管理函数
command_state_t command_handler_get_state(void);
void command_handler_set_state(command_state_t state);

// 内部命令处理函数
void command_handler_handle_test(void);
void command_handler_handle_rtc_config(void);
void command_handler_handle_rtc_now(void);
void command_handler_handle_conf(void);
void command_handler_handle_ratio(void);
void command_handler_handle_limit(void);
void command_handler_handle_config_save(void);
void command_handler_handle_config_read(void);
void command_handler_handle_start(void);
void command_handler_handle_stop(void);
void command_handler_handle_hide(void);
void command_handler_handle_unhide(void);

#endif // COMMAND_HANDLER_H
